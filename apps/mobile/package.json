{"name": "mobile", "private": true, "scripts": {"build": "nuxt build --mode test", "build-production": "nuxt build --mode production", "build-test": "nuxt build --mode test", "dev": "nuxt dev --port=3010 --mode dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@vant/nuxt": "^1.0.0", "i": "^0.3.7", "nuxt": "^3.3.1", "postcss-px-to-viewport": "^1.1.1"}, "dependencies": {"nuxt-swiper": "1.0.1", "sass": "^1.70.0", "vant": "^4.1.0", "vite-plugin-ali-oss": "^1.0.8", "ioredis": "^5.5.0"}}