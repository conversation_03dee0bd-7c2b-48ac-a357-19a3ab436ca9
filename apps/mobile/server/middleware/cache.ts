import Redis from 'ioredis';
import { useRuntimeConfig, eventHandler } from '#imports';

// 创建 Redis 实例
const config = useRuntimeConfig();
const redis = new Redis(Number(config.public.VITE_REDIS_PORT || 6379), config.public.VITE_REDIS_HOST, {
  password: config.public.VITE_REDIS_PASSWORD,
  db: Number(config.public.VITE_REDIS_DB || 0),
  connectTimeout: 50000,
  retryStrategy(times) {
    return Math.min(times * 50, 2000);
  },
});


// 生成缓存 Key
const generateCacheKey = (path: string, params: any) => {
  return `${path}?${new URLSearchParams(params).toString()}`;
};


// 使用 eventHandler 来定义中间件
export default eventHandler(async (event) => {
  const { path, query } = event;

  // 判断请求路径是否以 /wp-json/ 开头
  if (path.startsWith('/wp-json/website/pages') || path.startsWith('/wp-json/website/post')) {
    const cacheKey = generateCacheKey(path, query);

    console.log('cacheKey', cacheKey)
    // 尝试从 Redis 获取缓存
    const cachedData = await redis.get(cacheKey);
    if (cachedData) {

      // 如果缓存命中，直接返回缓存内容
      event.res.setHeader('Content-Type', 'application/json');
      return event.res.end(cachedData);
    } else {
      // 如果缓存未命中，进行原始请求
      const response = await fetch(`https://www.sdongpo.com${path}`, { method: event.req.method });
      const data = await response.json();

      // 将响应数据存入 Redis
      redis.set(cacheKey, JSON.stringify(data), 'EX', 60 * 24 * 60 ); // 设置24小时缓存过期时间

      // 返回原始数据
      event.res.setHeader('Content-Type', 'application/json');
      return event.res.end(JSON.stringify(data));
    }
  }
  // 如果是非 /wp-json/ 请求，交给 Nuxt 处理（不返回固定的 JSON）
  return; // 不做任何操作，交给 Nuxt 继续处理
});