/*
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 17:37:14
 * @LastEditors: hgj
 * @LastEditTime: 2023-05-30 18:21:39
 */
export const showMenu = ref(false);
export const menuHandle = () => {
  // 当打开蒙层的时候禁止滚动, 反之
  if (showMenu.value) {
    document.body.style.overflow = 'auto';
  } else {
    document.body.style.overflow = 'hidden';
  }
  showMenu.value = !showMenu.value;
}

const formInitData = {
  username: '',
  phone: '',
  companyName: '',
  user_level: 4,
};
export const formDataObj = reactive({
  ...formInitData,
});
export const resetForm = () => {
  formDataObj.username = formInitData.username;
  formDataObj.phone = formInitData.phone;
  formDataObj.companyName = formInitData.companyName;
  formDataObj.user_level = formInitData.user_level;
};
export const modalTypeList = ref({
  common:'common',
  price:'price'
});
export const currentModalType = ref(modalTypeList.value.common);
export const showModal = ref(false)

export const getBaseUrl = () => {
  const { VITE_API_PREFFIX } = useRuntimeConfig()
  return VITE_API_PREFFIX
}

export const getStudyCenterPages = async (module: String) => {
  const params = {
    module: module,
  }

  // const { data } = await useFetch(getBaseUrl() + '/wp-json/website/pages', { //加上getBaseUrl()会请求失败，在本地
  // VITE_API_PREFFIX='https://www.sdongpo.com'
  const { data } = await useFetch('/wp-json/website/pages', {
    params
  });
  return unref(data)
}

export const getNewsDetail = async (id: any) => {
  const { data } = await 
    useFetch("/wp-json/website/post", {
      params: {
        id,
      },
    })
  return unref(data);
};

export function openModal() {
  showModal.value = true
}
export function closeModal() {
  resetForm();
  showModal.value = false;
}
export const phonePattern = /^1[0-9]{10}$/;
export async function saveFormData(params: any = {}) {
  const route = useRoute();
  // 取最后一级路由, 例如/asb/sss/   处理成/sss/
  params.path = route.path.replace(/.*\/([^\/]*)\/$/, '/$1/');
  const res: any = await useFetch('https://m.sdongpo.com/wap/save', {
    method: 'POST',
    server: false,
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
    query: {...params},
  });
  return JSON.parse(res.data.value);
}

// 活动表单新弹窗, 后续可能会改成通用弹窗
export class datumForm {
  static dialog = ref(false)
  static noticeDialog = ref(false)
  static form = reactive({
    username: '',
    phone: '',
    companyName: '',
    user_level: 4,
  })
  static dataGramList = ref([])
  static activeGram = ref(null)
  static activeGramDetail = computed(() => {
    return this.dataGramList.value.find((item: any) => item.post_title === this.activeGram.value)
  })
  // 打开弹窗
  static showDialog = async () => {
    this.dialog.value = true
  }
  // 关闭弹窗
  static closeDialog() {
    this.dialog.value = false
  }
  static resetForm(){
    this.form.username = ''
    this.form.phone = ''
    this.form.companyName = ''
    this.form.user_level = 4
  }
  static async getDataGram() {
    const res = await getStudyCenterPages('资料包')
    if (res) {
      this.dataGramList.value = res.data.list || []
      if (this.dataGramList.value.length === 0) return
      this.activeGram.value = this.dataGramList.value[0].post_title
    }
  }
  static async saveFormData(params: any = {}) {
    const baseUrl = 'https://m.sdongpo.com/wap/save'
    // const baseUrl = '/wap/save/'

    const route = useRoute();
    // 取最后一级路由, 例如/asb/sss/   处理成/sss/
    params.path = route.path.replace(/.*\/([^\/]*)\/$/, '/$1/');

    // 自定义渠道逻辑, 如果链接里的query带scene参数, 则使用scene参数
    const query = route.query
    const { scene: customScene } = query || {}

    params.scene = `${ customScene ? '-' + customScene : '' }${ this.activeGram.value ? '-' + this.activeGram.value : '' }`

    // 验证姓名不能为空,手机号要是11位数
    if (!this.form.username) {
      showNotify({ type: 'danger', message: '姓名不能为空!' });
      return Promise.reject()
    }
    if (!phonePattern.test(this.form.phone)) {
      showNotify({ type: 'danger', message: '手机号不符合规范!' });
      return Promise.reject()
    }
    const res: any = await useFetch(baseUrl, {
      method: 'POST',
      server: false,
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      query: {
        ...this.form,
        ...params
      },
    });
    return JSON.parse(res.data.value);
  }
}
