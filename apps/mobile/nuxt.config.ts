/*
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 10:32:19
 * @LastEditors: hgj
 * @LastEditTime: 2023-05-31 15:27:49
 */
import vitePluginAliOss from './vite-plugins/vite-plugin-ali-oss'
import { loadEnv } from 'vite';
const prod = process.env.NODE_ENV === 'production'
const options = {
  region: 'oss-cn-beijing',
  accessKeyId: 'LTAI5tBm9T661p8qCL5cAEhM',
  accessKeySecret: '******************************',
  bucket: 'sdongpo-base-file',
  exclude: /\.(html|php|map)$/i
}
const proPlugins = [
  vitePluginAliOss(options)
]
const prodCdnUrl = 'https://base-oss.shudongpoo.com/static/'
const devPlugins = [{}]

interface VITE_ENV_CONFIG {
  // VITE_API_HOST: string
  // VITE_PACK_ENV: string
  VITE_PACK_URL: string
}
const envScript = (process.env as any).npm_lifecycle_script.split(' ')
const envName = envScript[envScript.length - 1]
const envData = loadEnv(envName, 'env') as unknown as VITE_ENV_CONFIG

export default {
 extends: ["../base"],
 modules: ['@vant/nuxt', 'nuxt-swiper'],
 serverMiddleware: [
  { path: '/wp-json/website', handler: '~server/middleware/cache.ts' },
],
 vant: {},
 postcss: {
   plugins: {
     "postcss-px-to-viewport": {
       unitToConvert: "px",
       viewportWidth: 375,
       unitPrecision: 5,
       propList: ["*", "!--van-*"],
       viewportUnit: "vw",
       fontViewportUnit: "vw",
       selectorBlackList: [/^\.vant-.*/],
       minPixelValue: 1,
       mediaQuery: false,
       replace: true,
       exclude: [],
       landscape: false,
       landscapeUnit: "vw",
       landscapeWidth: 568,
     },
   }
 },

 nitro: {
   devProxy: {
     '/wap/save': {
       target: 'http://m.sdp.com/wap/save', // 这里是接口地址
       changeOrigin: true,
       prependPath: true,
     },
   },
 },

 swiper: {
   modules: ['autoplay', 'effect-coverflow'],
 },

 runtimeConfig: {
   public: envData // 把env放入这个里面，通过useRuntimeConfig获取
 },

 app: {
   cdnURL: prod ? prodCdnUrl : ''
 },

 vite: {
   envDir: '~/env',
   plugins:  prod ? proPlugins : devPlugins
 },

 compatibilityDate: '2025-02-25'
};