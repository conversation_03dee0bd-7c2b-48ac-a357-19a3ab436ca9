<script setup lang="ts">
import sxpsBanner from 'assets/shucai/sxps-banner.png'
interface Props {
	config: {
		title: string;
		desc: string;
		btnText: string;
		btnBg: string;
		backImg: any;
	}
	// 是否深色
	dark?: boolean;
	// 是否需要遮罩层
	hasMask?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
	config: () => ({
		title: '',
		desc: '',
		btnText: '获取专属方案',
		btnBg: '#FF9607',
		backImg: ''
	}),
	dark: false
})
const handleShowModal = openModal;
</script>

<template>
	<div class="product-header" :class="{'dark': dark}" :style="{ backgroundImage: `url(${config.backImg || sxpsBanner})` }">
		<div v-if="hasMask" class="shade-mask"></div>
		<h1 v-html="config.title || '生鲜配送系统'"></h1>
		<p v-html="config.desc || '生鲜企业效能提升50%+，让生鲜人每天多睡两小时'"></p>
		<button @click="handleShowModal()" :style="{ 'background-color': config.btnBg || '#FF9607' }">{{ config.btnText || '获取专属方案'}}</button>
	</div>
</template>

<style scoped lang="scss">
.product-header {
	width: 100%;
	height: 180px;
	background-size: 100% auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	position: relative;
	> * {
		position: relative;
		z-index: 1;
	}
	.shade-mask {
		// 从下到上黑色透明度渐变1到0
		width: 100%;
		height: 100%;
		position: absolute;
		z-index: 0;
		background: linear-gradient(0deg, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0) 100%);
	}
	>h1 {
		margin-top: 40px;
		font-size: 22px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		color: #FFFFFF;
		line-height: 32px;
		text-align: center;
	}
	>h3 {
		margin-top: 40px;
		font-size: 22px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 600;
		color: #FFFFFF;
		line-height: 32px;
		text-align: center;
	}
	>p {
		font-size: 12px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		color: rgba(255,255,255, .9);
		line-height: 17px;
		margin-top: 18px;
		text-align: center;
		margin-bottom: 20px;
	}
	>button {
		width: 120px;
		height: 24px;
		border-radius: 4px;
/*		background-color: #FF9607;*/
		font-size: 12px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		color: rgba(255,255,255, .9);
		position: relative;
		z-index: 10;
	}
}
.dark {
	h3 {
		color: #2977FE;
	}
	p {
		color: #2977FE
	}
}
</style>
