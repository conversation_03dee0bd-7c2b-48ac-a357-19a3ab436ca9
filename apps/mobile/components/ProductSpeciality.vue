<script setup lang="ts">
type SpecialityItem = {
	title: string;
	img: any;
	desc: string;
}
type Props = {
	items: Array<SpecialityItem>;
}

const props = withDefaults(defineProps<Props>(), {
	items: () => []
})
</script>

<template>
	<div class="product-speciality">
		<div class="sp-item" v-for="item in items" :key="item.title">
			<img :src="item.img" alt="">
			<div class="sp-text">
				<p class="title">{{ item.title }}</p>
				<p class="desc">{{ item.desc }}</p>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.product-speciality {
	display: flex;
	flex-direction: column;
	gap: 12px;
	.sp-item {
		width: 100%;
		height: 70px;
		background: #FFFFFF;
		border-radius: 5px 5px 5px 5px;
		display: flex;
		align-items: center;
		padding: 9px 8px;
		>img {
			width: 44px;
			margin-right: 15px;
		}
		>.sp-text {
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			height: 100%;
			>.title {
				font-size: 14px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #2977FE;
			}
			>.desc {
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #333333;
			}
		}
	}
}
</style>
