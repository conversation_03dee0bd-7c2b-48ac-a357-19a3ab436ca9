<!--
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 13:53:12
 * @LastEditors: ddcoder zuo<PERSON><PERSON>@movee.cn
 * @LastEditTime: 2023-03-17 17:39:46
-->
<script setup>
  let formData = {};
  const show = showModal
  const form = ref(null);
  const handleChange = data => {
		formData = data;
  };
	
  const handleCloseModal = () => {
    form.value.resetForm();
    closeModal();
  };
  const handleSaveFormData = async () => {
    const validate = form.value.validate();
    if (!validate.valid) {
      showNotify({ type: 'warning', message: validate.error,'z-index' : 9999 });
      return;
    }
    const res = await saveFormData(formData);
    if (res.status === 'success') {
      showNotify({ type: 'success', message: res.message });
      closeModal();
    }
  };
</script>
<template>
  <van-dialog width="92vw" v-model:show="show" :z-index="9999" :cancel="handleCloseModal" :showConfirmButton="false">
    <div class="modal__main">
      <div class="modal__header">
        <img class="logoimg" src="~/assets/common/logo.png" alt="logo" />
        <span class="divider">|</span>
        <span>生鲜SaaS ERP</span>
        <img
          @click="handleCloseModal"
          class="modal__close"
          src="https://website-image.sdongpo.com/website/icon--close.png"
          alt="关闭"
        />
      </div>
      <div class="modal__content form-modal">
      
<!--        <TheForm ref="form" @on-change="handleChange" />-->
        <TheFormBak ref="form" @on-change="handleChange" />
      </div>
      <div class="modal__btn" @click="handleSaveFormData">立即申请</div>
      <div class="modal__tips">如有任何疑问请拨打7*24小时咨询热线：<br/>400-075-1863</div>
    </div>
  </van-dialog>
</template>
<style scoped lang="scss">
.modal__main {
  padding: 8px;
}

.modal__header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #047eef;
  margin-bottom: 27px;
  margin-top: 35px;
  position: relative;
	.modal__close {
		position: absolute;
		width: 20px;
		top: -32px;
		right: 7px;
		cursor: pointer;
	}
	>span {
		font-size: 16px;
	}
}

.logoimg {
    width: 85px;
    height: 30px;
  }
.divider {
  margin: 0 10px;
}

  :deep(.form-item ){
    display: flex;
    flex-direction: row;
    margin-bottom: 20px;
    vertical-align: middle;
  }
  :deep(.form-item__required)  {
    color: #ff3d15;
  }

  :deep(.form-item__label-tips)   {
    font-size: 12px;
    color: #b2b2b2;
  }

  :deep(.form-item__label) {
    font-size: 15px;
    text-align: right;
    padding-top: 10px;
    width: 78px;
  }

  :deep(.form-item__content) {
    padding-left: 16px;
  }

  :deep(.form-item__input) {
    font-size: 15px;
    box-sizing: border-box;
    border: 1px solid #cecece;
    border-radius: 6px;
    outline: 0;
    width: 210px;
    height: 42px;
    line-height: 15px;
    padding: 14px;
  }

  :deep(.employee-num){
    max-width: 240px;
		display: flex;
		grid-gap: 10px;
		flex-wrap: wrap;
		margin-bottom: 10px;
  }
  :deep(.employee-num__item) {
    width: 100px;
    height: 35px;
    font-size: 15px;
    border: 1px solid #cecece;
    border-radius: 6px;
    text-align: center;
    display: flex;
		align-items: center;
		justify-content: center;
    cursor: pointer;
  }

  :deep(.employee-num__item.active){
    color: #fff;
    background-color: #ff3d15;
    border: 1px solid #ff3d15;
  }
  .modal__btn {
    text-align: center;
    height: 35px;
    line-height: 35px;
    background: linear-gradient(90deg, #ff7214, #ff1616);
    border-radius: 6px;
    font-size: 17px;
    color: #fff;
    cursor: pointer;
    margin-top: -10px;
  }
.modal__tips {
  color: #666;
  margin-top: 14px;
  margin-bottom: 10px;
  width: 100%;
  text-align: center;
  font-size: 12px;
}
</style>
