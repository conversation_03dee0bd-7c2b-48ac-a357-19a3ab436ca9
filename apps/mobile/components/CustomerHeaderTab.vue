<template>
  <div class="tab-container">
    <div
      v-for="(tab, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: activeTab === index }"
      @click="handleClick(index,tab)"
    >
      {{ tab }}
      <div v-if="index !== tabs.length - 1 && activeTab !== index" class="tab-divider"></div>
    </div>
  </div>


</template>

<script setup>
import { ref } from "vue";
import { defineEmits,defineExpose } from "vue";

const emit = defineEmits(["tabChange"]);

const tabs = [
  "平台运营系统",
  "大数据监管系统",
  "校园食安智慧监管系统",
  "校园后厨管理系统",
  "家校联动系统",
  "智能硬件接入平台",
];
const handleClick = (index) => {
  activeTab.value = index;
  emit("tabChange", index);
};

const setActiveTab = (index) => {
  if(0 <= index && index <= 3) {
    activeTab.value = 0;
  } else if (index === 4) {
    activeTab.value = 1;
  }  else if (index === 5) {
    activeTab.value = 1;
  } else if (index === 6) {
    activeTab.value = 2;
  }else if (index === 7) {
    activeTab.value = 3;
  }else if (index === 8) {
    activeTab.value = 4;
  }else if (index === 9) {
    activeTab.value = 5;
  }
};

const activeTab = ref(0);
defineExpose({ setActiveTab });
</script>

<style scoped>
.tab-container {
  display: flex;
  background: #f9fbff;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.tab-item:first-child {
    border-top-left-radius: 5px;
}
.tab-item:last-child {
    border-top-right-radius: 5px;
}
.tab-item {
  position: relative;
  padding: 5px 4px;
  cursor: pointer;
  color: #a0a0a0;
  font-size: 10px;
  text-align: center;
  transition: background 0.3s, color 0.3s;
}
.tab-item.active {
  background: #007bff;
  color: #ffffff;
  font-weight: bold;
}
.tab-divider {
  position: absolute;
  right: 0;
  top: 10%;
  height: 80%;
  width: 1px;
  background: linear-gradient(to bottom, #f6f8fe, #bcbcbc, #f6f8fe);
}
</style>
