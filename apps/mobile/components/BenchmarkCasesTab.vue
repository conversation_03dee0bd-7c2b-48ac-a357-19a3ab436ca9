<template>
  <footer class="sidebar" :style="containerStyle">
    <h2 class="common_content__title" style="color: #fff">标杆案例</h2>
    <div class="sidebar_content">
      <van-tabs v-model:active="active" swipeable type="card">
        <van-tab v-for="(item, index) in items" :title="item.title">
          <p>{{ item.desc }}</p>
          <img :src="item.img" :alt="item.title" />
        </van-tab>
      </van-tabs>
    </div>
  </footer>
</template>
<script setup>
const active = ref(0);
const props = defineProps({
  items: Array,
});
const containerStyle = computed(() => {
  return {
    backgroundImage: `url(${props.items[active.value].bg})`,
  };
});
const timer = ref(null);

// 自动轮播, 间隔3s
const autoCarousel = () => {
	const length = props.items.length;
	timer.value =  setInterval(() => {
		active.value = (active.value + 1) % length;
	}, 3000);
};

// 组件销毁时候销毁定时器
onUnmounted(() => {
	clearInterval(timer.value);
})

onMounted(() => {
	autoCarousel();
})
</script>
<style lang="scss" scoped>
.sidebar {
  min-height: 360px;
  padding: 16px 53px 26px 53px;
  background-repeat: no-repeat;
  background-size: cover;
}
.sidebar_content {
  padding: 12px 0px 0px;
}
.sidebar_content p {
  padding: 25px 15px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 18px;
  zoom: 85%;
}

.sidebar_content img {
  display: block;
  //width: 200px;
  height: 80px;
  margin: 0 auto;
  
}
.sidebar_content :deep(.van-tabs__nav--card) {
  margin: 0px;
  height: 20px;
  border-width: 0px;
  color: #ffffff;
  background-color: rgb(255, 255, 255, 0.5)
}
.sidebar_content :deep(.van-tab--card) {
  border-color: rgb(255, 255, 255, 0.5)
}
.sidebar_content :deep(.van-tab__text) {
  font-size: 12px;
  font-weight: 400;
  color: #ffffff;
}
.sidebar_content :deep(.van-tab--active) {
  border-width: 0px;
  background-color: #2977fe;
  font-size: 12px;
  font-weight: 400;
}
</style>
