<template>
	<client-only>
		<div class="common_swipe__box">
			<van-swipe :autoplay="3000"  indicator-color="#2977FE">
				<van-swipe-item ref="swipeTag" v-for="(item, index) in items" :key="index">
					<div class="swipe_container">
						<h2 class="title">{{ item.title }}</h2>
						<ul>
							<li class="desc" v-for="(text, index) in item.desc" :key="index">{{ text }}</li>
						</ul>
						<div class="swipe_img">
							<img :src="item.img" :alt="item.title" />
						</div>
					</div>
				</van-swipe-item>
			</van-swipe>
			<div v-if="hasTry" class="btn" @click="handleShowModal">免费试用</div>
		</div>
	</client-only>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'

interface Props {
	items: Array<any>
	hasTry: Boolean
}
const props = withDefaults(defineProps<Props>(), {
	items: [],
	hasTry: true
})
const handleShowModal = openModal;

const swipeTag = ref(null)
// const setSwipeItemHeight = ()=>{
//   // 缺陷：不能设置van-swipe组件的lazy-render懒加载
//   let heightArr = swipeTag.value.map((item,index)=>{
//     return item.$el.clientHeight || 0
//   })
//   let MaxHeight = Math.max(...heightArr)
//   for (const item of swipeTag.value) {
//     item.$el.style.height = MaxHeight + 'px'
//   }
// }
// onMounted(()=>{
//   setSwipeItemHeight()
// })
</script>
<style lang="scss" scoped>
.common_swipe__box {
  position: relative;
  padding: 16px 12px 0px 12px;
	background: #F9FBFF;
	box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
	border-radius: 5px 5px 5px 5px;
}
.common_swipe__box .swipe_container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
	padding-bottom: 40px;
}
:deep(.van-swipe){
  background-color: #f9fbff;
}
.common_swipe__box .title {
  padding-bottom: 11px;
	font-size: 16px;
	font-family: PingFang SC, PingFang SC;
	font-weight: 500;
	color: #333333;
	line-height: 19px;
}
.common_swipe__box .desc {
  position: relative;
  padding: 4px 0px 4px 7px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 17px;
  zoom: 98%;
}
.common_swipe__box .desc::before {
  content: '';
  display: block;
  width: 3px;
  height: 3px;
  background: #2977fe;
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 11px;
}
.common_swipe__box .swipe_img {
  flex: 1;
  padding: 14px 5px 10px;
  display: flex;
  align-items: center;
	margin-top: 16px;
}
.common_swipe__box .swipe_img > img {
  max-width: 260px;
	//width: auto;
  display: block;
  margin: 0 auto;
}
.btn {
  position: absolute;
	bottom: 34px;
  left: 50%;
  transform: translateX(-50%);
  display: block;
  text-align: center;
  width: 98px;
  height: 27px;
  line-height: 27px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #fff;
  background: #2977fe;
  padding: 5 25px;
  border-radius: 5px 5px 5px 5px;
  cursor: pointer;
  z-index: 1;
}
:deep(.van-swipe__indicator){
  border:1px solid #2977fe;
}
:deep(.van-swipe__indicators) {
	z-index: 999;
	position: relative;
	display: flex;
	justify-content: center;
	margin: 0 auto;
	margin-top: 26px;
}
</style>
