<script setup lang="ts">
const props = defineProps({
	items: Array,
});
</script>

<template>
	<div class="home-case-swipe">
		<Swiper
			width="500"
			slidesOffsetBefore="-35"
			effect="coverflow"
			:modules="[SwiperEffectCoverflow, SwiperAutoplay]"
			:centered-slides="true"
			:initial-slide="0"
			:loop="true"
			:slides-per-view="2"
			:slide-to-clicked-slide="true"
			:coverflow-effect="{
        rotate: 0,
        stretch: -15, // 30
        depth: 70,
        modifier: 3,
        slideShadows: false,
      }"
			:autoplay="{
        delay: 30000,
        stopOnLastSlide: false,
        disableOnInteraction: true,
      }"
		>
			<swiper-slide v-for="item in items" :key="item.title">
				<div class="case-item">
					<img :src="item.img" alt="">
					<h3>{{ item.title }}</h3>
					<div class="desc">
						{{ item.desc }}
					</div>
					<img :style="{height: item.title.includes('拼食材')? '20px' : '28px'}" v-if="item.icon" class="icon" :src="item.icon" alt="">
					<span v-if="item.iconText" class="icon">{{ item.iconText }}</span>
				</div>
			</swiper-slide>
		</Swiper>
	</div>
</template>

<style scoped lang="scss">
.home-case-swipe {
	.case-item {
		width: 100%;
		height: 320px;
		background-color: white;
		text-align: center;
		box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
		border-radius: 5px 5px 5px 5px;
		overflow: hidden;
		position: relative;
		>img {
			width: 100%;
		}
		>h3 {
			font-size: 12px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #333333;
			margin-top: 6px;
		}
		.desc {
			text-align: left;
			font-size: 10px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #333333;
			padding: 10px;
		}
		.icon {
			color: #3388FF;
			position: absolute;
			height: 26px;
			width: auto;
			bottom: 20px;
			right: 20px;
		}
	}
}
</style>
