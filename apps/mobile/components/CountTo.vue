<template>
  <span>{{ current }}</span>
</template>
<script setup>
import { ref } from 'vue';
const props = defineProps({
  start: {
    type: Number,
    required: false,
    default: 0,
  },
  end: {
    type: Number,
    required: false,
    default: 0,
  },
  duration: {
    type: Number,
    default: 0,
  },
  isRolling: {
    type: Boolean,
    default: false,
  },
});
watch(
  () => props.isRolling,
  () => {
    if (props.isRolling) {
      startCount();
    }
  },
);

let start = props.start;
const end = props.end;
let current = ref('0');

const startCount = function () {
  let step = parseInt((end * 100) / (props.duration * 1000) + '');
  let timer = setInterval(() => {
    start += step;
    if (start > end) {
      clearInterval(timer);
      start = end;
      timer = null;
    }
    if (start === 0) {
      start = end;
      clearInterval(timer);
    }
    current.value = start.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,');
  }, props.duration * 100);
};
</script>
