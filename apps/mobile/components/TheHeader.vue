<script setup>
import {menuHandle, showMenu} from '~/composables';
import menu1 from '~/assets/layout/menu-1.png';
import menu2 from '~/assets/layout/menu-2.png';
import menu3 from '~/assets/layout/menu-3.png';
import menu4 from '~/assets/layout/menu-4.png';
import menu5 from '~/assets/layout/menu-5.png';
import menu6 from '~/assets/layout/menu-6.png';
import menu7 from '~/assets/layout/menu-7.png';
import menu8 from '~/assets/layout/menu-8.png';
import menu9 from '~/assets/layout/menu-9.png';

const router = useRouter();
const menuList = [
	{
		name: '首页',
		icon: menu1,
		path: '/'
	},
	{
		name: '产品介绍',
		icon: menu2,
		children: [
			{
				name: '生鲜配送系统',
				path: '/shucai'
			},
			{
				name: '食材阳光智慧采购平台',
				path: '/scygzhcgpt'
			},
			{
				name: '中央厨房系统',
				path: '/processing'
			},
			{
				name: '社区团购系统',
				path :'/groupbuy'
			},
			{
				name: '业财一体化',
				path: '/products/business'
			},
			{
				name: '业务定制平台 PaaS',
				path: '/products/paas'
			},
			{
				name: '开放平台OpenAPI',
				path: '/products/api'
			},
			{
				name: '智能硬件',
				path: '/products/hardware/#smart'
			},
		]
	},
	{
		name: '解决方案',
		icon: menu3,
		children: [
			{
				name: '企事业单位食堂配送',
				path: '/qsy'
			},
			{
				name: '餐饮酒店配送',
				path: '/p2'
			},
			{
				name: '团餐团膳食堂承包供应链',
				path: '/tuanshan'
			},
			{
				name: '高校食堂供应链管理',
				path: '/gxst'
			},
			{
				name: '学生营养餐中央厨房',
				path: '/xsyyc'
			},
			{
				name: '肉类加工与分割',
				path: '/roulei'
			},
			{
				name: '净菜加工',
				path: '/jcjg'
			},
			{
				name: '农产品溯源',
				path: '/solutions/suyuan'
			},
		]
	},
	{
		name: '客户案例',
		icon: menu4,
		path: '/movee'
	},
	// {
	// 	name: '行业会议',
	// 	icon: menu5
	// },
	// {
	// 	name: '生鲜课堂',
	// 	icon: menu6
	// },
	// {
	// 	name: '央厨课堂',
	// 	icon: menu7
	// },
	// {
	// 	name: '8大服务中心',
	// 	icon: menu8
	// },
]
const expandMenu = ref('产品介绍')
const expandHandle = (name) => {
	if (expandMenu.value === name) {
		expandMenu.value = '';
	} else {
		expandMenu.value = name;
	}
}
const skipPage = (path) => {
	if (!path) {
		showToast('暂无跳转路径');
		return;
	}
	menuHandle();
	router.push(path).then(() => {
    window.location.reload();
  });
}
const handleShowModal = openModal;
</script>
<template>
  <header class="the-header">
    <NuxtLink to="/">
      <img class="header-logo" src="../assets/layout/logo.png" alt="">
    </NuxtLink>
    <img @click="menuHandle()" class="header-menu" src="../assets/layout/menu.png" alt="">
		
		<div v-if="showMenu" class="header-menu-main is-visible" >
			<div class="menu-item"  v-for="(item, index) in menuList" :key="index">
				<div class="menu-content"  @click="item.children? expandHandle(item.name) : skipPage(item.path)">
					<img :src="item.icon" alt="">
					<p>{{ item.name }}</p>
					<img
						v-show="item.children"
						class="right-arrow"
						:class="{'is-expand': item.name === expandMenu}"
						src="../assets/layout/right-arrow.png"
						alt=""
					>
				</div>
				
				<ul class="child-menu-con" v-if="expandMenu === item.name">
					<li class="child-menu-item" @click="skipPage(childItem.path)" v-for="childItem in item.children" :key="childItem.name">
						{{ childItem.name }}
					</li>
				</ul>
			</div>
			<button @click="handleShowModal()" class="free-try">免费体验</button>
			<p class="phone"><a href="tel:************">************</a></p>
		</div>
  </header>
</template>
<style lang="scss" scoped>
.the-header {
  height: 40px;
  background: #FFFFFF;
  opacity: 1;
  padding: 0 19px 0 12px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	position: sticky;
  top: 0;
  box-shadow: 0 0 0.1rem rgba(0,204,204,.4);
	z-index: 20;
  .header-logo {
    height: 34px;
  }
	.header-menu {
		height: 14px;
	}
	.header-menu-main {
		transform: translateY(-100%);
		transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		z-index: 10;
		width: 200px;
		min-height: 190px;
		background: #FFFFFF;
		position: absolute;
		border-radius: 0 0 5px 5px;
		top: 30px;
		right: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 10px;
		>.menu-item {
			width: 100%;
			>.menu-content {
				width: 100%;
				display: flex;
				align-items: center;
				padding: 10px 20px;
				border-bottom: 1px solid #D8D8D8;;
				position: relative;
				> img {
					width: 17px;
				}
				p {
					font-size: 12px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 14px;
					margin-left: 5px;
				}
				>.right-arrow {
					width: 11px;
					position: absolute;
					right: 20px;
					transition:  all .2s ease-out;
				}
				>.is-expand {
					transform: rotate(90deg);
				}
			}
			@keyframes show-out {
				0% {
					max-height: 0;
				}
				100% {
					max-height: 400px;
				}
			}
			>.child-menu-con {
				padding-left: 42px;
				animation: show-out .2s ease-out forwards;
				>.child-menu-item {
					border-bottom: 1px solid #D8D8D8;;
					padding: 10px 0;
					font-size: 12px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 14px;
				}
			}
		}
		>.free-try {
			margin: 12px 0;
			width: 144px;
			height: 27px;
			background: #2977FE;
			border-radius: 5px 5px 5px 5px;
			font-size: 13px;
			font-family: PingFang SC, PingFang SC;
			color: white;
		}
		>.phone {
			font-size: 12px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #2977FE;
			line-height: 14px;
			margin-bottom: 10px;
			> a {
				color: #2977FE;
			}
		}
	}
	.header-menu-main.is-visible {
		animation: bounce 0.5s ease-out forwards;
	}
	/* 添加弹跳动画 */
	@keyframes bounce {
		0%{
			transform: translateY(-100%);
		}
		40% {
			transform: translateY(0);
		}
		70% {
			transform: translateY(10px);
		}
		100% {
			transform: translateY(0);
		}
	}
}
</style>
