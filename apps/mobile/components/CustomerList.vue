<template>
  <div class="container">
    <slot name="title"></slot>
    <div class="company-list">
    <!-- @click="handleLookMore(item)" -->
    <a class="company-item" v-for="item in props.dataSource" :key="item?.ID" :href="`/artcle/a-${item?.ID}`">
        <img :src="item.conver_image" :alt="item.post_title" class="company-item-img">
        <div class="item-info">
          <p class="name">{{ item.post_title }}</p>
          <p class="introduce"><span class="tip">赋能助力：</span><span class="des">{{ item.cooperation_value }}</span></p>
          <p class="article-detail__line"></p>
          <div class="article-detail__tags">
            <div v-show="item.business_type.length" class="tags_container">
              <span class="tags__item">#{{item.business_type[0]}}</span>
              <span class="tags__item">详情 ></span>
            </div>
          </div>
        </div>
    </a>
  </div>
</div>
</template>
<script setup>
  const router = useRouter()
  const props = defineProps({
    dataSource: {
      type: Array,
      required: true,
    },
  });
  const handleLookMore = (item) => {
    // sessionStorage.setItem('imgUrl',item?.conver_image)
    router.push(`/artcle/a-${item?.ID}`)
  }
</script>
<style scoped lang="scss">
.container {
  padding: 16px;
  .company-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding-top: 0;
    >.company-item {
      border-radius: 5px;
      width: 162px;
      box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
      >img {
        width: 100%;
        height: 103px;
        padding: 8px;
        border-radius: 3px;
      }
      >.item-info {
        padding: 8px;
        >.name {
          font-weight: 600;
          font-size: 10px;
          color: #333333;
        }
        .introduce{
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          max-width: 100%;
          font-weight: 400;
          font-size: 9px;
          margin-top: 6px;
        }
        .tip {
          color: #2977FE;
        }
        .des {
          color: #666666;
        }
        .article-detail__line {
          height: 0.5px;
          margin: 12px 0;
          background: #E6E6E6;
        }
        .tags_container {
          display: flex;
          justify-content: space-between;
          .tags__item {
            color: #057FF1FF;
            font-weight: 400;
            font-size: 9px;
          }
        }
      }
    }
  }
}
</style>