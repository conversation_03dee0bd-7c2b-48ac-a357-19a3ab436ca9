<script setup lang="ts">
type ScenarioItem = {
	title: string;
	// img是一张导入的图片
	img: any
	desc: Array<string> | string;
}
interface Props {
	items: Array<ScenarioItem>,
	special: <PERSON>olean
}
const props = withDefaults(defineProps<Props>(), {
	items: () => [],
	special: false
})
</script>

<template>
	<div class="business-scenario" :class="{'special': special}">
		<div class="scenario-item" v-for="item in props.items" :key="item.title">
			<img :src="item.img" alt="">
			<p class="title">{{ item.title }}</p>
			<template v-if="typeof item.desc === 'string'">
				<p>{{ item.desc }}</p>
			</template>
			<template v-else>
				<p v-for="text in item.desc" :key="text">{{ text }}</p>
			</template>
		</div>
	</div>
</template>

<style scoped lang="scss">
.business-scenario {
	display: flex;
	justify-content: space-between;
	margin-top: 40px;
	.scenario-item {
		width: 146px;
		height: 204px;
		background: #FFFFFF;
		border-radius: 5px 5px 5px 5px;
		opacity: 1;
		text-align: center;
		>img {
			margin-top: -24px;
			width: 54px;
		}
		>p {
			font-size: 10px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			line-height: 14px;
			color: #666666;
		}
		.title {
			font-size: 14px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #2977FE;
			margin-bottom: 10px;
		}
	}
}
.special {
	flex-direction: column;
	gap: 33px;
	.scenario-item {
		width: 100%;
		height: auto;
		background: #FFFFFF;
		border-radius: 5px 5px 5px 5px;
		text-align: left;
		padding: 10px 10px 15px 10px;
		position: relative;
		>p {
			font-size: 11px;
		}
		>img {
			position: absolute;
			margin-top: -34px;
			left: 0;
			width: 54px;
		}
		.title {
			font-size: 14px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 500;
			color: #2977FE;
			margin-left: 54px;
		}
		// 偶数项
		&:nth-child(even) {
			>img {
				right: 0;
				left: auto;
			}
			.title {
				text-align: right;
				margin-right: 54px;
				font-size: 14px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 500;
				color: #2977FE;
			}
		}
	}
}
</style>
