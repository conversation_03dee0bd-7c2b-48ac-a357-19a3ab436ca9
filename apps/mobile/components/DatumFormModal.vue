
<script setup>
import { datumForm } from '~/composables';

const route = useRoute();
const show = datumForm.dialog
const formData = datumForm.form;
const levelList = [
  {
    label: '0-20',
    value: 4
  },
  {
    label: '21-50',
    value: 3
  },
  {
    label: '51-100',
    value: 2
  },
  {
    label: '100以上',
    value: 1
  }
]
const datumList = datumForm.dataGramList
const curDatum = datumForm.activeGram

const handleCloseModal = () => {
  datumForm.resetForm();
  datumForm.closeDialog();
};

const noticeDialog = datumForm.noticeDialog

const submitForm = async () => {
  const res = await datumForm.saveFormData();
  if (res.status === 'success') {
    showNotify({ type: 'success', message: res.message });
    handleCloseModal();
    // 如果有资料包，弹出资料包
    if (datumForm.activeGramDetail.value?.post_content) {
      setTimeout(() => {
        noticeDialog.value = true
      }, 1000)
    }
  } else {
    showNotify({ type: 'danger', message: res.message });
  }
};

</script>
<template>
  <van-dialog
    v-model:show="show"
    :z-index="100"
    width='89.33vw'
    :overlayStyle="{backgroundColor: 'rgba(0,0,0,.2)'}"
    :cancel="handleCloseModal"
    :showConfirmButton="false"
  >
    <div class="modal__main">
      <div class="modal__header">
        <img
          @click="handleCloseModal"
          class="modal__close"
          src="https://website-image.sdongpo.com/website/icon--close.png"
          alt="关闭"
        />
      </div>
      <div class="modal__content form-modal">
        <div class='form-item'>
          <div class='form-item__label'>姓名:</div>
          <div class='form-item__content'>
            <input class='form-item__input' v-model="formData.username" placeholder='请输入姓名' />
          </div>
        </div>
        <div class='form-item'>
          <div class='form-item__label'>公司:</div>
          <div class='form-item__content'>
            <input class='form-item__input' v-model="formData.companyName" placeholder='请输入公司名称' />
          </div>
        </div>
        <div class='form-item'>
          <div class='form-item__label'>联系电话:</div>
          <div class='form-item__content'>
            <input class='form-item__input' v-model="formData.phone" placeholder='请输入联系电话' />
          </div>
        </div>
        
        <div class='form-item' style='align-items: flex-start;margin-top: 20px'>
          <div class='form-item__label'>公司规模:</div>
          <div class='form-item__content' style='padding-left: 10px'>
            <van-radio-group v-model="formData.level" >
              <van-radio
                v-for='item in levelList' :key='item.value'
                :name="item.value"
              >
                <p class='radio-label'>{{ item.label }}</p>
                <template #icon="props">
                  <div class='dot-circle' :class="{'active-dot': props.checked}">
                    <div class='dot'></div>
                  </div>
                </template>
              </van-radio>
            </van-radio-group>
          </div>
        </div>
        <div class='form-item limit-time' style='align-items: flex-start;margin-top: 20px'>
          <div class='form-item__label'>限时资料包:</div>
          <div class='form-item__content' style='padding-left: 10px'>
            <van-radio-group v-model="curDatum" >
              <van-radio
                v-for='item in datumList' :key='item.value'
                :name="item.post_title"
              >
                <p class='radio-label'>{{ item.post_title }}</p>
                <template #icon="props">
                  <div class='dot-circle' :class="{'active-dot': props.checked}">
                    <div class='dot'></div>
                  </div>
                </template>
              </van-radio>
            </van-radio-group>
          </div>
        </div>
      </div>
      <button @click='submitForm' class='sub-btn'>点击提交</button>
    </div>
  </van-dialog>
</template>
<style scoped lang="scss">
.modal__main {
  min-height: 360px;
  padding: 0 21px;
}

.modal__header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #047eef;
  margin-top: 35px;
  position: relative;
}

.modal__header .modal__close {
  position: absolute;
  width: 10px;
  top: -24px;
  right: -9px;
  cursor: pointer;
}

.modal__content .form-item {
  display: flex;
  align-items: center;
  margin: 6px 0;
}

.modal__content .form-item .form-item__label {
  width: 75px;
  font-size: 14px;
  color: #333333;
  flex-shrink: 0;
}

.modal__content .form-item .form-item__content .form-item__input {
  font-size: 15px;
  box-sizing: border-box;
  outline: 0;
  height: 36px;
  background: #FFFFFF;
  border-radius: 5px 5px 5px 5px;
  opacity: 1;
  width: 220px;
  color: #333333;
  border: 1px solid #CCCCCC;
  line-height: 15px;
  padding: 0 10px;
}

.modal__content .form-item .form-item__content .form-item__input::-webkit-input-placeholder {
  color: #999999;
  -webkit-background-clip: text;
  font-weight: 400;
  font-size: 13px;
}

.modal__content .form-item .form-item__content .radio-label {
  font-size: 14px;
  width: 70px;
  color: #333333;
}

.modal__content .limit-time {
  color: whitesmoke;
}

.modal__content .limit-time .radio-label {
  font-size: 14px;
  width: 150px !important;
  color: #333333;
}

.van-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.dot-circle {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #CCCCCC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 3px;
}

.dot-circle .dot {
  width: 6px;
  height: 6px;
  background-color: #cccccc;
  border-radius: 50%;
}

.active-dot {
  border-color: #2977FE;
}

.active-dot .dot {
  background-color: #2977FE;
}

.sub-btn {
  width: 264px;
  height: 34px;
  background: #057FF1;
  border-radius: 30px 30px 30px 30px;
  opacity: 1;
  color: white;
  font-weight: 500;
  font-size: 16px;
  border: none;
  outline: none;
  display: block;
  margin: 26px auto;
}

</style>
