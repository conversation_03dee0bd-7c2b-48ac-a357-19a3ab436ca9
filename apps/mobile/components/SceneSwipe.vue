<template>
  <div class="common_swipe__box">
    <Swiper
      effect="coverflow"
      :modules="[SwiperEffectCoverflow, SwiperAutoplay]"
      :centered-slides="true"
      :initial-slide="1"
      :loop="true"
      :slides-per-view="2"
      :slide-to-clicked-slide="true"
      @slideChange="handelSlideChange"
      :coverflow-effect="{
        rotate: 0,
        stretch: 5, // 30
        depth: 100,
        modifier: 3,
        slideShadows: false,
      }"
      :autoplay="{
        delay: 3000,
        stopOnLastSlide: false,
        disableOnInteraction: true,
      }"
    >
      <SwiperSlide v-for="(item, index) in items" :key="index">
        <div class="swipe_img">
          <img :src="item.img" :alt="item.desc" />
          <p>{{ item.desc }}</p>
        </div>
      </SwiperSlide>
    </Swiper>
  </div>
</template>
<script lang="ts" setup>
const props = defineProps({
  items: Array,
});
const handelSlideChange = val => {};
</script>
<style lang="scss" scoped>
.common_swipe__box {
  padding: 0px 30px;
}
.common_swipe__box .swipe_img {
  position: relative;
}
.common_swipe__box .swipe_img > img {
  width: 154px;
  height: 184px;
}
.common_swipe__box .swipe_img > p {
  display: none;
  width: 148px;
  position: absolute;
  bottom: 25px;
  text-align: center;
  font-size: 11px;
	padding: 0 10px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 17px;
}

.swiper-wrapper {
  height: 180px;
}
:deep(.swiper-slide-active) .swipe_img p{
  display: block;
}

</style>
