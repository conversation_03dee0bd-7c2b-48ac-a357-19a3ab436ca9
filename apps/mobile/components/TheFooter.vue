<!--
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 13:53:12
 * @LastEditors: ddcoder zuo<PERSON><PERSON>@movee.cn
 * @LastEditTime: 2023-03-17 14:30:37
-->
<script setup>
</script>
<template>
  <div class="sdp-footer">
		<div class="navigation-1">
			<NuxtLink class="link-to" to="/">首页</NuxtLink>|
			<NuxtLink class="link-to" to="https://m.sdongpo.com/products/">产品介绍</NuxtLink>|
			<NuxtLink class="link-to" to="/shucai">解决方案</NuxtLink>|
			<NuxtLink class="link-to" to="/movee">客户案例</NuxtLink>
		</div>
		<div class="navigation-2">
<!--			<NuxtLink class="link-to" to="/about">行业会议</NuxtLink>|-->
<!--			<NuxtLink class="link-to" to="/about">生鲜课堂</NuxtLink>|-->
<!--			<NuxtLink class="link-to" to="/about">央厨课堂</NuxtLink>|-->
<!--			<NuxtLink class="link-to" to="/about">8大服务中心</NuxtLink>-->
			<NuxtLink class="link-to" to="https://m.sdongpo.com/sdpcollege/">研究院</NuxtLink>|
			<NuxtLink class="link-to" to="https://m.sdongpo.com/xueyuan/">新闻</NuxtLink>|
			<NuxtLink class="link-to" to="https://m.sdongpo.com/about/">关于我们</NuxtLink>
		</div>
		<p class="brand-text">北京木屋时代科技有限公司</p>
		<p class="brand-text">为企业提供 SAAS + ERP 生鲜供应链系统</p>
    <p class="icp-text">（京ICP备 14021337号-2）</p>
  </div>
</template>
<style lang="scss" scoped>
.sdp-footer {
	height: 150px;
	background-color: #333333;
	display: flex;
	flex-direction: column;
	align-items: center;
	color: white;
	padding: 29px 0;
	.navigation-1, .navigation-2 {
		margin-bottom: 14px;
		display: flex;
		justify-content: center;
		font-size: 10px;
		align-items: center;
		.link-to {
			width: 70px;
			font-size: 10px;
			text-align: center;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #EEEEEE;
			line-height: 12px;
		}
	}
	.brand-text {
		font-size: 8px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		color: #EEEEEE;
		line-height: 11px;
	}
	.icp-text {
		margin-top: 12px;
		font-size: 8px;
		font-family: PingFang SC, PingFang SC;
		font-weight: 400;
		color: rgba(255,255,255, .5);
		line-height: 9px;
	}
}
</style>
