<!--
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 13:53:12
 * @LastEditors: hgj
 * @LastEditTime: 2023-06-06 15:54:33
-->
<template>
  <div class="bgxz">
    <div class="top"></div>
    <div class="banner-box">
      <div class="banner">
        <h1 class="title">行业权威</h1>
        <h1 class="title">联合首发</h1>
        <p class="desc">洞察行业未来，引领生鲜食材供应链行业数字化进程</p>
      </div>
    </div>
    <div class="line"></div>
    <div class="main">
      <div class="block">
        <h2 class="title2">2023年中国生鲜食材供应链行业洞察</h2>
        <p class="desc2">
          <span>已有</span>
          <span class="num">{{ count }}</span>
          <span>家企业下载成功</span>
        </p>
      </div>
    </div>
    <div class="line"></div>
    <div class="footer">
      <div class="block">
        <h2 class="title3">评论（11925条）</h2>
        <div class="scroll-warp">
          <div class="scroll-content">
            <div class="scroll-item" v-for="item in scrollList" :key="item.name">
              <img class="img" :src="item.img" :alt="item.name" />
              <div class="text">
                <p class="name">{{ item.name }}</p>
                <p class="comment">{{ item.comment }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="btn-box">
      <div class="btn" @click="showPopup">限时下载</div>
    </div>
    <van-popup v-model:show="show" position="bottom" :style="{ height: '75%', borderRadius: '20px 20px 0px 0px' }">
      <h3 class="title4">完善信息，立即下载</h3>
      <div class="form-box">
        <div class="form">
          <p class="form_desc">请填写相关信息下载文件</p>
          <van-form ref="formRef" @submit="onSubmit">
            <van-cell-group>
              <p class="label">
                姓名
                <span class="error">*</span>
              </p>
              <van-field
                v-model="username"
                name="username"
                placeholder="请输入姓名"
                :rules="[{ required: true, message: '请输入姓名' }]"
              />
              <p class="label">
                手机号
                <span class="error">*</span>
              </p>
              <van-field v-model="phone" name="phone" placeholder="请输入手机号" :rules="[{ validator: checkPhone }]" />
              <p class="label">
                企业全称
                <span class="error">*</span>
              </p>
              <van-field
                v-model="companyName"
                name="companyName"
                placeholder="请输入企业全称"
                :rules="[{ required: true, message: '请输入企业全称' }]"
              />
            </van-cell-group>
          </van-form>
        </div>
        <div class="btn" style="margin-bottom: 40px" @click="save">点击提交</div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import img1 from '../assets/bgxz/comment1.png';
import img2 from '../assets/bgxz/comment2.png';
import img3 from '../assets/bgxz/comment3.png';
import img4 from '../assets/bgxz/comment4.png';
import img5 from '../assets/bgxz/comment5.png';
import img6 from '../assets/bgxz/comment6.png';
import img7 from '../assets/bgxz/comment7.png';
import img8 from '../assets/bgxz/comment8.png';
import img9 from '../assets/bgxz/comment9.png';
const route = useRoute();
const routeParams = {
  ...route.params,
  ...route.query,
};
definePageMeta({
  layout: 'none',
});
const scrollList = [
  {
    img: img1,
    name: '蔬菜姐（叶子）生鲜供应链',
    comment: '不错！很实用！',
  },
  {
    img: img2,
    name: '生鲜创业课',
    comment: '艾瑞发的，好像是第一次吧',
  },
  {
    img: img3,
    name: '杨大叔生鲜',
    comment: '现在换软件还来得及吗？',
  },
  {
    img: img4,
    name: '鲜绿家生鲜旗舰店',
    comment: '没想到艾瑞也会关注到这个行业',
  },
  {
    img: img5,
    name: '最新报价（寿光基地发全国）',
    comment: '棒！棒！棒！',
  },
  {
    img: img6,
    name: '南京生鲜批发',
    comment: '学习了',
  },
  {
    img: img7,
    name: '长沙顺意生鲜超市',
    comment: '感谢[抱拳][抱拳][抱拳]',
  },
  {
    img: img8,
    name: '禽类冻货批发走量',
    comment: '蔬东坡市占率这么高？看来没买错',
  },
  {
    img: img9,
    name: '老刘生鲜',
    comment: '还有其他的吗，想多学习一下',
  },
  {
    img: img1,
    name: '蔬菜姐（叶子）生鲜供应链',
    comment: '不错！很实用！',
  },
  {
    img: img2,
    name: '生鲜创业课',
    comment: '艾瑞发的，好像是第一次吧',
  },
  {
    img: img3,
    name: '杨大叔生鲜',
    comment: '现在换软件还来得及吗？',
  },
  {
    img: img4,
    name: '鲜绿家生鲜旗舰店',
    comment: '没想到艾瑞也会关注到这个行业',
  },
  {
    img: img5,
    name: '最新报价（寿光基地发全国）',
    comment: '棒！棒！棒！',
  },
  
  
  
];
const show = ref(false);
const formRef = ref(null);
const showPopup = () => {
  show.value = true;
};

const username = ref('');
const phone = ref('');
const companyName = ref('');
const save = () => {
  formRef.value.submit();
};
const checkPhone = val => {
  const check = /^1[0-9]{10}$/.test(val);
  if (!check) {
    return '请输入正确的手机号码';
  }
};
const onSubmit = async values => {
  console.log('values', values);
  const params = {
    ...routeParams,
    userName: values.username,
    phone: values.phone,
    companyName: values.companyName,
  };
  const res = await saveFormData(params);
  console.log('res---', res);
  if (res.status === 'success') {
    showNotify({ type: 'success', message: res.message });
    show.value = false;
  }
};
const count = ref(86)
onMounted(() => {
  let timerId;
  const getRandomDelay = () => {
    return Math.floor(Math.random() * (1000 - 300 + 1)) + 300;
  };

  const incrementCount = () => {
    count.value++;
    if (count.value >= 20000) {
      clearInterval(timerId);
    } else {
      const delay = getRandomDelay();
      timerId = setTimeout(incrementCount, delay);
    }
  };

  const initialDelay = getRandomDelay();
  timerId = setTimeout(incrementCount, initialDelay);
});

</script>
<style lang="scss" scoped>
p,
h1,
h2 {
  margin: 0;
  padding: 0;
}
.top {
  height: 30px;
  width: 100%;
  background: url('../assets/bgxz/top.jpg') no-repeat;
  background-size: 100% 100%;
}
.bgxz {
  font-size: 12px;
  color: #333333;
}
.banner-box {
  height: 302px;
  background: url('../assets/bgxz/banner.jpg') no-repeat;
  background-size: 100% 100%;
}
.banner {
  box-sizing: border-box;
  padding-left: 30px;
  padding-top: 100px;
  padding-bottom: 60px;
}
.title {
  margin: 0px;
  font-size: 24px;
  color: #ffffff;
  font-weight: 500;
  margin-bottom: 25px;
}
.desc {
  font-size: 14px;
  font-weight: 400;
  color: #ffffff;
  margin-top: 5px;
  margin-bottom: 0px;
}
.main {
  padding-bottom: 20px;
}
.title2 {
  font-weight: 500;
  font-size: 18px;
  margin-bottom: 20px;
}
.num {
  color: #2977fe;
  margin: 0 5px;
}
.desc2 {
  text-align: right;
}
.footer {
}
.scroll-warp {
  margin-top: 20px;
  margin-bottom: 38px;
  height: 180px;
  overflow: hidden;
}
.scroll-content {
  height: 360px;
  transform: translate3d(0, 0, 0); /* 硬件加速 */
  animation: move-up 10s linear infinite;
}
.scroll-item {
  margin-bottom: 6px;
  margin-top: 6px;
  display: flex;
}
.img {
  width: 32px;
  height: 32px;
  margin-right: 6px;
}
.text {
  flex: 1;
}
.name {
  color: #666666;
  font-size: 10px;
  font-weight: 300;
}
.title3 {
  font-size: 12px;
  font-weight: 500;
}
.line {
  width: 100%;
  height: 5px;
  background-color: #edf2fa;
}
.block {
  padding: 20px 30px 0px 30px;
  background-color: #ffffff;
}
.btn-box {
  padding-bottom: 40px;
}
@keyframes move-up {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(0, -100%, 0);
  }
}
.title4 {
  margin: 15px auto;
  color: #333333;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  justify-content: center;
}
.form-box {
  font-size: 14px;
  color: #333333;
}
.van-cell-group {
  margin: 0px;
}
.van-cell {
  width: 315px;
  padding-top: 7px;
  background: #ffffff;
  border-radius: 5px;
  border: 1px solid #cccccc;
  margin-bottom: 15px;
}
.van-cell:after {
  border-bottom: none;
}
.van-cell-group:after {
  border: none;
}
.form_desc {
  color: #333333;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 20px;
}
.form {
  margin-left: 30px;
  margin-bottom: 40px;
}
.label {
  font-size: 14px;
  margin-bottom: 10px;
}
.error {
  vertical-align: middle;
  color: #ff1111;
}
.btn {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  width: 264px;
  height: 34px;
  background: #057ff1;
  border-radius: 30px;
}
</style>
