<script setup lang="ts">
import ProductHeader from '~/components/ProductHeader.vue'
import ProductSpeciality from '~/components/ProductSpeciality.vue'
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'
import groupBuyBanner from '~/assets/groupBuy/group-buy-banner.png'
import groupBuyUp1 from '~/assets/groupBuy/group-buy-up-1.png'
import groupBuyUp2 from '~/assets/groupBuy/group-buy-up-2.png'
import groupBuyUp3 from '~/assets/groupBuy/group-buy-up-3.png'
import groupBuyUp4 from '~/assets/groupBuy/group-buy-up-4.png'

import groupBuySpeciality1 from '~/assets/groupBuy/group-buy-speciality-1.png'
import groupBuySpeciality2 from '~/assets/groupBuy/group-buy-speciality-2.png'
import groupBuySpeciality3 from '~/assets/groupBuy/group-buy-speciality-3.png'
import groupBuySpeciality4 from '~/assets/groupBuy/group-buy-speciality-4.png'
import groupBuySpeciality5 from '~/assets/groupBuy/group-buy-speciality-5.png'

import groupBuyProduct1 from '~/assets/groupBuy/group-buy-product-1.png'
import groupBuyProduct2 from '~/assets/groupBuy/group-buy-product-2.png'
import groupBuyProduct3 from '~/assets/groupBuy/group-buy-product-3.png'
import groupBuyProduct4 from '~/assets/groupBuy/group-buy-product-4.png'
import BusinessScenario from '~/components/BusinessScenario.vue'
import groupBuyScenar1 from '~/assets/groupBuy/group-buy-scenar-1.png'
import groupBuyScenar2 from '~/assets/groupBuy/group-buy-scenar-2.png'

useHead({
	title: '社区团购系统',
	meta: [
		{
			name: 'description',
			content: '蔬东坡社区团购管理系统,建立全流程、一体化的社区团购管理模式，助力企业快速抢占团购市场',
		},
		{
			name: 'keywords',
			content: '蔬东坡社区团购管理系统,建立全流程、一体化的社区团购管理模式，助力企业快速抢占团购市场',
		},
	],
});

const headerConfig = {
	title: '社区团购系统',
	backImg: groupBuyBanner,
	desc: '建立全流程、一体化的社区团购管理模式<br/>助力企业快速抢占团购市场',
}
const specialityList = [
	{
		title: '分销裂变',
		desc: '推荐有奖、佣金制度、刺激裂变',
		img: groupBuySpeciality1
	},
	{
		title: '拼团秒杀',
		desc: '吸引大量用户、低成本获客',
		img: groupBuySpeciality2
	},
	{
		title: '智能分拣',
		desc: '减少分拣时间、降低人力成本',
		img: groupBuySpeciality3
	},
	{
		title: '智能仓储',
		desc: '精细化库存管理、自动报损报溢',
		img: groupBuySpeciality4
	},
	{
		title: '智能物流',
		desc: '可视化排揎、智能合理规划路线',
		img: groupBuySpeciality5
	},
]
const productSwipeList = [
	{
		title: '营销活动丰富 天天新玩法',
		desc: ['优惠券：新会员注册即可获得优惠券，破冰购买下单更快', '秒杀商品：商品倒计时抢购，提高用户活跃度和留存力', '群接龙：阶梯拼团，邻居好友一起团，快速起量，打造人气'],
		img: groupBuyProduct1,
	},
	{
		title: '团长运营小程序 快速成团',
		desc: ['会员下单，团长收益立即可见，总部结算，自由提现', '订单到货自动通知，一键取货快捷操作', '团长销售排名随时掌握，经营数据清晰可见，随时掌握团长动态', '团长分级，差异化佣金管理，多劳多得，互利双赢'],
		img: groupBuyProduct2,
	},
	{
		title: '团长运营小程序 快速成团',
		desc: ['会员下单，团长收益立即可见，总部结算，自由提现', '订单到货自动通知，一键取货快捷操作', '团长销售排名随时掌握，经营数据清晰可见，随时掌握团长动态', '团长分级，差异化佣金管理，多劳多得，互利双赢'],
		img: groupBuyProduct3,
	},
	{
		title: '供应链系统管理 收益最大化',
		desc: ['精准化采购管理，自动发布采购任务，减少人工计算', '精细化库房管理，库区库位规划，一秒定位，减少找货时间', '智能化的分拣管理，支持多种分拣模式，商品标签批量打印', '配送线路提前规划，根据团长批量发货，降低物流成本，提高企业效率'],
		img: groupBuyProduct4,
	},
]
const scenarioList = [
	{
		title: '门店模式',
		img: groupBuyScenar1,
		desc: '利用邻里关系和实体店优势，结合线上线下突破区域限制，扩大业务范围'
	},
	{
		title: '生活服务',
		img: groupBuyScenar2,
		desc: '搭建满足社区居民用户生活、家政、旅游等社区业态全场景的电商平台'
	}
]
</script>

<template>
	<div class="group-buy-page">
		<product-header :config="headerConfig"></product-header>
		<div class="profit-enhancement list-box">
			<p class="common_content__title">产品价值</p>
			<div class="enhancement-con">
				<div class="enhancement-item">
					<img :src="groupBuyUp1" alt="">
					<div class="item-text">
						<p>智慧化会员营销</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img :src="groupBuyUp2" alt="">
					<div class="item-text">
						<p>强互动直播卖货</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img :src="groupBuyUp3" alt="">
					<div class="item-text">
						<p>完善的团长体系</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img :src="groupBuyUp4" alt="">
					<div class="item-text">
						<p>供应链管理</p>
					</div>
				</div>
			</div>
		</div>
		<div class="speciality list-box">
			<p class="common_content__title">
				多样化核心功能 玩转社区团购 <br>助力社区团购平台销量倍增
			</p>
			<product-speciality :items="specialityList"></product-speciality>
		</div>
		<div class="product list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productSwipeList"></product-swipe-new>
		</div>
		<div class="scene list-box">
			<p class="common_content__title">社区团购全业态场景</p>
			<business-scenario :items="scenarioList"></business-scenario>
		</div>
	</div>
</template>

<style scoped lang="scss">
.group-buy-page {
	.list-box {
		padding: 10px 30px;
	}
	.list-box:nth-child(odd) {
		background-color: #f6f9fe;
	}
	.enhancement-con {
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		grid-gap: 10px;
		.enhancement-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 14px;
			>img {
				width: 34px;
			}
			>.item-text {
				width: 105px;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
				>.rate {
					font-size: 16px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
				}
			}
		}
	}
	.speciality {
		.common_content__title {
			width: 270px;
		}
		:deep(.sp-item) {
			>img {
				margin-left: 20px;
			}
		}
	}
	:deep(.scenario-item) {
		height: 140px;
		padding: 0 10px;
	}
}
</style>
