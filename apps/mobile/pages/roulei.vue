<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> qin<PERSON><PERSON><EMAIL>
 * @Date: 2023-08-02 10:00:00
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <CommonHeader>
    <template #title>
      <h1>肉类加工与分割解决方案</h1>
    </template>
    <template #desc>
      <p>精细化管控肉类加工企业</p>
      <p>销售、采购、生产加工全流程</p>
    </template>
    <template #img>
      <img class="roulei_header__img" :src="descImg" alt="肉类加工与分割解决方案" />
    </template>
  </CommonHeader>
  <div class="roulei">
    <div class="business_box">
      <p class="common_content__title">灵活的运营模式导致监管巨难</p>
      <div class="business_bg"></div>
    </div>

    <div class="solution_box">
      <p class="common_content__title">解决方案可实现</p>
      <div class="solution_box__content">
        <div class="item" v-for="(item, index) in solutionList" :key="index">
          <img :src="item.icon" :alt="item.title" />
          <p class="item_title">{{ item.title }}</p>
          <p class="item_desc">{{ item.desc }}</p>
        </div>
      </div>
    </div>

    <div class="product_box">
      <p class="common_content__title">产品功能</p>
      <ProductSwipe :items="productList"></ProductSwipe>
    </div>

    <div class="scene_box">
      <p class="common_content__title" style="padding-bottom: 3px">
        蔬东坡肉类分割解决方案
        <br />
        全场景覆盖
      </p>
      <SceneSwipe :items="sceneList"></SceneSwipe>
    </div>
    <BenchmarkCasesTab :items="benchmarkCasesList"></BenchmarkCasesTab>
  </div>
</template>

<script setup>
import descImg from '~/assets/roulei/roulei_header_img_desc.png';
import footerBg from '~/assets/roulei/roulei_footer_bg.png';

import footerImgWHSS from '~/assets/roulei/roulei_footer_img_whss.png';
import footerImgHNXHH from '~/assets/roulei/roulei_footer_img_hnxhh.png';
import footerImgGDZDF from '~/assets/roulei/roulei_footer_img_gdzdf.png';

import contentIConCCL from '~/assets/roulei/roulei_content_icon_ccl.png';
import contentIConJXC from '~/assets/roulei/roulei_content_icon_jxc.png';
import contentIConYLFS from '~/assets/roulei/roulei_content_icon_ylfs.png';
import contentIConZNFJ from '~/assets/roulei/roulei_content_icon_znfj.png';
import contentIConZNPC from '~/assets/roulei/roulei_content_icon_znpc.png';
import contentIConZXSC from '~/assets/roulei/roulei_content_icon_zxsc.png';

import contentSwipeCGGL from '~/assets/roulei/roulei_content_swipe_cggl.png';
import contentSwipeDDGL from '~/assets/roulei/roulei_content_swipe_ddgl.png';
import contentSwipeFGGL from '~/assets/roulei/roulei_content_swipe_fggl.png';
import contentSwipeFJGL from '~/assets/roulei/roulei_content_swipe_fjgl.png';
import contentSwipeKCGL from '~/assets/roulei/roulei_content_swipe_kcgl.png';
import contentSwipePSGL from '~/assets/roulei/roulei_content_swipe_psgl.png';

import contentImgNYTZ from '~/assets/roulei/roulei_content_img_nytz.png';
import contentImgQLTZ from '~/assets/roulei/roulei_content_img_qltz.png';
import contentImgSZTZ from '~/assets/roulei/roulei_content_img_sztz.png';

useHead({
	title: '肉类分割解决方案_肉类分割管理系统_猪肉分割配送系统-蔬东坡',
	meta: [
		{
			name: 'description',
			content: '精细化管控肉类加工企业销售，采购，生产加工全流程，让产品成品即时可得，适配猪牛羊、禽类等行业',
		},
		{ name: 'keywords', content: '肉类分割解决方案,肉类供应链管理,肉类分割管理软件,肉类分割配送系统' },
	],
});

const solutionList = [
  {
    title: '在线商城',
    desc: '在线下单，省时省力',
    icon: contentIConZXSC,
  },
  {
    title: '进销存',
    desc: '联通上下游，高效协同',
    icon: contentIConJXC,
  },
  {
    title: '原料反算',
    desc: 'BOM管理，MRP原料反算',
    icon: contentIConYLFS,
  },
  {
    title: '智能排产',
    desc: '系统一键排产，过程监控',
    icon: contentIConZNPC,
  },
  {
    title: '智能分拣',
    desc: '分拣进度可视化',
    icon: contentIConZNFJ,
  },
  {
    title: '出成率',
    desc: '实时出成率，数据管控',
    icon: contentIConCCL,
  },
];
const productList = [
  {
    title: '订单管理',
    desc: ['在线商城下单，快速复制订单', '后台全键盘急速录单，更高效', '智能文字识别，快速输入'],
    img: contentSwipeDDGL,
  },
  {
    title: '采购管理',
    desc: ['自动汇总采购需求，一键生成采购计划', '自动分配供应商，支持一品多商', '采购过程监管，杜绝贪腐'],
    img: contentSwipeCGGL,
  },
  {
    title: '分割管理',
    desc: ['结合肉类行业轨道秤，数据自动传输', '收货领料一体化，效率更高', '分割完工数据回收，结果更清晰'],
    img: contentSwipeFGGL,
  },
  {
    title: '分拣管理',
    desc: ['智能硬件自动称重上传', '支持播种分拣、摘果分拣', '分拣进度实时掌控'],
    img: contentSwipeFJGL,
  },
  {
    title: '库存管理',
    desc: [
      '多库房管理，原材料、成品、耗材区分管理',
      '统一商品名称、规格，库存更清晰',
      '精确库存管理，减少呆滞料，提升周转率',
    ],
    img: contentSwipeKCGL,
  },
  {
    title: '配送管理',
    desc: ['固定、半固定线路一键分配', '可视化地图排线，效率更高', '扫码装车，拒绝错漏货'],
    img: contentSwipePSGL,
  },
];
const benchmarkCasesList = [
  {
    title: '武汉三盛',
    desc: '武汉三盛后勤服务管理有限公司是一家专门从事肉类分割的企业。目前三盛长期稳定合作客户300多家，年流水近一个亿。随着业务量增大，手动统计数据错误率高、效率低、整体数据的准确率不断降低。与蔬东坡合作后，业务处理速度得到了极大提升，准确率达到100%',
    img: footerImgWHSS,
    bg: footerBg,
  },
  {
    title: '湖南新辉煌',
    desc: '湖南省新辉煌生鲜配送有限公司成立于2015年，2020年销售总额达到数亿；目前客户多达到500余家，线上线下均有销售渠道。主要面向电商平台、大型连锁餐饮、商超等提供配送服务。与蔬东坡合作后，精准分析销量数据，预测备货量，损耗下降5%；只能加工管理，直接获取加工、分割记录；明确加工流程、任务分配到人，加工人员整体效率提升30%',
    img: footerImgHNXHH,
    bg: footerBg,
  },
  {
    title: '广东猪多福',
    desc: '广东省猪多福食品有限公司成立于2020年，主要做猪肉分割和配送业务。主要面向商超、社区生鲜店、食堂、酒店等客户提供配送服务。与蔬东坡合作后，通过智能预采精准生成加工任务，以销定采；条形溯源码，一物一码；智能加工管理，取消人工记录，自动核算出成率；自动统计各环节损耗，报表随时查',
    img: footerImgGDZDF,
    bg: footerBg,
  },
];

const sceneList = [
  {
    img: contentImgNYTZ,
    desc: '牛羊屠宰分割管理方案',
  },
  {
    img: contentImgSZTZ,
    desc: '生猪屠宰分割管理方案',
  },
  {
    img: contentImgQLTZ,
    desc: '禽类屠宰分割管理方案',
  },
  {
    img: contentImgNYTZ,
    desc: '牛羊屠宰分割管理方案',
  },
  {
    img: contentImgSZTZ,
    desc: '生猪屠宰分割管理方案',
  },
  {
    img: contentImgQLTZ,
    desc: '禽类屠宰分割管理方案',
  },
];
</script>
<style lang="scss" scoped>
.roulei {
	.sidebar {
		min-height: 440px;
	}
}
.business_box {
  padding: 30px 0px 0px;
}
.business_box .business_bg {
  width: auto;
  height: 330px;
  background: url('../assets/roulei/roulei_content_img_info.png') no-repeat;
  background-size: cover;
}
.solution_box {
  padding: 16px 0px 0px;
  background-color: #f9fbff;
}
.solution_box .solution_box__content {
  padding: 16px 20px 0px 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.solution_box__content .item {
  flex: 0 0 32%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 20px;
}
.solution_box__content .item > img {
  width: 40px;
  height: 40px;
  padding-bottom: 4px;
}

.solution_box__content .item .item_title {
  padding-bottom: 3px;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 14px;
}
.solution_box__content .item .item_desc {
  text-align: center;
  width: 100%;
  font-size: 18px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 22px;
  zoom: 48%;
}

.product_box {
  padding: 16px 0px 0px;
}
.product_box > p {
  margin-bottom: 17px;
}
.scene_box {
  margin-top: 24px;
  padding: 24px 0px 24px;
  background-color: #f9fbff;
}
.scene_box > p {
  margin-bottom: 17px;
}
:deep(.sidebar){
  min-height: 350px;
}
</style>
