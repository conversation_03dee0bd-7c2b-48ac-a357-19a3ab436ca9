<script setup>
import p2Product1 from '~/assets/p2/p2-product-1.png';
import p2Product2 from '~/assets/p2/p2-product-2.png';
import p2Product3 from '~/assets/p2/p2-product-3.png';
import p2Product4 from '~/assets/p2/p2-product-4.png';
import p2Product5 from '~/assets/p2/p2-product-5.png';
import p2Product6 from '~/assets/p2/p2-product-6.png';
import p2CaseBg from '~/assets/p2/p2-case-bg.png';
import p2Case1 from '~/assets/p2/p2-case-1.png';
import p2Case2 from '~/assets/p2/p2-case-2.png';
import p2Banner from '~/assets/p2/p2-banner.png';

import CommonHeader from '~/components/CommonHeader.vue';

useHead({
	title: '餐饮酒店配送解决方案_餐饮配送管理系统_餐饮酒店配送管理-蔬东坡',
	meta: [
		{ name: 'description', content: '为餐饮酒店配送行业提供一站式生鲜配送系统解决方案，助力餐饮酒店配送企业降本增效' },
		{ name: 'keywords', content: '餐饮配送软件,餐饮管理系统,餐饮配送管理系统,餐饮食品配送企业,餐饮酒店配送解决方案' },
	],
});

const productList = [
	{
		title: '微信商城下单，收单效率提升100%',
		desc: [
			'告别传统纸质下单，收单效率提升100%，获客方式更高效',
			'微信小程序+公众号h5商城+支付宝商城全渠道覆盖',
			'多种在线支付方式，大幅度盘活生鲜配送企业现金流',
			'营销拓客CRM，精细化客户标签，掌握客户消费偏好'
		],
		img: p2Product1,
	},
	{
		title: '智能分拣称重，分拣0差错',
		desc: [
			'一键称重自动打签，数据直传系统后台',
			'随时查看分拣进度，杜绝错分漏分，分拣0差错',
			'平均每千个包裹降低至少50斤菜品损耗',
			'分拣包裹贴实重二维码标签，大幅度提升现场交货速度'
		],
		img: p2Product2,
	},
	{
		title: '可视化物流排线，单人半小时，完成2000家客户物流排线',
		desc: [
			'可结合车辆运力和客户地址批量排线，提升运力和配送效率',
			'配送企业和客户方可查看司机实时位置，配送轨迹'
		],
		img: p2Product3,
	},
	{
		title: '数据化采购管控，采购成本立省5%',
		desc: [
			'根据客户下单明细，实时汇总生成采购需求',
			'供应商在线询价报价，智能比价，采购成本'
			],
		img: p2Product4
	},
	{
		title: '自动对账，一键结算，成本利润当天出',
		desc: [
			'可先款后货，多退少补 应收应付款自动生成',
			'单品成本利润，单客户成本利润当天可看，大幅度提升企业经营效率'
		],
		img: p2Product5,
	},
	{
		title: '多维损耗报表，助力配送企业降低损耗20%',
		desc: [
			'采购损耗、库房损耗、配送损耗实时掌控',
			'各项损耗趋势比重多维度清晰呈现',
			'助力配送企业精细化管控损耗，降低菜品损耗20%'
		],
		img: p2Product6,
	},
];
const benchmarkCasesList = [
	{
		title: '菜无忧',
		desc: '菜无忧，成立于2021年，与蔬东坡合作后，为其制定中小餐饮配送解决方案，现已为菜无忧搭建了信息化运营管理体系与先进的生鲜分拣中心，成功打造垂直供应链体系，在提升运转效率的同时也提高了企业的核心竞争力。',
		img: p2Case1,
		bg: p2CaseBg,
	},
	{
		title: '绿鲜达',
		desc: '云南生鲜食材配送龙头企业，2023年预计营收突破5个亿。与蔬东坡合作后，搭建了“互联网+农产品”模式下的生鲜电商平台，采取“基地+农户+公司”的方式，减少了农产品流通渠道，凭借自建仓储、物流、配送、创新升级农产品的供应链，实现了企业营收的跳跃增长。',
		img: p2Case2,
		bg: p2CaseBg,
	}
];

const handleShowModal = openModal;
</script>
<template>
	<CommonHeader>
		<template #title>
			<h1>餐饮酒店配送解决方案</h1>
		</template>
		<template #desc>
			<p>助力餐饮酒店配送企业降本增效</p>
			<p>降低经营风险、减少环节损耗</p>
		</template>
		<template #img>
			<img class="tuancan_header__img" :src="p2Banner" alt="团餐团膳食堂承包供应链解决方案" />
		</template>
	</CommonHeader>
	<div class="p2">
		<div class="business_box">
			<img style="width: 100%" src="../assets/p2/p2-pain.png" alt="">
		</div>
		
		<div class="solution_box">
			<p class="common_content__title">企事业单位食堂配送方案全流程</p>
			<div class="solution_box__content">
				<div class="solution_bg"></div>
			</div>
		</div>
		
		<div class="product_box">
			<p class="common_content__title">产品功能</p>
			<ProductSwipe :items="productList"></ProductSwipe>
		</div>
		
		<div class="client-value list-box">
			<p class="common_content__title">
				以客户价值为核心的全生<br/>命周期服务
			</p>
			<div class="client-con">
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-1.png" alt="">
					<p>企业级定制</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-2.png" alt="">
					<p>7x24小时服务</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-3.png" alt="">
					<p>1v1系统顾问</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-4.png" alt="">
					<p>一线实施专家</p>
				</div>
			</div>
			<button @click="handleShowModal">免费试用</button>
		</div>
		<BenchmarkCasesTab :items="benchmarkCasesList"></BenchmarkCasesTab>
	</div>
</template>

<style lang="scss" scoped>
.business_box {
	padding: 30px 0px 0px;
}
.business_box .business_bg {
	width: auto;
	height: 330px;
	background: url('../assets/tuancan/tuancan_content_img_info.png') no-repeat;
	background-size: cover;
}
.solution_box {
	padding: 20px 0px 0px;
	background-color: #f9fbff;
}
.solution_box .solution_box__content {
	padding: 16px 35px 30px 35px;
}
.solution_box .solution_box__content .solution_bg {
	height: 175px;
	background: url('../assets/p2/p2-flow.png') no-repeat;
	background-size: 100% 100%;
}
.product_box {
	padding: 16px 0px 24px;
}
.product_box > p {
	margin-bottom: 17px;
}
:deep(.sidebar){
	min-height: 390px;
}
.client-value {
	.client-con {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-template-rows: repeat(2, 1fr);
		grid-gap: 16px;
		>.client-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 4px 4px 0px rgba(59,105,220,0.05);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 14px;
			font-size: 14px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 16px;
			>img {
				width: 36px;
			}
		}
	}
	>button {
		width: 174px;
		height: 27px;
		border-radius: 5px;
		background-color: #FF9607;
		margin: 0 auto;
		display: block;
		color: white;
		font-size: 12px;
		margin-top: 16px;
	}
}
</style>
