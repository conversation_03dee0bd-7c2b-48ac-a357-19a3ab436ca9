<template>
  <div class='datum-form'>
    <img class='banner-img' src='../assets/datumForm/banner.png' alt=''>
    <main>
      <p class='tips'>左右滑动 更多精彩</p>
      <van-swipe  indicator-color="white">
        <van-swipe-item v-for="(datum, index) in datumList" :key="index">
          <div class="module">
            <h2 class="title">{{ datum.title }}</h2>
            <ul class="list" >
              <li v-for="(item, i) in datum.list" :key="i">
                <img class='datum-icon' src='../assets/datumForm/ziliaoicon.png' alt=''>
                {{ item }}
              </li>
            </ul>
          </div>
        </van-swipe-item>
      </van-swipe>
      <img class='wechat-img' src='../assets/datumForm/weichat.png' alt=''>
    </main>
    <div class='sub-btn-con'>
      <button @click='handleShowModal()' class='get-btn'>点击填写 领取资料</button>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { ref } from 'vue';
import { datumForm } from '~/composables';

definePageMeta({
  layout: 'none',
});
const route = useRoute();

const handleShowModal = datumForm.showDialog;

const datumList = ref([
  {
    title: '1.生鲜配送资料',
    list: [
      "中央厨房运营前期主要工作",
      "生鲜配送企业采购管理制度",
      "生鲜配送企业法务管理制度",
      "生鲜配送企业配送管理制度",
      "生鲜配送企业人事管理制度",
      "生鲜配送企业市场管理制度",
      "生鲜配送企业员工考核制度",
      "蔬菜分拣标准与流程",
      "生鲜配送公司组织架构",
      "部门职责及工作流程、组织架构",
      "生鲜配送企业岗位说明书",
      "生鲜配送企业薪资结构模板",
      "绩效考核",
      "各部门实操工作流程图",
      "生鲜食材运营管理手册",
      "某头部企业生鲜食材运营管理手册"
    ]
  },
  {
    title: '2.中央厨房资料',
    list: [
      "行政组职责划分",
      "物管组职责划分",
      "物流组职责划分",
      "品控员岗位职责",
      "保全组职责划分",
      "物管组职责划分",
      "化验员岗位职责划分",
      "配料组职责划分",
      "采购专员岗位职责划分",
      "收货员岗位职责划分",
      "分装员岗位职责划分",
      "仓管员岗位职责划分",
      "米饭加工岗位职责划分",
      "质检中心职责划分",
      "炒制中心职责划分",
      "原料加工中心职责划分",
      "肉类加工温度要求",
      "大锅原料加工标准",
      "全国常用营养餐菜品主辅料用量标",
      "央厨体系业务流程、指令",
      "盒饭配量表",
      "生产工艺流程图",
      "100道标准家常菜配比表",
      "生鲜采购验收配送标准",
      "净料加工标准",
      "食谱发布流程",
      "筐具交接流程",
      "蔬菜最佳保鲜温湿度要求"
    ]
  },
  {
    title: '3.学生营养餐资料',
    list: [
      "城市餐饮保障新模式",
      "打造标准化学生营养餐央厨",
      "双厨房催生智慧再升级",
      "学生餐多元化发展",
      "政策驱动中国学生营养餐的高质量发展",
      "中央厨房生产运营精细化管理",
      "2024学生餐行业发展调研报告"
    ]
  },
  {
    title: '4.报告&电子书',
    list: [
      "2023年度山东团餐产业发展报告",
      "2023年生鲜配送行业发展报告-艾瑞咨询",
      "2023预制菜产业基地百强研究报告",
      "2023中国餐饮业年度报告",
      "产业大会分享-混合所有制改革：助推团餐3.0时代",
      "打造第四代中央厨房-蔬东坡昆山学生营养餐峰会合集",
      "高校食堂经营阶段管理手册",
      "高校食堂经营阶段管理手册",
      "生鲜配送企业管理从入门到精通",
      "蔬东坡中央厨房运营手册"
    ]
  }
]);

onMounted(async () => {
  // 获取各种参数
  const { auto } = route.query
  setTimeout(() => {
    datumForm.showDialog();
  }, 300)
})

// 在组件挂载后设置滚动
// onMounted(() => {
//   const listElement = document.querySelector('.list') || {}; // 获取列表元素
//   const height = listElement.scrollHeight - listElement.clientHeight;
//   let currentScroll = 0;
//
//   const scrollInterval = setInterval(() => {
//     if (currentScroll < height) {
//       currentScroll += 5; // 每次滚动 5px，可以根据需要调整
//       listElement.scrollTop = currentScroll;
//     } else {
//       listElement.scrollTop = 0;
//       currentScroll = 0;
//     }
//   }, 100); // 每 100 毫秒滚动一次，可以根据需要调整
// });
const preventZoom = (e) => {
  if (e.touches.length > 1) {
    e.preventDefault();
  }
}
onMounted(() => {
  document.addEventListener('touchstart', preventZoom, { passive: false });
})
</script>
<script lang='ts'>
export default {
  ssr: false,
};
</script>

<style scoped lang='postcss'>
.datum-form .banner-img {
  position: relative;
  display: block;
  width: 100%;
  z-index: 0;
}

.datum-form main {
  top: -30px;
  position: relative;
  z-index: 1;
  height: 631px;
  background: #F9FBFF;
  border-radius: 25px 0px 0px 0px;
  opacity: 1;
  padding: 20px;
}

.datum-form .module {
  width: 335px;
  height: 336px;
  background: #FFFFFF;
  box-shadow: 1px 1px 1px 1px rgba(28,102,231,0.05);
  border-radius: 27px 27px 27px 27px;
  opacity: 1;
}

.datum-form .module .title {
  font-size: 14px;
  font-weight: 500;
  height: 32px;
  background: linear-gradient(92deg, #055EF7 0%, rgba(56,159,250,0.2) 100%);
  box-shadow: 1px 1px 1px 1px rgba(28,102,231,0.05);
  border-radius: 25px 25px 0px 0px;
  opacity: 1;
  padding-left: 24px;
  color: white;
  line-height: 32px;
}

.datum-form .module .list {
  padding: 17px 0;
  position: relative;
  height: 260px;
  overflow: auto;
}

.datum-form .module .list li {
  padding-left: 16px;
  font-size: 12px;
  font-weight: 400;
  display: flex;
  align-items: center;
  -webkit-background-clip: text;
  margin-bottom: 5px;
}

.datum-form .module .list .datum-icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.datum-form .wechat-img {
  width: 224px;
  margin: 11px auto;
  display: block;
}

.datum-form .get-btn {
  width: 294px;
  height: 34px;
  background: #057FF1;
  border-radius: 30px 30px 30px 30px;
  opacity: 1;
  color: white;
  font-size: 16px;
  font-weight: 500;
  display: block;
  margin: 27px auto;
  outline: none;
  border: none;
}
.sub-btn-con {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fbff;
}
@keyframes move-up {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-100%);
  }
}
main>.tips {
  color: #285dee;
  text-align: right;
  font-size: 14px;
  margin-bottom: 10px;
  margin-right: 20px;
}
</style>
