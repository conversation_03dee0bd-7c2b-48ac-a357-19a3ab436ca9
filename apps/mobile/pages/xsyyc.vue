<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> qinji<PERSON><EMAIL>
 * @Date: 2023-08-02 10:00:00
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <CommonHeader>
    <template #title>
      <h1>学生营养餐中央厨房解决方案</h1>
    </template>
    <template #desc>
      <p>保障食品安全，降低经营风险</p>
      <p>助力营养餐中央厨房企业降本增效</p>
    </template>
    <template #img>
      <img class="yyc_header__img" :src="descImg" alt="学生营养餐中央厨房解决方案" />
    </template>
  </CommonHeader>
  <div class="yyc">
    <div class="business_box">
      <p class="common_content__title">学生营养餐中央厨房行业痛点</p>
      <div class="business_bg"></div>
    </div>

    <div class="solution_box">
      <p class="common_content__title">解决方案可实现</p>
      <div class="solution_box__content">
        <div class="item" v-for="(item, index) in solutionList" :key="index">
          <p class="item_title">
            {{ item.title }}
            <CountTo class="rolling_count" :start="0" :end="item.num" :isRolling="isRolling" :duration="0.9"></CountTo>
            %
          </p>
        </div>
      </div>
    </div>

    <div class="product_box">
      <p class="common_content__title">产品功能</p>
      <ProductSwipe :items="productList"></ProductSwipe>
    </div>

    <BenchmarkCasesTab :items="benchmarkCasesList"></BenchmarkCasesTab>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import descImg from '~/assets/yyc/yyc_header_img_desc.png';

import footerBg from '~/assets/yyc/yyc_footer_bg.png';
import footerImgHNTC from '~/assets/yyc/yyc_footer_img_hntc.png';
import footerImgJSHCY from '~/assets/yyc/yyc_footer_img_jshcy.png';

import contentSwipeDYHXD from '~/assets/yyc/yyc_content_swipe_dyhxd.png';
import contentSwipeCP from '~/assets/yyc/yyc_content_swipe_cp.png';
import contentSwipeSJHCG from '~/assets/yyc/yyc_content_swipe_sjhcg.png';
import contentSwipeSCQGC from '~/assets/yyc/yyc_content_swipe_scqgc.png';
import contentSwipeKFGL from '~/assets/yyc/yyc_content_swipe_kfgl.png';

import contentSwipeWLPS from '~/assets/yyc/yyc_content_swipe_wlps.png';
import contentSwipeYCDN from '~/assets/yyc/yyc_content_swipe_ycdn.png';

useHead({
	title: '学生营养餐解决方案_学生营养餐配送_中央厨房管理-蔬东坡',
	meta: [
		{ name: 'description', content: '保障食品安全，降低经营风险，助力营养餐中央厨房企业降本增效' },
		{ name: 'keywords', content: '学生营养餐,中央厨房管理系统,学生营养餐管理系统,学生营养餐解决方案' },
	],
});

const isRolling = ref(false);
const initRollingNum = () => {
  if ('IntersectionObserver' in window) {
    // 浏览器支持 IntersectionObserver
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1, // 元素交叉区域达到视口10%时触发回调
    };
    // 定义回调函数
    function callback(entries, observer) {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          isRolling.value = true;
          observer.unobserve(entry.target); // 停止观察该元素
        }
      });
    }
    let observer = new IntersectionObserver(callback, options);
    observer.observe(document.querySelector('.rolling_count'));
  } else {
    // 浏览器不支持 IntersectionObserver
    isRolling.value = true;
  }
};

onMounted(() => {
  initRollingNum();
});


const solutionList = [
  {
    title: '原料浪费降低',
    num: 30,
  },
  {
    title: '各流程人效提升',
    num: 50,
  },
  {
    title: '数据准确率高达',
    num: 90,
  },
  {
    title: '毛利总提升',
    num: 20,
  },
];

const productList = [
  {
    title: '多元化下单方式，订单处理效率提升100%',
    desc: [
      '支持学生家长端在线充值缴费和请假，盘活企业现金流提升就餐体验',
      '支持按周菜谱下单，支持多餐别，多餐标，多就餐类型下单',
      '支持营养餐菜谱，营养成分分析',
      '原料净料成品可商城下单',
    ],
    img: contentSwipeDYHXD,
  },
  {
    title: '菜谱和bom设定标准化，提升菜品口味 稳定性，提前锁定菜品毛利',
    desc: [
      '告别传统靠经验定菜谱方式',
      '根据菜品成本，营养成分，通过央厨系统提前进行bom设定和菜谱设定',
      '菜品的主料，辅料明细系统记录沉淀保证菜品的口味稳定性',
      '通过央厨系统做菜谱，定毛利，锁定利润',
    ],
    img: contentSwipeCP,
  },
  {
    title: '数据化采购管控采购成本立省15%',
    desc: [
      '根据下单情况，基于BOM与库存情况进行MRP反算原料生成采购需求',
      '供应商在线询价报价，智能比价，市场行情随时掌握',
      '采购收货智能称实重，采购供应商监管数据可依',
    ],
    img: contentSwipeSJHCG,
  },
  {
    title: '生产全过程精细化管控加工效率提升20%，加工损耗降低5%',
    desc: [
      '通过需求计划，bom，库存制定生产计划',
      '生产指令通过系统+人工高效拆分，实时同步加工工作台，高效生产协同',
      '系统化统筹领料，完工，退料等生产指令，保障生产工作顺利进行',
      '核心工序完工可手动自动报工，阶段性出品结果有数据记录',
      '生产绩效有据可依，彻底解决产品损耗，出成率问题',
      '实时管控生产进度，生产过程可溯源，责任到人',
    ],
    img: contentSwipeSCQGC,
  },
  {
    title: '库房管理标准化，告别呆滞货品仓储周转率提升20%',
    desc: [
      '商品入库，出库，盘点实时记录，仓库作业过程可溯源',
      '库房周转率大幅度，告别呆滞货品',
      '仓库货品保质期精准管理，临期商品自动预警',
      '所有货品可溯源，助力央厨企业规避食安风险',
    ],
    img: contentSwipeKFGL,
  },
  {
    title: '物流配送全程管控，确保交付安全',
    desc: ['物流过程全程实时跟踪可视，物流位置也可实时同步客户', '确保物流交付安全'],
    img: contentSwipeWLPS,
  },
  {
    title: '央厨大脑 数据中心',
    desc: ['经营报表实时掌握，菜品成本损耗随时可看', '每日营业概况按客户按商品多维度提供分析提升企业经营决策效率'],
    img: contentSwipeYCDN,
  },
];
const benchmarkCasesList = [
  {
    title: '湖南天辰央厨',
    desc: '成立于2014年，主做学生营养餐商务餐，以采购规模化、生产智能化、仓配一体化、工序专业化、餐食营养健康化，实现了日产2万份',
    img: footerImgHNTC,
    bg: footerBg,
  },
  {
    title: '江苏好厨艺',
    desc: '占地面积110亩，一期投资3亿元。团餐单餐达10万份，是目前国内团餐产能规模大、食品品控严、安全卫生标准高的大型食品加工企业之一',
    img: footerImgJSHCY,
    bg: footerBg,
  },
];
</script>
<style lang="scss" scoped>
.business_box {
  padding: 30px 0px 0px;
}
.business_box .business_bg {
  width: auto;
  height: 330px;
  background: url('../assets/yyc/yyc_content_img_info.png') no-repeat;
  background-size: cover;
}
.solution_box {
  padding: 20px 0px 0px;
  background-color: #f9fbff;
}
.solution_box .solution_box__content {
  padding: 16px 20px 14px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.solution_box__content .item {
  flex: 0 0 48%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 7px 0px;
  margin-bottom: 16px;
  box-shadow: 2px 4px 4px 0px rgba(59, 105, 220, 0.05);
  border-radius: 5px 5px 5px 5px;
  background: #2977fe;
}

.solution_box__content .item .item_title {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 14px;
}
.solution_box__content .item .item_title span {
  min-width: 37px;
  margin: 0px 1px 0px 5px;
  font-size: 30px;
  line-height: 35px;
}
.product_box {
  padding: 16px 0px 24px;
}
.product_box > p {
  margin-bottom: 17px;
}
:deep(.sidebar){
  min-height: 300px;
}
</style>
