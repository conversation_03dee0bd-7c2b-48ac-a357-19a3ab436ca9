<script setup lang="ts">
import ProductHeader from '~/components/ProductHeader.vue'
import ProductSpeciality from '~/components/ProductSpeciality.vue'
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'
import sxpsBanner from '../assets/shucai/sxps-banner.png'
import shucaiCJQ from '~/assets/shucai/shucai-cjq.png'
import shucaiGLX from '~/assets/shucai/shucai-glx.png'
import shucaiFWH from '~/assets/shucai/shucai-fwh.png'
import shucaiJSQ from '~/assets/shucai/shucai-jsq.png'
import shucaiCGGL from '~/assets/shucai/shucai-cggl.png'
import shucaiFJCZ from '~/assets/shucai/shucai-fjcz.png'
import shucaiKCGL from '~/assets/shucai/shucai-kcgl.png'
import shucaiGXPX from '~/assets/shucai/shucai-gxpx.png'
import shucaiGXBB from '~/assets/shucai/shucai-gxbb.png'
import SceneSwipe from '~/components/SceneSwipe.vue'
import shucaiGXST from '~/assets/shucai/shucai-gxst.png'
import shucaiTCGYL from '~/assets/shucai/shucai-tcgyl.png'
import shucaiCYJD from '~/assets/shucai/shucai-cyjd.png'

useHead({
	title: '生鲜配送系统_生鲜配送软件_蔬菜配送软件-蔬东坡',
	meta: [
		{
			name: 'description',
			content:
				'蔬东坡生鲜配送系统,专为生鲜配送企业开发的业务管理软件,广泛应用于生鲜配送、蔬菜配送、食材配送、农产品配送等场景,已帮助超10000家生鲜企业实现降本增效',
		},
		{ name: 'keywords', content: '生鲜配送软件,蔬菜配送软件,生鲜配送系统,蔬东坡蔬菜配送软件,蔬菜配送管理系统' },
	],
});

const headerConfig = {
	title: '生鲜配送系统',
	backImg: sxpsBanner,
	desc: '生鲜企业效能提升50%+，让生鲜人每天多睡两小时',
}
const specialityList = [
	{
		title: '场景全',
		desc: '覆盖多种业务形态，全面支持上下游业务对接整合',
		img: shucaiCJQ
	},
	{
		title: '管理细',
		desc: '全流程信息化管控，损耗↓20%，效能↑50%',
		img: shucaiGLX
	},
	{
		title: '服务好',
		desc: '1对1专属客服，8大服务中心，覆盖200+城市',
		img: shucaiFWH
	},
	{
		title: '技术强',
		desc: '99%上线率，年度更新功能1600+，灵活对接第三方软件',
		img: shucaiJSQ
	}
]
const productSwipeList = [
	{
		title: '采购管理，有效降低35%采购成本',
		desc: ['供应商比价：日常询价记录对比，拒绝贪腐，有效降低采购成本', '自动分配采购任务：自动分配，无需人工干预，扫码随时看明细', '采购进度可视化：通过采购APP随时查看任务、追踪采购情况，一目了然'],
		img: shucaiCGGL
	},
	{
		title: '分拣称重0出错，提效60%',
		desc: ['一体化智能称：一键打印分拣标签，同步分拣进度，回传商品实重', '分拣进度可视化：实时监控分拣情况，避免影响发货', '分拣缺货标记：同步库存情况，杜绝错分、漏分', '支持多种分拣方式：支持客户分拣、商品分拣、路线分拣等多种方式，有效优化分拣流程'],
		img: shucaiFJCZ
	},
	{
		title: '库存管理，精准运营',
		desc: ['进销存管理：精细化库区、库位、货架管理，减少90％找货时间', '掌上库房：扫码收货、盘点，效率提升200%', '无纸化库房管理：移动操作，记录不丢失，随时随地查看库房情况'],
		img: shucaiKCGL
	},
	{
		title: '高效排线，半小时搞定2000家',
		desc: ['可视化物流排线：用画图的方式进行排线，可视化且高效', '多种排线方式：支持按商品金额、重量、体积排线，降低空载率', '配送地图：实时查看司机位置'],
		img:shucaiGXPX
	},
	{
		title: '财务报表，清晰明了',
		desc: ['账务数据自动生成：每笔订单均有据可查，流水记录清晰明了', '灵活统计：支持事后改价、抹零等；报表、图表模式任意切换，随时查看经营状况', '全流程损耗统计：实时掌控采购、库房、配送、手动报损等数据，降低至少20%损耗'],
		img: shucaiGXBB
	}
]
const sceneList = [
	{
		desc: '高校食堂食材供应链管理解决方案',
		img: shucaiGXST,
	},
	{
		desc: '团餐供应链解决方案',
		img: shucaiTCGYL,
	},
	{
		desc: '餐饮酒店配送解决方案',
		img: shucaiCYJD,
	},
	{
		desc: '高校食堂食材供应链管理解决方案',
		img: shucaiGXST,
	},
	{
		desc: '团餐供应链解决方案',
		img: shucaiTCGYL,
	},
	{
		desc: '餐饮酒店配送解决方案',
		img: shucaiCYJD,
	}
]
</script>

<template>
	<div class="sxps-page">
		<product-header :config="headerConfig"></product-header>
		<div class="profit-enhancement list-box">
			<p class="common_content__title">3~5% 净利润提升</p>
			<div class="enhancement-con">
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjixiajaing.png" alt="">
					<div class="item-text">
						<p>商品损耗降低</p>
						<p class="rate">20%</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjishangsheng.png" alt="">
					<div class="item-text">
						<p>分拣速度提高</p>
						<p class="rate">60%</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjishangsheng.png" alt="">
					<div class="item-text">
						<p>订单效率提高</p>
						<p class="rate">90%</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjishangsheng.png" alt="">
					<div class="item-text">
						<p>准确率高达</p>
						<p class="rate">99.9%</p>
					</div>
				</div>
			</div>
		</div>
		<div class="speciality list-box">
			<p class="common_content__title">企事业单位食堂配送、餐饮酒店配送、 团餐团膳食堂承包、高校食堂供应链管理都能用！</p>
			<product-speciality :items="specialityList"></product-speciality>
		</div>
		<div class="product list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productSwipeList"></product-swipe-new>
		</div>
		<div class="scene list-box">
			<p class="common_content__title">应用场景</p>
			<scene-swipe :items="sceneList"></scene-swipe>
		</div>
	</div>
</template>

<style scoped lang="scss">
.sxps-page {
	.list-box {
		padding: 10px 30px;
	}
	.list-box:nth-child(odd) {
		background-color: #f6f9fe;
	}
	.enhancement-con {
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		grid-gap: 10px;
		.enhancement-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 14px;
			>img {
				width: 34px;
			}
			>.item-text {
				width: 105px;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
				>.rate {
					font-size: 16px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
				}
			}
		}
	}
	.speciality {
		.common_content__title {
			width: 270px;
		}
	}
}
</style>
