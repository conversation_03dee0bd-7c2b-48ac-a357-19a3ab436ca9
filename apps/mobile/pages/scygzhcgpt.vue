<script setup>
import p2Banner from "~/assets/scygzhcgpt/banner_bg.png";


import ptyyxt from "~/assets/scygzhcgpt/ptyyxt.png";
import zncp from "~/assets/scygzhcgpt/zncp.png";
import gysgl from "~/assets/scygzhcgpt/gysgl.png";
import cwdz from "~/assets/scygzhcgpt/cwdz.png";
import syjg from "~/assets/scygzhcgpt/syjg.png";
import jgdp from "~/assets/scygzhcgpt/jgdp.png";
import znyj from "~/assets/scygzhcgpt/znyj.png";
import jxld from "~/assets/scygzhcgpt/jxld.png";
import xyhc from "~/assets/scygzhcgpt/xyhc.png";
import mczm from "~/assets/scygzhcgpt/mczm.png";

import company_icon4 from "~/assets/scygzhcgpt/company_icon4.png";
import company_icon1 from "~/assets/scygzhcgpt/company_icon1.png";
import company_icon2 from "~/assets/scygzhcgpt/company_icon2.png";
import company_icon3 from "~/assets/scygzhcgpt/company_icon3.png";

useHead({
  title: "食材阳光智慧采购平台—蔬东坡",
  meta: [
    {
      name: "description",
      content:
        "食材阳光智慧采购平台—蔬东坡，多个区域监管平台上线，数字化助力阳光食材采购，构建校园食安与供应链监管新生态，创新构建一个中心、六大运营系统、十大核心亮点功能",
    },
    {
      name: "keywords",
      content:
        "食材阳光智慧采购平台—蔬东坡，政府食材采购监管，校园食材采购安全监管，学校食材集中采购",
    },
  ],
});
const productSwipeList = [
  {
    special_item_title: ["多维度食材价格管控", "让食材采购监管更透明"],
    light_text: [
      "支持平台运营方对食材进行市场询价",
      "供应商在线报价比价",
      "平台供应商报价审核",
      "下浮率限价管理",
    ],
    img: ptyyxt,
  },
  {
    special_item_title: ["智能菜谱下单，减少食材浪费", "保障营养均衡"],
    light_text: [
      "智能菜品库自动生成菜谱",
      "结合营养成分分析，实现学校菜谱智能下单",
      "对原料进行采购量反算",
      "支持选餐制",
    ],
    img: zncp,
  },
  {
    special_item_title: ["财务对账，高效透明"],
    light_text: [
      "供应商、学校、监管方线上三方对账",
      "科学管理膳食经费，支持财务结算审批",
      "与第三方财务系统灵活对接",
    ],
    img: cwdz,
  },
  {
    special_item_title: ["供应商全生命周期管控", "有据可依"],
    light_text: [
      "供应商准入，供应商履约过程监管  ",
      "供应商评价盲选：结合商品品质、送货及时率",
      "缺货率、服务态度等维度对供应商进行评价",
    ],
    img: gysgl,
  },
  {
    special_item_title: ["全链条溯源监管，保障食品安全", "源头可溯、过程可控"],
    light_text: ["索证索票", "多维度溯源监管", "交易数据可视化监管", "供应商准入及过程监管"],
    img: syjg,
  },
  {
    special_item_title: ["监管驾驶舱数据大屏，阳光公示", "数据实时呈现"],
    light_text: ["食安大屏", "溯源大屏", "配送大屏", "阳光交易监管大屏"],
    img: jgdp,
  },
  {
    special_item_title: ["校园食安安全管控后台", "实时预警，食安风险早发现！"],
    light_text: [
      "明厨亮灶系统",
      "AI监管识别",
      "IOT智能化物联设备",
      "校园食堂餐厅数据大屏公示",
    ],
    img: mczm,
  },
  {
    special_item_title: ["学校食堂进销存管理", "电子台账一目了然"],
    light_text: [
      "线上食材比价",
      "学校食堂进销存管理",
      "学校财务管理",
      "线上食材采购",
      "移动端索票索证",
    ],
    img: xyhc,
  },
  {
    special_item_title: ["家校联动系统", "实现让家长“看得见”的安全！"],
    light_text: ["缴费管理", "菜谱公示", "线上请假、报餐", "陪餐管理", "投诉建议及评价"],
    img: jxld,
  },
  {
    special_item_title: ["整合智能化设备", "实现物联网高效监管"],
    light_text: ["智能收货核磅   ", "晨检巡检", "AI识别监管", "配送车载GPS"],
    img: znyj,
  },
];
const eventList = [
	{
    image: company_icon1,
    title: "遵义市红花岗区校园阳光采购平台",
    explainText:
      "遵义市红花岗区教体局为了解决区里中小学食材统一采购问题，搭建了遵义市首个针对122家学校的服务平台",
    points: [
      "通过智能化管理工具解决了各学校订单汇总问题，供应商直供，实现对商品流、物流的全面管理",
      "学校可对每个单品进行采购成本分析、数量汇总，并输出报表",
      "财务上明确了解每个食堂的商品数量以及金额、每个供应商配送商品数量及金额，效率提升至少60%，2-3人即可轻松实现后勤食材保障",
      "教体局随时查看食堂采购价格、数量，查看供应商的服务评分。",
    ],
  },
  {
    image: company_icon2,
    title: " 上海市崇明县教育局阳光采购平台 ",
    explainText:
      "上海市崇明县教育局是上海市崇明县人民政府主管全县教育工作的职能部门，解决区里100多所中小学统一学校下单问题，教育局监管订单和采购，减少规避出错，通过软件加强管理管控",
    points: [
      "教育局统一制定菜谱，响应教育部要求的食堂选餐制",
      "对供应商、食堂行为进行统一管控，平台通过线上询价、竞价，增强学校管控力",
      "方便供应商自行管理商品，直接生成账单，与档口/商户直接对账", 
      "食安溯源，所有食材的溯源信息都有据可查，保障食品安全", 
    ],
  },
  {
    image: company_icon4,
    title: "临沂市沂河新区智慧食安平台",
    explainText:
      "临沂市沂河新区为了提升农副产品流通效率和增强食品安全防控能力，由临沂食安搭建智慧食安平台，通过平台实现统一的食材下单平台、供应链效率提升、食安溯源",
    points: [
      "实现公开透明的采购模式，学校、企事业单位等可以自主的选择供应商下单",
      "供应商通过系统分拣配送以及上传检测报告，极大提升供应商的配送效率，目前有60多家供应商入驻平台",
      "相关监管部门可以通过平台查看食品交易数据、价格指数、溯源信息等",
    ],
  },
  // {
  //   image: company_icon3,
  //   title: "郑州管城区教育局阳光采购平台",
  //   explainText:
  //     "郑州管城区有64所中小学校，所有学校的食材采购统一在教育平台上下单，通过平台对全地区学校食堂进行统一管理，建立供应商的询价、竞价、定价机制，降低采购成本，提高食材安全性",
  //   points: [
  //     "建立教育局（学校）食材采购商城，食材采购直接在商城下单，大大减少下单时间，提高学校食材采购工作效率、减少人工、避免差错率",
  //     "对供应商实现竞价比价，并可对所有食材产品、供应商的价格进行横向、纵向对比，给学校食材采购提供数据参考",
  //     "所有进入学校的食材实现溯源管理，实现商品的溯源功能",
  //     "供应商分拣配送，供应商配送的数据与学校收货、验货数据实现实时对接",
  //   ],
  // },
]
</script>
<template>
  <SpecialHeader :isShowContentImg="false" :bg-img="p2Banner">
    <template #title>
      <h1 class="color1">食材阳光智慧采购平台</h1>
    </template>
    <template #desc>
      <p class="color2">数字化助力阳光食材采购</p>
      <p class="color2">构建校园食安与供应链监管新生态</p>
    </template>
  </SpecialHeader>
  <div class="api__tenlight">
    <div class="wrap">
      <h2>
        <div>
          <span class="light_color">一个</span>中心、<span class="light_color">六大</span
          >运营系统、<span class="light_color">十大</span>核心亮点
        </div>
        <div>构建校园食安与供应链监管新生态</div>
      </h2>
      <img
        class="linner_tab"
        src="../assets/scygzhcgpt/linner_tab.png"
        alt="食材阳光智慧采购平台"
      />
      <div class="tenlight_bg_wrap">
        <img
          class="tenlight_bg"
          src="../assets/scygzhcgpt/tenlight_bg.png"
          alt="食材阳光智慧采购平台"
        />
        <div class="point tenlight_text1">学校食堂进销存管理</div>
        <div class="point tenlight_text2">多维度食材价格管控</div>
        <div class="point tenlight_text3">智能化菜谱库</div>
        <div class="point tenlight_text4">三方对账</div>
        <div class="point tenlight_text5">供应商全生命周期管控</div>
        <div class="point tenlight_text6">晨检巡检</div>
        <div class="point tenlight_text7">校园食安管控</div>
        <div class="point tenlight_text8">多维度溯源监管</div>
        <div class="point tenlight_text9">监管驾驶舱大屏</div>
        <div class="point tenlight_text10">家校联动</div>
        <div class="tenlight_text11 light_color2">蔬东坡食材阳光</div>
        <div class="tenlight_text12 light_color2">智慧采购平台</div>
        <div class="tenlight_text13">AI识别监管</div>
        <div class="tenlight_text14">配送车载GPS</div>
      </div>
    </div>
  </div>
  <div class="api__operationflow">
    <div class="wrap">
      <h2>
        <!-- <div><span class="light_color">一个</span>中心、<span class="light_color">六大</span>运营系统、<span class="light_color">十大</span>核心亮点</div> -->
        <div>校园食材阳光采购平台业务流程示意</div>
      </h2>
      <img
        class="linner_tab"
        src="../assets/scygzhcgpt/linner_tab.png"
        alt="食材阳光智慧采购平台"
      />
      <div class="tenlight_bg_wrap">
        <img
          class="tenlight_bg"
          src="../assets/scygzhcgpt/operationflow_bg.png"
          alt="食材阳光智慧采购平台"
        />
        <ul class="text_ul">
          <li>生产基地</li>
          <li>合作社</li>
          <li>批发市场</li>
        </ul>
        <div class="color_size2 tenlight_text1">平台运营方</div>
        <div class="color_size2 tenlight_text2">供应商</div>
        <div class="color_size2 tenlight_text3">学校自营食堂<br />或团餐公司</div>
        <div class="color_size2 tenlight_text4">食材检测</div>
        <div class="color_size2 tenlight_text12">教体局监管</div>
        <div class="color_size1 tenlight_text5">
          <span class="color_size3">1.</span>平台下单
        </div>
        <div class="color_size1 tenlight_text6">
          <span class="color_size3">2.</span>食材采购<br /><span
            style="visibility: hidden"
            >购</span
          >(大宗)
        </div>
        <div class="color_size1 tenlight_text7">
          <span class="color_size3">3.</span> 订单分配
        </div>
        <div class="color_size1 tenlight_text8">
          <span class="color_size3">4.</span>食材入库
        </div>
        <div class="color_size1 tenlight_text9">
          <span class="color_size3">5.</span>食材配送<br /><span
            style="visibility: hidden"
            >检</span
          >检测报告<br /><span style="visibility: hidden">标</span>标准单据
        </div>
        <div class="color_size1 tenlight_text10">
          <span class="color_size3">6.</span>支付结算
        </div>
        <div class="color_size1 tenlight_text11">
          <span class="color_size3">7.</span><br />对账清算
        </div>
      </div>
    </div>
  </div>
  <div class="api__system">
    <div class="wrap">
      <h2>
        <!-- <div><span class="light_color">一个</span>中心、<span class="light_color">六大</span>运营系统、<span class="light_color">十大</span>核心亮点</div> -->
        <div>
          <span class="light_color">六大</span>运营系统，<span class="light_color"
            >十大</span
          >核心亮点
        </div>
      </h2>
      <img
        class="linner_tab"
        src="../assets/scygzhcgpt/linner_tab.png"
        alt="食材阳光智慧采购平台"
      />
    </div>
    <div class="product list-box">
      <function-swipe :items="productSwipeList"></function-swipe>
    </div>
  </div>
  <div class="event__system">
    <div class="wrap">
      <h2>
        <!-- <div><span class="light_color">一个</span>中心、<span class="light_color">六大</span>运营系统、<span class="light_color">十大</span>核心亮点</div> -->
        <div><span class="light_color">选择蔬东坡，食安管理更放心</span></div>
      </h2>
      <img
        class="linner_tab"
        src="../assets/scygzhcgpt/linner_tab.png"
        alt="食材阳光智慧采购平台"
      />
    </div>
    <div class="product list-box">
      <!-- <function-swipe :items="productSwipeList"></function-swipe> -->
      <LinearSwipe :items="eventList"></LinearSwipe>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.light_color2 {
  color: #3e74f6ff;
  font-size: 9px;
  font-weight: 600;
}
.color1 {
  color: #333333ff !important;
}
.color2 {
  color: #3e74f6ff !important;
  margin-top: -7px !important;
}
.api__tenlight {
  // background-color: #f6f8feff;
  height: 300px;
  .wrap {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    h2 {
      margin-top: 20px;
      div {
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 600;
        color: #333333ff;
        line-height: 23px;
        text-align: center;
        &:last-child {
          margin-bottom: 10px;
        }
      }
      .light_color {
        color: #3e74f6ff;
      }
    }
    .linner_tab {
      width: 50px;
    }
    .tenlight_bg_wrap {
      position: relative;
      font-size: 7px;
      color: #333333ff;
      .point {
        position: relative;
      }
      .point::before {
        content: "";
        position: absolute;
        left: -7px; /* 调整圆点的位置 */
        top: 50%;
        transform: translateY(-50%); /* 垂直居中 */
        width: 4px;
        height: 4px;
        background-color: #3e74f6ff;
        border-radius: 50%;
      }
      .tenlight_bg {
        width: 100%;
      }
      .tenlight_text1 {
        position: absolute;
        top: 28px;
        left: 30px;
      }
      .tenlight_text2 {
        position: absolute;
        top: 70px;
        left: 30px;
      }
      .tenlight_text3 {
        position: absolute;
        top: 85px;
        left: 30px;
      }
      .tenlight_text4 {
        position: absolute;
        top: 100px;
        left: 30px;
      }
      .tenlight_text5 {
        position: absolute;
        top: 115px;
        left: 30px;
      }
      .tenlight_text6 {
        position: absolute;
        top: 153px;
        left: 30px;
      }
      .tenlight_text13 {
        position: absolute;
        top: 163px;
        left: 30px;
      }
      .tenlight_text14 {
        position: absolute;
        top: 173px;
        left: 30px;
      }
      .tenlight_text7 {
        position: absolute;
        top: 28px;
        right: 30px;
      }
      .tenlight_text8 {
        position: absolute;
        top: 85px;
        right: 23px;
      }
      .tenlight_text9 {
        position: absolute;
        top: 100px;
        right: 23px;
      }
      .tenlight_text10 {
        position: absolute;
        top: 160px;
        right: 44px;
      }
      .tenlight_text11 {
        position: absolute;
        top: 100px;
        right: 155px;
      }
      .tenlight_text12 {
        position: absolute;
        top: 112px;
        right: 160px;
      }
    }
  }
}
.api__operationflow {
  height: 255px;
  background-color: #f6f8feff;
  .wrap {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    h2 {
      margin-top: 20px;
      div {
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 600;
        color: #333333ff;
        line-height: 23px;
        text-align: center;
        &:last-child {
          margin-bottom: 10px;
        }
      }
      .light_color {
        color: #3e74f6ff;
      }
    }
    .linner_tab {
      width: 50px;
    }
    .tenlight_bg_wrap {
      position: relative;
      padding: 0px 20px 20px 20px;
      .tenlight_bg {
        width: 100%;
      }
      .color_size1 {
        color: #3f3f3fff;
        font-size: 10px;
        font-weight: 600;
      }
      .color_size2 {
        color: #ffffffff;
        font-size: 10px;
        font-weight: 400;
      }
      .color_size3 {
        color: #3e74f6ff;
      }
      .text_ul {
        position: absolute;
        top: 22px;
        left: 40px;
        display: flex;
        gap: 7px;
        flex-direction: column;
        li {
          width: 10px;
          color: #ffffffff;
          font-size: 8px;
          font-weight: 400;
        }
      }
      .tenlight_text1 {
        position: absolute;
        top: 55px;
        left: 143px;
      }
      .tenlight_text2 {
        position: absolute;
        top: 133px;
        left: 137px;
      }
      .tenlight_text3 {
        position: absolute;
        top: 49px;
        right: 33px;
      }
      .tenlight_text4 {
        position: absolute;
        top: 133px;
        right: 35px;
      }
      .tenlight_text5 {
        position: absolute;
        top: 20px;
        right: 113px;
      }
      .tenlight_text6 {
        position: absolute;
        top: 50px;
        left: 70px;
      }
      .tenlight_text7 {
        position: absolute;
        top: 80px;
        left: 120px;
        width: 20px;
        height: 15px;
      }
      .tenlight_text8 {
        position: absolute;
        top: 120px;
        left: 70px;
      }
      .tenlight_text9 {
        position: absolute;
        top: 125px;
        right: 88px;
      }
      .tenlight_text10 {
        position: absolute;
        top: 60px;
        right: 113px;
      }
      .tenlight_text11 {
        position: absolute;
        top: 80px;
        left: 165px;
        width: 20px;
        height: 15px;
      }
      .tenlight_text12 {
        position: absolute;
        top: 98px;
        right: 114px;
      }
    }
  }
}
.api__system {
  height: 555px;
  margin-bottom: 10px;
  .product {
    height: 300px;
  }
  .list-box {
    padding: 10px 30px;
  }
  .wrap {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    h2 {
      margin-top: 20px;
      div {
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 600;
        color: #333333ff;
        line-height: 23px;
        text-align: center;
        &:last-child {
          margin-bottom: 10px;
        }
      }
      .light_color {
        color: #3e74f6ff;
      }
    }
    .linner_tab {
      width: 50px;
    }
    .tenlight_bg_wrap {
      position: relative;
      width: 270px;
      padding: 0px 20px 20px 20px;
      .tenlight_bg {
        width: 100%;
      }
      .color_size1 {
        color: #3f3f3fff;
        font-size: 10px;
        font-weight: 600;
      }
      .color_size2 {
        color: #ffffffff;
        font-size: 10px;
        font-weight: 400;
      }
      .color_size3 {
        color: #3e74f6ff;
      }
    }
  }
}
.event__system {
  height: 685px;
//   margin-bottom: 30px;
  .product {
    height: 300px;
  }
  .list-box {
    padding: 10px 30px;
  }
  .wrap {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    h2 {
      margin-top: 20px;
      div {
        font-size: 16px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 600;
        color: #333333ff;
        line-height: 23px;
        text-align: center;
        &:last-child {
          margin-bottom: 10px;
        }
      }
      .light_color {
        color: #3e74f6ff;
      }
    }
    .linner_tab {
      width: 50px;
    }
    .tenlight_bg_wrap {
      position: relative;
      width: 270px;
      padding: 0px 20px 20px 20px;
      .tenlight_bg {
        width: 100%;
      }
      .color_size1 {
        color: #3f3f3fff;
        font-size: 10px;
        font-weight: 600;
      }
      .color_size2 {
        color: #ffffffff;
        font-size: 10px;
        font-weight: 400;
      }
      .color_size3 {
        color: #3e74f6ff;
      }
    }
  }
}
</style>
