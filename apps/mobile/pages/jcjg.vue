<script setup>
import jcjgProduct1 from '~/assets/jcjg/jcjg-product-1.png';
import jcjgProduct2 from '~/assets/jcjg/jcjg-product-2.png';
import jcjgProduct3 from '~/assets/jcjg/jcjg-product-3.png';
import jcjgProduct4 from '~/assets/jcjg/jcjg-product-4.png';
import jcjgProduct5 from '~/assets/jcjg/jcjg-product-5.png';
import jcjgProduct6 from '~/assets/jcjg/jcjg-product-6.png';
import jcjgCaseBg from '~/assets/jcjg/jcjg-case-bg.png';
import jcjgCase1 from '~/assets/jcjg/jcjg-case-1.png';
import jcjgCase2 from '~/assets/jcjg/jcjg-case-2.png';
import jcjgBanner from '~/assets/jcjg/jcjg-banner.png';

import CommonHeader from '~/components/CommonHeader.vue';

useHead({
	title: '净菜加工解决方案,净菜加工系统,净菜加工生产流程_蔬东坡',
	meta: [
		{ name: 'description', content: '助力净菜加工流程标准化、降低损耗、提升企业经营效益' },
		{ name: 'keywords', content: '净菜加工，净菜加工系统，净菜加工生产流程，净菜加工解决方案' },
	],
});

const productList = [
	{
		title: '计划管理',
		desc: [
			'需求自动汇总，实时掌控需求动态',
			'MRP计算，成品及半成品，生产原材料情况',
			'一键生成计划，计划效率提升90%'
		],
		img: jcjgProduct1,
	},
	{
		title: '采购管理',
		desc: [
			'采购员、供应商移动协同作业，效率翻倍',
			'自动分配供应商，支持一品多商',
			'采购进度查看，采购过程监管，杜绝贪腐'
		],
		img: jcjgProduct2,
	},
	{
		title: '加工管理',
		desc: [
			'加工流程可定义，加工过程管理颗粒度自主把控',
			'生产过程全程管控，生产标准SOP指导现场作业',
			'支持多重混合业态业务模式成本核算'
		],
		img: jcjgProduct3,
	},
	{
		title: '分拣管理',
		desc: [
			'智能硬件自动称重上传',
			'支持播种分拣、摘果分拣',
			'分拣进度实时掌控'
		],
		img: jcjgProduct4,
	},
	{
		title: '配送管理',
		desc: [
			'固定、半固定线路一键分配',
			'可视化地图排线，效率更高',
			'扫码装车，拒绝错漏货'
		],
		img: jcjgProduct5,
	},
	{
		title: '财务管理',
		desc: [
			'往来对账，应收应付管理，账目清晰',
			'自动生成应收应付明细账、汇总账报表',
			'结算周期管理，账龄分析，杜绝坏账风险'
		],
		img: jcjgProduct6,
	},
];
const benchmarkCasesList = [
	{
		title: '湖南今日送达',
		desc: '湖南今日送达，针对连锁餐饮特性，提供多样化的净菜加工服务，为终端门店打造标准化的供应链服务。与蔬东坡合作后，人效提升25%，整体损耗下降17%',
		img: jcjgCase1,
		bg: jcjgCaseBg,
	},
	{
		title: '常州李耿餐饮',
		desc: '常州李耿餐饮，集餐饮管理服务、集体配送等为一体的专业化综合性餐饮管理服务企业。江苏省团餐行业标杆企业之一。与蔬东坡合作后损耗下降30%，毛利率提升22%',
		img: jcjgCase2,
		bg: jcjgCaseBg,
	},
];

const handleShowModal = openModal;
</script>
<template>
	<CommonHeader>
		<template #title>
			<h1>净菜加工解决方案</h1>
		</template>
		<template #desc>
			<p>助力净菜加工流程标准化</p>
			<p>降低损耗、提高企业经营效益</p>
		</template>
		<template #img>
			<img class="tuancan_header__img" :src="jcjgBanner" alt="团餐团膳食堂承包供应链解决方案" />
		</template>
	</CommonHeader>
	<div class="jcjg">
		<div class="business_box">
			<img style="width: 100%" src="../assets/jcjg/jcjg-pain.png" alt="">
<!--			<p class="common_content__title">净菜加工行业痛点</p>-->
<!--			<div class="business_bg"></div>-->
		</div>
		
		<div class="solution_box">
			<p class="common_content__title">企事业单位食堂配送方案全流程</p>
			<div class="solution_box__content">
				<div class="solution_bg"></div>
			</div>
		</div>
		
		<div class="product_box">
			<p class="common_content__title">产品功能</p>
			<ProductSwipe :items="productList"></ProductSwipe>
		</div>
		
		<div class="client-value list-box">
			<p class="common_content__title">
				以客户价值为核心的全生<br/>命周期服务
			</p>
			<div class="client-con">
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-1.png" alt="">
					<p>企业级定制</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-2.png" alt="">
					<p>7x24小时服务</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-3.png" alt="">
					<p>1v1系统顾问</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-4.png" alt="">
					<p>一线实施专家</p>
				</div>
			</div>
			<button @click="handleShowModal">免费试用</button>
		</div>
		<BenchmarkCasesTab :items="benchmarkCasesList"></BenchmarkCasesTab>
	</div>
</template>

<style lang="scss" scoped>
.business_box {
	padding: 30px 0px 0px;
}
.business_box .business_bg {
	width: auto;
	height: 330px;
	background: url('../assets/tuancan/tuancan_content_img_info.png') no-repeat;
	background-size: cover;
}
.solution_box {
	padding: 20px 0px 0px;
	background-color: #f9fbff;
}
.solution_box .solution_box__content {
	padding: 16px 35px 30px 35px;
}
.solution_box .solution_box__content .solution_bg {
	height: 175px;
	background: url('../assets/jcjg/jcjg-flow.png') no-repeat;
	background-size: 100% 100%;
}
.product_box {
	padding: 16px 0px 24px;
}
.product_box > p {
	margin-bottom: 17px;
}
:deep(.sidebar){
	min-height: 390px;
}
.client-value {
	.client-con {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-template-rows: repeat(2, 1fr);
		grid-gap: 16px;
		>.client-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 4px 4px 0px rgba(59,105,220,0.05);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 14px;
			font-size: 14px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 16px;
			>img {
				width: 36px;
			}
		}
	}
	>button {
		width: 174px;
		height: 27px;
		border-radius: 5px;
		background-color: #FF9607;
		margin: 0 auto;
		display: block;
		color: white;
		font-size: 12px;
		margin-top: 16px;
	}
}
</style>
