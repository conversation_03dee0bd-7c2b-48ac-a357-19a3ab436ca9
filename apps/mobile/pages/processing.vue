<script setup lang="ts">
import ProductHeader from '~/components/ProductHeader.vue'
import ProductSpeciality from '~/components/ProductSpeciality.vue'
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'
import processingBanner from '~/assets/processing/processing-banner.png'
import processingTHYW from '~/assets/processing/processing-thyw.png'
import processingSCYY from '~/assets/processing/processing-scyy.png'
import processingZSTD from '~/assets/processing/processing-zstd.png'
import processingBDMJ from '~/assets/processing/processing-bdmj.png'
import processingCJ1 from '~/assets/processing/processing-cj1.png'
import processingCJ2 from '~/assets/processing/processing-cj2.png'
import processingCJ3 from '~/assets/processing/processing-cj3.png'
import processingCPGL from '~/assets/processing/processing-cpgl.png'
import processingJHGL from '~/assets/processing/processing-jhgl.png'
import processingJGGL from '~/assets/processing/processing-jggl.png'
import processingZNFJ from '~/assets/processing/processing-znfj.png'
import processingSASY from '~/assets/processing/processing-sasy.png'
import processingSJFX from '~/assets/processing/processing-sjfx.png'

import SceneSwipe from '~/components/SceneSwipe.vue'

useHead({
	title: '中央厨房管理系统_净菜加工系统_中央厨房管理软件-蔬东坡',
	meta: [
		{
			name: 'description',
			content:
				'蔬东坡中央厨房管理系统,助力央厨业务管理数字升级,实现中央厨房生产集约化、操作智能化、生产高效化、食品绿色化、财账清晰化,提高央厨企业精细化管理能力',
		},
		{ name: 'keywords', content: '中央厨房管理系统,中央厨房系统,中央厨房管理软件,净菜加工,央厨SaaS 软件' },
	],
});

const headerConfig = {
	title: '中央厨房系统',
	backImg: processingBanner,
	desc: '助力中央厨房企业精细化管理',
}
const specialityList = [
	{
		title: '贴合业务实际',
		desc: '从生产中来，到生产中去，真正有效解决实际生产痛点',
		img: processingTHYW
	},
	{
		title: '生产运营双核',
		desc: '不仅提供信息化解决方案更提供运营解决方案',
		img: processingSCYY
	},
	{
		title: '资深团队',
		desc: '平均行业经验超30年，驻场指导生产',
		img: processingZSTD
	},
	{
		title: '本地敏捷服务',
		desc: '8大本地化服务中心，提供针对性的全生命周期服务',
		img: processingBDMJ
	}
]
const productSwipeList = [
	{
		title: '菜谱管理',
		desc: ['菜谱智能决策：菜品、菜谱毛利预估，营养成分分析，助力菜谱智能决策', '菜谱库：打造企业菜谱库，快速制定符合要求的客户菜谱', '餐别餐标管理：餐别和餐标统一管理，简化菜谱作业'],
		img: processingCPGL,
	},
	{
		title: '计划管理',
		desc: ['需求自动汇总：多方汇总多源头需求，实时掌控需求动态', 'MRP计算：通过多维度信息自动输入，一键计算生产的成品及半成品，生产原材料情况', '一键生成计划：系统自动拆分，一键生成采购计划、生产计划，计划效率提升90%'],
		img: processingJHGL,
	},
	{
		title: '加工管理',
		desc: ['加工流程可定义：领投料，完工入库是否分离，加工过程管理颗粒度自主把控', '生产过程管理：领料、投料、退料、完工，工艺路线工序过程监管，全程管控', '成本核算：同时满足营养餐、肉类分割、净菜加工多重业务模式成本核算'],
		img: processingJGGL,
	},
	{
		title: '智能分拣',
		desc: ['提效60%：智能电子秤同步分拣重量，免去手工统计', '杜绝错分、漏分：分拣进度可视化，分拣缺货标记，应对各种情况'],
		img: processingZNFJ,
	},
	{
		title: '食安溯源',
		desc: ['源头追溯：食材供应商是谁，进货时间，收货人是谁，检疫票证展示', '检测信息：农残检测是否超标，农残检测报告展示', '过程追溯：生产过、原料产品转换、分拣、出库、配送过程展示'],
		img: processingSASY,
	},
	{
		title: '数据分析',
		desc: ['数据大屏：可视化数据大屏展示，数据实时掌控，树立企业形象', '报表分析：经营数据分析、生产数据分析、财务数据分析，数据一目了然'],
		img: processingSJFX,
	},
]
const sceneList = [
	{
		desc: '学生营养餐解决方案',
		img: processingCJ1,
	},
	{
		desc: '学生营养餐解决方案',
		img: processingCJ2,
	},
	{
		desc: '学生营养餐解决方案',
		img: processingCJ3,
	},
	{
		desc: '学生营养餐解决方案',
		img: processingCJ1,
	},
	{
		desc: '学生营养餐解决方案',
		img: processingCJ2,
	},
	{
		desc: '学生营养餐解决方案',
		img: processingCJ3,
	},
]
</script>

<template>
	<div class="processing-page">
		<product-header :config="headerConfig"></product-header>
		<div class="profit-enhancement list-box">
			<p class="common_content__title">产品价值</p>
			<div class="enhancement-con">
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjixiajaing.png" alt="">
					<div class="item-text">
						<p>商品损耗降低</p>
						<p class="rate">20%</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjixiajaing.png" alt="">
					<div class="item-text">
						<p>人力成本下降</p>
						<p class="rate">35%</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjishangsheng.png" alt="">
					<div class="item-text">
						<p>计划效率提升</p>
						<p class="rate">90%</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjishangsheng.png" alt="">
					<div class="item-text">
						<p>加工效率提升</p>
						<p class="rate">22%</p>
					</div>
				</div>
				<div class="enhancement-item">
					<img src="@/assets/common/a-shujutongjishangsheng.png" alt="">
					<div class="item-text">
						<p>订单效率提升</p>
						<p class="rate">85%</p>
					</div>
				</div>
			</div>
		</div>
		<div class="speciality list-box">
			<p class="common_content__title">
				学生营养餐、肉类加工与分割、 <br>
				净菜加工都能用！
			</p>
			<product-speciality :items="specialityList"></product-speciality>
		</div>
		<div class="product list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productSwipeList"></product-swipe-new>
		</div>
		<div class="scene list-box">
			<p class="common_content__title">应用场景</p>
			<scene-swipe :items="sceneList"></scene-swipe>
		</div>
	</div>
</template>

<style scoped lang="scss">
.processing-page {
	.list-box {
		padding: 10px 30px;
	}
	.list-box:nth-child(odd) {
		background-color: #f6f9fe;
	}
	.enhancement-con {
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		grid-gap: 10px;
		.enhancement-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 14px;
			>img {
				width: 34px;
			}
			>.item-text {
				width: 105px;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
				>.rate {
					font-size: 16px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
				}
			}
		}
	}
	.speciality {
		.common_content__title {
			width: 270px;
		}
	:deep(.sp-item) {
			>img {
				width: 60px;
				margin-right: 5px;
			}
		}
	}
}
</style>
