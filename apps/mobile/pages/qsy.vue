<script lang="ts" setup>
import qsyBanner from '~/assets/qsy/qsy-banner.png'
import qsypain1 from '~/assets/qsy/qsy-pain-1.png'
import qsypain2 from '~/assets/qsy/qsy-pain-2.png'
import qsypain3 from '~/assets/qsy/qsy-pain-3.png'
import qsypain4 from '~/assets/qsy/qsy-pain-4.png'

import qsyProduct1 from '~/assets/qsy/qsy-product-1.png'
import qsyProduct2 from '~/assets/qsy/qsy-product-2.png'
import qsyProduct3 from '~/assets/qsy/qsy-product-3.png'
import qsyProduct4 from '~/assets/qsy/qsy-product-4.png'
import qsyProduct5 from '~/assets/qsy/qsy-product-5.png'

import CommonHeader from '~/components/CommonHeader.vue';
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'
import BenchmarkCasesTab from '~/components/BenchmarkCasesTab.vue'
import qsyCaseBg from '~/assets/qsy/qsy-case-bg.png'
import qsyCase1 from '~/assets/qsy/qsy-case-1.png'
import qsyCase2 from '~/assets/qsy/qsy-case-2.png'
import qsyCase3 from '~/assets/qsy/qsy-case-3.png'
import footerImgWHSS from 'assets/roulei/roulei_footer_img_whss.png'
import footerBg from 'assets/roulei/roulei_footer_bg.png'
import footerImgHNXHH from 'assets/roulei/roulei_footer_img_hnxhh.png'
import footerImgGDZDF from 'assets/roulei/roulei_footer_img_gdzdf.png'

useHead({
	title: '企事业单位食堂配送解决方案_食材配送系统_单位食堂配送解决方案_蔬菜配送-蔬东坡',
	meta: [
		{
			name: 'description',
			content: '保障食品安全，降低经营风险，颠覆传统配送模式，减少中间环节损耗让企事业单位食堂配送更高效',
		},
		{
			name: 'keywords',
			content: '食材配送系统,企事业单位食堂食材采购,团膳配送系统,团膳食堂ERP,企事业单位食堂解决方案',
		},
	],
});

const productList = [
	{
		title: '订单处理 一键完成',
		desc: [
			'50个菜品，2分钟高效精准下单',
			'支持套餐订单，可按照套餐数量在购物车分解成原料'
		],
		img: qsyProduct1
	},
	{
		title: '数据化采购管控、智能定价采购成本立省5%',
		desc: [
			'根据客户下单明细，实时汇总生成采购需求',
			'供应商在线询价报价，智能比价，采购成本更可控',
			'采购收货智能称实重，采购供应商监管有据可依'
		],
		img: qsyProduct2
	},
	{
		title: '分拣称重 提效60%',
		desc: [
			'智能电子秤同步分拣重量，免去手工统计',
			'杜绝错分漏分',
			'分拣任务支持按商品、分类、客户等多'
		],
		img: qsyProduct3
	},
	{
		title: '物流配送 轻松排线',
		desc: [
			'自带司机APP，送货有导航',
			'支持按商品金额、重量、体积排线，降低车辆空载率'
		],
		img: qsyProduct4
	},
	{
		title: '生鲜溯源 食安保障',
		desc: [
			'区块链+大数据，一品一码，扫码溯源',
			'数据大屏与食品检测设备打通 清晰展示订单溯源信息'
		],
		img: qsyProduct5
	},
	
]
const painList = [
	{
		title: '企业经营管理难',
		desc: '生鲜管理制度不明确<br/>作业环节多<br/>人才培养难',
		img: qsypain1
	},
	{
		title: '数据分析难',
		desc: '人力成本高<br/> 采购成本高<br/> 难以提升业绩',
		img: qsypain2
	},
	{
		title: '生产过程管控难',
		desc: '损耗层级多<br/> 成本损耗不清晰产品转换 <br/>成本毛利无法统计数据不全<br/> 生产预测难，易浪费',
		img: qsypain3
	},
	{
		title: '食品安全溯源难',
		desc: '对账慢，流水账记录<br/> 账目易混乱<br/> 对账清算难<br/> 资金压力大',
		img: qsypain4
	}
]
const benchmarkCasesList = [
	{
		title: '重庆幼鲜通',
		desc: '重庆百姓之家旗下品牌“幼鲜通”专注幼儿园营养膳食食材配送服务，有专业高效的物流团队和配送网络。面对创业初期分拣效率低、人工算单、发车慢等难题，选择与蔬东坡合作，解决了业务瓶颈，进一步提升了管理效率。',
		img: qsyCase2,
		bg: qsyCaseBg,
	},
	{
		title: '四川玛王农业',
		desc: '品牌起源于2014年，专注团膳食材配送业务，主要服务对象为企事业、学校、医院等。现有厂房占地20余亩，总建筑面积10000㎡+。与蔬东坡合作后，既保障食品安全，又做到控制损耗，为企业降低了成本和经营风险。',
		img: qsyCase3,
		bg: qsyCaseBg,
	},
];

const handleShowModal = openModal;
</script>
<template>
	<CommonHeader>
		<template #title>
			<h1>企事业单位食堂配送解决方案</h1>
		</template>
		<template #desc>
			<p>保障食品安全、 降低经营风险</p>
			<p>颠覆传统配送模式、减少环节损耗</p>
		</template>
		<template #img>
			<img class="tuancan_header__img" :src="qsyBanner" alt="企事业单位食堂配送解决方案" />
		</template>
	</CommonHeader>
	<div class="qsy">
		<div class="solution_box list-box">
			<p class="common_content__title">企事业单位食堂配送行业痛点</p>
			<div class="empower-list">
				<div class="em-item" v-for="item in painList">
					<div class="img-con">
						<img :src="item.img" alt="">
					</div>
					<p class="title">{{ item.title }}</p>
					<p class="desc" v-html="item.desc"></p>
				</div>
			</div>
		</div>
		<div class="flow-box list-box">
			<p class="common_content__title">企事业单位食堂配送方案全流程</p>
			<img style="width: 100%" src="../assets/qsy/qsy-flow.png" alt="企事业单位食堂配送方案全流程">
		</div>
		<div class="product-box list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productList"></product-swipe-new>
		</div>
		<div class="client-value list-box">
			<p class="common_content__title">
				以客户价值为核心的全生<br/>命周期服务
			</p>
			<div class="client-con">
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-1.png" alt="">
					<p>企业级定制</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-2.png" alt="">
					<p>7x24小时服务</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-3.png" alt="">
					<p>1v1系统顾问</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-4.png" alt="">
					<p>一线实施专家</p>
				</div>
			</div>
			<button @click="handleShowModal">免费试用</button>
		</div>
		<BenchmarkCasesTab :items="benchmarkCasesList"></BenchmarkCasesTab>
	</div>
</template>

<style lang="scss" scoped>
.qsy {
	.list-box:nth-child(even) {
		background-color: #f6f9fe;
	}
	.solution_box {
		display: flex;
		flex-direction: column;
		align-items: center;
		.empower-list {
			display: grid;
			grid-gap: 18px;
			grid-template-rows: repeat(2, 1fr);
			grid-template-columns: repeat(2, 1fr);
			.em-item {
				background-color: #f9fbff;
				width: 134px;
				min-height: 150px;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 12px;
				>.img-con {
					border-radius: 50%;
					width: 40px;
					height: 40px;
					background: #2977FE;
					display: flex;
					align-items: center;
					justify-content: center;
					>img {
						width: 30px;
					}
				}
				.title {
					font-size: 12px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 24px;
					margin: 4px 0;
				}
				.desc {
					font-size: 9px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					text-align: center;
					color: #333333;
					line-height: 13px;
				}
			}
		}
	}
	.client-value {
		.client-con {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: repeat(2, 1fr);
			grid-gap: 16px;
			>.client-item {
				width: 152px;
				height: 56px;
				background: #2977FE;
				box-shadow: 2px 4px 4px 0px rgba(59,105,220,0.05);
				border-radius: 5px 5px 5px 5px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 14px;
				font-size: 14px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 16px;
				>img {
					width: 36px;
				}
			}
		}
		>button {
			width: 174px;
			height: 27px;
			border-radius: 5px;
			background-color: #FF9607;
			margin: 0 auto;
			display: block;
			color: white;
			font-size: 12px;
			margin-top: 16px;
		}
	}
	
	.map-box {
		padding: 25px;
		width: 100%;
		overflow-x: auto;
		overflow-y: hidden;
		display: flex;
		gap: 24px;
		>.map-item {
			flex-shrink: 0;
			width: 120px;
			height: 186px;
			background: #FFFFFF;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			text-align: center;
			>img {
				width: 100%;
				position: relative;
				z-index: 1;
			}
			.title {
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 28px;
			}
			.desc {
				font-size: 8px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 9px;
			}
			.map-text {
				text-align: center;
				z-index: 2;
				>.img-con {
					position: relative;
					z-index: 2;
					margin: 0 auto;
					margin-top: -20px;
					width: 32px;
					height: 32px;
					background: #2977FE;
					border-radius: 50%;
					box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.05);
					display: flex;
					align-items: center;
					justify-content: center;
					>img {
						width: 15px;
					}
				}
			}
		}
	}
}
</style>
