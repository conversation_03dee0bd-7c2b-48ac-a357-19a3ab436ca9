<script setup>
import swipeSPSH from 'assets/index/index-spsh.png';
import swipeFJ from 'assets/index/index-fj.png';
import swipeKSXD from 'assets/index/index-ksxd.png';
import swipeTJFX from 'assets/index/index-tjfx.png';
import swipeZNGK from 'assets/index/index-zngk.png';
import ProductSwipeNew from '~/components/ProductSwipeNew.vue';
import BusinessScenario from '~/components/BusinessScenario.vue';
import scenarioHYRY from 'assets/index/index-hyry.png';
import scenarioJSRZ from 'assets/index/index-jsrz.png';
import indexSys1 from 'assets/index/index-sys-1.png';
import indexSys2 from 'assets/index/index-sys-2.png';
import indexSys3 from 'assets/index/index-sys-3.png';
import indexSys1A from 'assets/index/index-sys-1-a.png';
import indexSys1B from 'assets/index/index-sys-1-b.png';
import indexSys1C from 'assets/index/index-sys-1-c.png';
import indexSys1D from 'assets/index/index-sys-1-d.png';
import indexSys2A from 'assets/index/index-sys-2-a.png';
import indexSys2B from 'assets/index/index-sys-2-b.png';
import indexSys2C from 'assets/index/index-sys-2-c.png';
import indexSys3A from 'assets/index/index-sys-3-a.png';
import indexSys3B from 'assets/index/index-sys-3-b.png';
import indexSys3C from 'assets/index/index-sys-3-c.png';
import indexSys3D from 'assets/index/index-sys-3-d.png';
import case1 from 'assets/index/case-1.png';
import case2 from 'assets/index/case-2.png';
import case3 from 'assets/index/case-3.png';
import case4 from 'assets/index/case-4.png';
import case5 from 'assets/index/case-5.png';
import case6 from 'assets/index/case-6.png';
import case7 from 'assets/index/case-7.png';
import case8 from 'assets/index/case-8.png';
import case1Icon from 'assets/index/case-1-icon.png';
import case2Icon from 'assets/index/case-2-icon.png';
import case3Icon from 'assets/index/case-3-icon.png';
import case4Icon from 'assets/index/case-4-icon.png';
import case5Icon from 'assets/index/case-5-icon.png';
import case6Icon from 'assets/index/case-6-icon.png';
// import case7Icon from 'assets/index/case-7-icon.png';
import case8Icon from 'assets/index/case-8-icon.png';
import HomeCaseSwipe from '~/components/HomeCaseSwipe.vue';

useHead({
  title: '蔬东坡生鲜配送软件系统,蔬菜食材配送系统-蔬东坡',
  meta: [
    {
      name: 'description',
      content:
        '蔬东坡提供专业的生鲜供应链SaaS解决方案，包括生鲜配送系统、蔬菜配送软件、食材配送软件等，为超过10000家生鲜配送企业提供数智服务',
    },
    { name: 'keywords', content: '生鲜配送系统,生鲜配送软件,蔬东坡' },
  ],
});

const sysList = [
	{
		title: '生鲜配送系统',
		subhead: '生鲜企业效能提升50%+，让生鲜人每天多睡两小时',
		img: indexSys1,
		desc: [
			{
				text: '企事业单位食堂配送解决方案',
				icon: indexSys1A,
				path: '/qsy'
			},
			{
				text: '餐饮酒店配送解决方案',
				icon: indexSys1B,
				path: '/p2'
			},
			{
				text: '团餐团膳食堂承包供应链解决方案',
				icon: indexSys1C,
				path: '/tuanshan'
			},
			{
				text: '高校食堂供应链管理解决方案',
				icon: indexSys1D,
				path: '/gxst'
			}
		]
	},
	{
		title: '中央厨房系统',
		subhead: '中央厨房效能提升50%+，让中央厨房每天多做两万份菜',
		img: indexSys2,
		desc: [
			{
				text: '学生营养餐中央厨房解决方案',
				icon: indexSys2A,
				path: '/xsyyc'
			},
			{
				text: '肉类加工与分割解决方案',
				icon: indexSys2B,
				path: '/roulei'
			},
			{
				text: '净菜加工解决方案',
				icon: indexSys2C,
				path: '/jcjg'
			}
		]
	},
	{
		title: '社区团购系统',
		subhead: '建立全流程、一体化的社区团购管理模式，助力企业快速抢占团购市场',
		img: indexSys3,
		desc: [
			{
				text: '营销活动丰富 天天新玩法',
				icon: indexSys3A,
				path: '/groupbuy'
			},
			{
				text: '团长运营小程序 快速成团',
				icon: indexSys3B,
				path: '/groupbuy'
			},
			{
				text: '商品运营后台 清晰有序',
				icon: indexSys3C,
				path: '/groupbuy'
			},
			{
				text: '供应链系统管理 收益最大化',
				icon: indexSys3D,
				path: '/groupbuy'
			}
		]
	},
]
const productList = [
	{
		title: '分拣速度上升，分拣称重 提效60%',
		desc: ['标品一键分拣，自动打单称重,数据通传生鲜配送系统后台，效率提高60%', '分拣状态查询，杜绝错分漏分，分拣进度可视化，在家也可做管理', '多种分拣方式可供选择，支持商品分拣、客户分拣、路线分拣...'],
		img: swipeFJ,
	},
	{
		title: '商品损耗下降20%，进销存管理严控损耗',
		desc: ['精细化库区、库位、货架管理，可有效减少90%的找货时间', '扫码收获入库+扫码盘点，轻松一扫，盘点效率提升2倍', '移动操作，无纸化管理，随时随地，掌握库房情况'],
		img: swipeSPSH,
	},
	{
		title: '订单效率上升90%，快速下单 一键完成',
		desc: ['订单处理，自动汇总，节省人工统计时间', '补单/改单，及时调整，采购/供应商可手机实时查看及调整', '联动库存，计算损耗，通过生鲜配送系统进行更为精确的采购预测'],
		img: swipeKSXD,
	},
	{
		title: '准确率上升99.9%，全流程智能管控',
		desc: ['“生鲜大脑”——生鲜配送系统，实时掌握采购、库房、配送、手动报损等数据', '分拣大屏，进度可见错分漏分预警提醒，分拣进度实时把控', '数据大屏，精准分析，订单、商品、交易、配送、多维度分析'],
		img: swipeZNGK
	},
	{
		title: '净利润提升3-5%，多维度统计分析',
		desc: ['经营状况，随时呈现，商品、订单的销售/退货情况清晰展现', '财务数据，自动生成，每笔订单均有据可查，收款记录清晰明了', '全流程损耗统计，通过生鲜配送系统把控各环节，降低20%损耗成本'],
		img: swipeTJFX
	}
];
const periodList = [
	{
		title: '生存期 — 破局新生',
		items: ['现场实施培训', '线上直播培训', '远程连线指导', '系统操作手册', '专属服务顾问']
	},
	{
		title: '发展期 — 能力建设',
		items: ['定期现场回访', '最佳实践应用', '客户案例分享', '管理员认证培训', '行业沙龙邀约', '系统使用报告', '版本迭代推送']
	},
	{
		title: '平台期 — 陪伴成功',
		items: ['企业运营诊断', '落地实战培训', '企业管理咨询', '行业发展峰会', '企业管理研修班', '年度奖项评选']
	}
];
const scenarioList = [
	{
		title: '行业荣誉',
		img: scenarioHYRY,
		desc: `中国产业互联网推荐服务商、SaaS软件最佳服务商、中国农业互联网先锋企业、中国未来农业100强、中国产业互联网100强、中国团餐供应链优秀服务商、中国农产品供应链建设优秀单位、中国团餐信息化服务商top30`,
		// desc: ['中国产业互联网推荐服务商', 'SaaS软件最佳服务商', '中国农业互联网先锋企业', '中国未来农业100强', '中国产业互联网100强', '中国团餐供应链优秀服务商', '中国农产品供应链建设优秀单位', '中国团餐信息化服务商top30', '......']
	},
	{
		title: '技术认证',
		img: scenarioJSRZ,
		desc: `100+专利及软著、国家高新技术企业、北京市专精特新中小企业、ISO9001研发质量管理体系认证、国家三级等保认证、微信支付最佳合作服务商`,
		// desc: ['100+专利及软著', '国家高新技术企业', '北京市专精特新中小企业', 'ISO9001研发质量管理体系认证', '国家三级等保认证', '微信支付最佳合作服务商', '......']
	}
]
const caseList = [
	{
		title: '四川玛王农业',
		img: case1,
		icon: case1Icon,
		desc: '品牌起源于2014年，专注团膳食材配送业务，主要服务对象为企事业、学校、医院等。现有厂房占地20余亩，总建筑面积10000㎡+。与蔬东坡合作后，既保障食品安全，又做到控制损耗，为企业降低了成本和经营风险。'
	},
	{
		title: '重庆幼鲜通',
		img: case8,
		icon: case8Icon,
		desc: '重庆百姓之家旗下品牌“幼鲜通”专注幼儿园营养膳食食材配送服务，有专业高效的物流团队和配送网络。面对创业初期分拣效率低、人工算单、发车慢等难题，选择与蔬东坡合作，解决了业务瓶颈，进一步提升了管理效率。'
	},
	// {
	// 	title: '昆明学院',
	// 	img: case2,
	// 	icon: case2Icon,
	// 	desc: '昆明学院，校内食堂+其余商户，合计1000多档口需管控。与蔬东坡合作后，实现业财数据一体化，帮助学校实现全流程监管，增强学校食安管控力；供应商可与档口直接对账，学校后勤直接查询账单数据，为三方省时省力；小程序商城内直接显示各商品的基准价与上浮率，方便档口/商户比价，方便学校统一汇总记录。'
	// },
	// {
	// 	title: '欧亚学院',
	// 	img: case3,
	// 	icon: case3Icon,
	// 	desc: '西安欧亚学院是一所多学科协调发展的国际化应用型普通本科高校。校内100多档口+餐饮街，每日所需食材量数十吨，包含上千品类。与蔬东坡合作后，学校对供应商、档口/商户行为进行统一管控；供应商自行管理商品，直接生成账单，与档口/商户直接对账；食安溯源，保障食品安全。'
	// },
	{
		title: '菜无忧',
		img: case4,
		icon: case4Icon,
		desc: '菜无忧，成立于2021年，与蔬东坡合作后，为其制定中小餐饮配送解决方案，现已为菜无忧搭建了信息化运营管理体系与先进的生鲜分拣中心，成功打造垂直供应链体系，在提升运转效率的同时也提高了企业的核心竞争力。'
	},
	{
		title: '今日送达',
		img: case5,
		icon: case5Icon,
		desc: '湖南今日送达，针对连锁餐饮特性，提供多样化的净菜加工服务，为终端门店打造标准化的供应链服务。与蔬东坡合作后，人效提升25%，整体损耗下降17%。'
	},
	{
		title: '武汉三盛',
		img: case6,
		icon: case6Icon,
		desc: '武汉三盛后勤服务管理有限公司是一家专门从事肉类分割的企业。目前三盛长期稳定合作客户300多家，年流水近一个亿。随着业务量增大，手动统计数据错误率高、效率低、整体数据的准确率不断降低。与蔬东坡合作后，业务处理速度得到了极大提升，准确率达到100%。'
	},
	{
		title: '北海启悦',
		img: case7,
		desc: '成立于2021年，专注学校食堂承包配送，年销售额近2000万。与蔬东坡合作后，上线小程序商城，客户可以在商城端直接下单。节省手动制单时间；之前所用系统无法实现多仓管理，手动汇总两个仓库的数据。通过蔬东坡可以实现一套系统多个账套，分开管控双仓库；项目点线上收货，收货数据自动同步至采购单，后台一键汇总收货数据。'
	},
]
const handleShowModal = openModal;
</script>
<template>
  <div class="sdp-home">
		<div class="home-banner">
			<h1>生鲜食材供应链数字化开创者</h1>
			<p>上万家客户信赖的生鲜食材供应链ERP</p>
			<button  @click="handleShowModal()">获取专属方案</button>
		</div>
		<div class="all-scheme list-box">
			<div class="scheme-swipe">
				<img @click="$refs.swipe.prev()" src="../assets/index/zuo.png" alt="">
				<van-swipe ref="swipe" :show-indicators="false" class="swipe-con" >
					<van-swipe-item v-for="item in sysList" :key="item.title">
						<div class="card-item">
							<img :src="item.img" alt="">
							<div class="content-box">
								<p class="title">{{ item.title }}</p>
								<p class="subhead">{{ item.subhead }}</p>
								<div @click="$router.push(line.path)" class="desc-item" v-for="line in item.desc">
									<img class="icon" :src="line.icon" alt="">
									<p>{{ line.text }}</p>
									<img class="arrow" src="../assets/index/arrow-right.png" alt="">
								</div>
							</div>
						</div>
					</van-swipe-item>
				</van-swipe>
				<img @click="$refs.swipe.next()" src="../assets/index/you.png" alt="">
			</div>
			<button @click="handleShowModal()" class="get-scheme">免费试用</button>
		</div>
		<div class="product list-box">
			<p class="common_content__title">五大产品优势</p>
			<product-swipe-new :has-try="true"  :items="productList"></product-swipe-new>
		</div>
		
		<div class="period list-box">
			<p class="common_content__title">
				以客户价值为核心的<br/>
				全生命周期服务</p>
			<div class="period-list">
				<div class="period-item-con" v-for="(item, index) in periodList" :key="item.title">
					<div class="period-item" >
						<p class="title">{{ item.title }}</p>
						<div class="entry-list">
							<p  class="item" v-for="text in item.items" :key="text">{{ text }}</p>
						</div>
					</div>
					<img v-show="index < periodList.length -1 " src="@/assets/common/arrow-down-full.png" alt="">
				</div>
			</div>
		</div>
		<div class="client-value list-box">
			<div class="client-con">
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-1.png" alt="">
					<p>企业级定制</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-2.png" alt="">
					<p>7x24小时服务</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-3.png" alt="">
					<p>1v1系统顾问</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-4.png" alt="">
					<p>一线实施专家</p>
				</div>
			</div>
			<button @click="handleShowModal">免费试用</button>
		</div>
		<div class="pole-case list-box">
			<p class="common_content__title">标杆案例</p>
			<home-case-swipe :items="caseList"></home-case-swipe>
		</div>
		<div class="new-fashion list-box">
			<p class="common_content__title">
				公司荣誉与实力认证
			</p>
			<business-scenario :special="true" :items="scenarioList"></business-scenario>
		</div>
		
  </div>
</template>
<style lang="scss">
.sdp-home {
	.home-banner {
		height: 225px;
		width: 100%;
		background-image: url("../assets/index/top-banner.png");
		background-size: 100% 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		* {
			text-align: center;
		}
		h1 {
			font-size: 22px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			color: #FFFFFF;
			line-height: 26px;
			margin-top: 55px;
		}
		>p {
			font-size: 12px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;
			line-height: 14px;
			margin-top: 16px;
			margin-bottom: 28px;
		}
		button {
			width: 144px;
			height: 32px;
			font-size: 14px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: white;
			border-radius: 4px;
			background-color: #FF9607;
		}
	}
	.all-scheme {
		padding: 10px 18px;
		.scheme-swipe {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 10px;
			.swipe-con {
				width: 275px;
				box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.05);
			}
			>img {
				width: 20px;
			}
			.card-item {
				width: 275px;
				height: 354px;
				background: #F5F9FF;
				box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.05);
				border-radius: 5px 5px 5px 5px;
				>img {
					height: 150px;
				}
				.content-box {
					text-align: center;
					.title {
						font-size: 14px;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						color: #333333;
						line-height: 16px;
						margin-top: 12px;
					}
					.subhead {
						font-size: 10px;
						font-family: PingFang SC, PingFang SC;
						font-weight: 400;
						color: #666666;
						line-height: 12px;
						margin: 12px 0;
					}
					.desc-item {
						margin: 6px auto;
						width: 232px;
						height: 24px;
						background: #FFFFFF;
						border-radius: 5px 5px 5px 5px;
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding: 0 8px;
						>p {
							text-align: left;
							width: 80%;
							font-size: 10px;
							font-family: PingFang SC, PingFang SC;
							font-weight: 400;
							color: #2977FE;
							line-height: 12px;
						}
						>.icon {
							width: 20px;
						}
						>.arrow {
							width: 20px
						}
					}
				}
			}
		}
		.get-scheme {
			display: block;
			width: 176px;
			height: 32px;
			background: #2977FE;
			border-radius: 5px 5px 5px 5px;
			color: white;
			font-size: 14px;
			margin: 15px auto;
		}
	}
	// 当自身为父级容器下的偶数元素且类名包含list-box时, 背景颜色为#f6f9fe
	.list-box:nth-child(odd) {
		background-color: #f6f9fe;
	}
	.product {
		padding-top: 2px;
		.common_swipe__box, .van-swipe {
			background-color: white;
			.title {
				color: #2977FE;
			}
		}
	}
	.period {
		padding-top: 2px;
	}
	.period-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		.period-item-con {
			text-align: center;
			.period-item {
				width: 270px;
				min-height: 82px;
				background: #F5F9FF;
				box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
				border-radius: 10px 10px 10px 10px;
				opacity: 1;
				overflow: hidden;
				>p {
					display: block;
					height: 28px;
					background: #2977FE;
					box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
					font-size: 12px;
					color: white;
					line-height: 28px;
					text-align: center;
				}
				.entry-list {
					padding: 10px 9px;
					font-size: 11px;
					color: #666666;
					display: flex;
					justify-content: center;
					flex-wrap: wrap;
					grid-gap: 5px;
					>p {
						width: 32%;
						// 不允许换行
						white-space: nowrap;
						text-align: left;
					}
				}
			}
			>img {
				width: 30px;
				margin-top: -8px;
				margin-bottom: 0px;
			}
		}
	}
	.pole-case {
		padding-top: 2px;
		padding-left: 0;
		padding-right: 0;
	}
	.new-fashion {
		padding-top: 2px;
		.common_content__title {
			width: auto;
		}
	}
	.client-value {
		.client-con {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: repeat(2, 1fr);
			grid-gap: 16px;
			>.client-item {
				width: 152px;
				height: 56px;
				background: #2977FE;
				box-shadow: 2px 4px 4px 0px rgba(59,105,220,0.05);
				border-radius: 5px 5px 5px 5px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 14px;
				font-size: 14px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 16px;
				>img {
					width: 36px;
				}
			}
		}
		>button {
			width: 174px;
			height: 29px;
			background: #2977FE;
			border-radius: 5px 5px 5px 5px;
			margin: 0 auto;
			display: block;
			color: white;
			font-size: 12px;
			margin-top: 16px;
		}
	}
}
</style>
