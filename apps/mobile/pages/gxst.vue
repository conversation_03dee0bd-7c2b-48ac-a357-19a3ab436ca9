<script setup>
import gxstProduct1 from '~/assets/gxst/gxst-product-1.png';
import gxstProduct2 from '~/assets/gxst/gxst-product-2.png';
import gxstProduct3 from '~/assets/gxst/gxst-product-3.png';
import gxstProduct4 from '~/assets/gxst/gxst-product-4.png';
import gxstProduct5 from '~/assets/gxst/gxst-product-5.png';
import gxstCaseBg from '~/assets/gxst/gxst-case-bg.png';
import gxstCase1 from 'assets/index/case-3.png';
import gxstCase2 from 'assets/index/case-2.png';
import caseBaoshan from 'assets/gxst/case-baoshan.png';
import caseXiankeji from 'assets/gxst/case-xiankeji.png';
import caseBaoshanIcon from 'assets/gxst/case-baoshan-icon.png';
import caseXiankejiIcon from 'assets/gxst/case-xiankeji-icon.png';
import descImg from '~/assets/tuancan/tuancan_header_img_desc.png';

import CommonHeader from '~/components/CommonHeader.vue';
import HomeCaseSwipe from '~/components/HomeCaseSwipe.vue';
import case3Icon from 'assets/index/case-3-icon.png';
import case2Icon from 'assets/index/case-2-icon.png';

useHead({
	title: '高校食堂供应链管理解决方案_生鲜供应链管理_蔬菜配送-蔬东坡',
	meta: [
		{ name: 'description', content: '助力高效整合供应链规范供应商、档口行为，保障食品安全' },
		{ name: 'keywords', content: '生鲜供应链,高校食堂供应链管理,生鲜供应链管理系统,高校食堂供应链管理解决方案' },
	],
});

const productList = [
	{
		title: '商城自动接单、自动拆单，效率提升100%',
		desc: [
			'避免手工接单，接单效率提升100%',
			'系统根据供应商自动拆单，使拆单错误率降为0'
		],
		img: gxstProduct1,
	},
	{
		title: '单据实时可追溯，数据0丢失',
		desc: [
			'避免手工接单，接单效率提升100%',
			'监管机构可随时根据单据对食品源头进行监控，一旦出现问题，可立即控制流通'
		],
		img: gxstProduct2,
	},
	{
		title: '档口的进销存管理，可使货品丢失降低10%',
		desc: [
			'每个档口的货品数量的进和出都有据可查',
			'对货品可进行有效监管，避免人为导致的货品丢失'
		],
		img: gxstProduct3,
	},
	{
		title: '系统自动对账，可节省1-2个财务人员成本',
		desc: [
			'供应商发货后，系统就已经自动完成了对账',
			'无需安排1-2个人工去根据纸质单据去核对账目，即费时又容易出错'
		],
		img: gxstProduct4,
	},
	{
		title: '多维度报表，档口利润当天出',
		desc: [
			'实时对档口利润进行测算，以保证档口正常经营',
			'根据实时利润情况，从而给予政策性补贴的支持'
		],
		img: gxstProduct5,
	},
];
const benchmarkCasesList = [
	{
		title: '西安欧亚学院',
		desc: '西安欧亚学院是一所多学科协调发展的国际化应用型普通本科高校、中国大陆地区办学水平和办学层次最高的财经类民办大学之一。校内100多档口+餐饮街，每日所需食材量数十吨，包含上千品类。与蔬东坡合作后，学校对供应商、档口/商户行为进行统一管控；供应商自行管理商品，直接生成账单，与档口/商户直接对账；食安溯源，保障食品安全。',
		img: gxstCase1,
		icon: case3Icon,
		bg: gxstCaseBg,
	},
	{
		title: '昆明学院',
		desc: '昆明学院地处中国（云南）自贸试验区昆明片区（昆明市经开区），校内食堂+其余商户，合计1000多档口需管控。与蔬东坡合作后，实现业财数据一体化，帮助学校实现全流程监管，增强学校食安管控力；供应商可与档口直接对账，学校后勤直接查询账单数据，为三方省时省力；小程序商城内直接显示各商品的基准价与上浮率，方便档口/商户比价，方便学校统一汇总记录。',
		img: gxstCase2,
		icon: case2Icon,
		bg: gxstCaseBg,
	},
	{
		title: '保山学院',
		desc: '保山学院是一所本科层次的普通高等学校。与蔬东坡合作后，食堂档口多渠道自助下单，容错率提高30%；系统自动汇总采购需求，自动匹配对应供应商，节省20%时间；财务自动对账，结算及时，食堂档口成本利润清晰。',
		img: caseBaoshan,
		icon: caseBaoshanIcon,
	},
	{
		title: '西安科技大学高新学院',
		desc: '西安科技大学高新学院是一所全日制普通本科独立学院，师生共11000余人。因后勤工作过于繁琐，供应商引荐与蔬东坡合作。通过智能化解决各档口订单汇总问题；分析采购成本，并输出报表；财务明确每个档口/供应商，商品数量和金额，效率提升60%，2-3人轻松保障后勤。',
		img: caseXiankeji,
		icon: caseXiankejiIcon,
	},
];
</script>
<template>
	<CommonHeader>
		<template #title>
			<h1>高校食堂供应链管理解决方案</h1>
		</template>
		<template #desc>
			<p>助力高校整合供应链，规范供应商、</p>
			<p>档口行为，保障食品安全</p>
		</template>
		<template #img>
			<img class="tuancan_header__img" :src="descImg" alt="团餐团膳食堂承包供应链解决方案" />
		</template>
	</CommonHeader>
	<div class="gxst">
		<div class="business_box">
			<img style="width: 100%" src="../assets/gxst/gxst-pain.png" alt="">
<!--			<p class="common_content__title">高校食堂供应链管理行业痛点</p>-->
<!--			<div class="business_bg"></div>-->
		</div>
		
		<div class="solution_box">
			<p class="common_content__title">蔬东坡团餐团膳解决方案全流程</p>
			<div class="solution_box__content">
				<div class="solution_bg"></div>
			</div>
		</div>
		
		<div class="product_box">
			<p class="common_content__title">产品功能</p>
			<ProductSwipe :items="productList"></ProductSwipe>
		</div>
		
		<div class="case_box">
			<p class="common_content__title">标杆案例</p>
			<home-case-swipe :items="benchmarkCasesList"></home-case-swipe>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.business_box {
	padding: 30px 0px 0px;
}
.business_box .business_bg {
	width: auto;
	height: 330px;
	background: url('../assets/tuancan/tuancan_content_img_info.png') no-repeat;
	background-size: cover;
}
.solution_box {
	padding: 20px 0px 0px;
	background-color: #f9fbff;
}
.solution_box .solution_box__content {
	padding: 16px 35px 30px 35px;
}
.solution_box .solution_box__content .solution_bg {
	height: 175px;
	background: url('../assets/gxst/gxst-flow.png') no-repeat;
	background-size: 100% 100%;
}
.product_box {
	padding: 16px 0px 24px;
}
.product_box > p {
	margin-bottom: 17px;
}
:deep(.sidebar){
	min-height: 390px;
}
.case_box {
	padding-bottom: 10px;
}
</style>
