<template>
  <div class="khal-page">
    <div class="khal-header" v-show="detailData">
      <div class="header_bg_img">
        <img :src="detailData?.conver_image" :alt="detailData?.post_title" />
        <div class="bg_color"></div>
      </div>
      <div class="header-content">
        <p>{{ redirectData?.post_title }}</p>
      </div>
    </div>
    <div class="customer">
      <h2 class="customer_title">{{ detailData?.post_title }}</h2>
      <div class="customer_tag">赋能助力：</div>
      <div class="customer_content">{{ detailData?.cooperation_value }}</div>
    </div>

    <pre class="detail_container" style="white-space: pre-line">
        <div v-html="redirectData?.post_content"></div>
    </pre>
    <div class="customer_rondom" v-show="randomData && randomData.length">
      <CustomerList :data-source="randomData">
        <template #title> 
          <p class="article-detail__line"></p>
          <div class="fs1">更多精彩案例：</div>
        </template>
      </CustomerList>
    </div>
  </div>
</template>
<script setup lang="ts">
import CustomerList from "@/components/CustomerList.vue";
import { ref, onMounted } from "vue";
// const imgUrl = ref();
const route = useRoute();
const id = route.params.id;
let detailData = ref();
let dataList = ref([]);
let redirectData = ref();
let randomData = ref();
// const getData = async () => {
  // const { data } = (await getNewsDetail(id)) || {};
  // const res = await getStudyCenterPages("客户案例");
  // if (res) {
  //   dataList = res?.data?.list || [];
  //   console.log("dataList", dataList);
  //   randomData.value = dataList.sort(() => Math.random() - 0.5).slice(0, 4) || [];
  //   getIMgUrl(dataList);
  // }
// };
const getIMgUrl = (data: any) => {
  if (data.length > 0) {
    data.find((item: any) => {
      if (item.ID.toString() === id.toString()) {
        if (item) {
          detailData.value = item || {};
        }
        if (item?.redirect_url) {
          const match = item?.redirect_url.match(/c-(\d+)\.html/);
          const result = match ? match[1] : "";
          getDirectData(result);
          console.log("result", result);
        }
      }
    });
  }
};
const getDirectData = async (id: any) => {
  const { data } = (await getNewsDetail(id)) || {};
  if (data) {
    redirectData.value = data || {};
    console.log("data", data);
  }
};
const res = await getStudyCenterPages("客户案例");
  if (res) {
    dataList.value = res?.data?.list || [];
    console.log("dataList", dataList.value);
    randomData.value = dataList.value.sort(() => Math.random() - 0.5).slice(0, 4) || [];
    getIMgUrl(dataList.value);
  }
// onMounted(() => {
//   // imgUrl.value = sessionStorage.getItem("imgUrl") || "";
//   // console.log("imgUrl", sessionStorage.getItem("imgUrl"));
//   getData();
// });
</script>
<style scoped lang="scss">
.khal-page {
  .khal-header {
    position: relative;
    height: 162px;
    z-index: 1;
    .header_bg_img {
      img {
        position: relative;
        width: 100%;
        height: 162px;
      }
      .bg_color {
        width: 100%;
        height: 162px;
        z-index: 99;
        transform: translateY(-102%);
        background-color: rgba(51, 51, 51, 0.6);
      }
    }

    > .header-content {
      text-align: center;
      position: absolute;
      z-index: 2;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      padding-top: 20px;

      p {
        font-weight: 600;
        font-size: 18px;
        color: #ffffff;
        line-height: 20px;
        letter-spacing: 2px;
        text-align: left;
        padding: 0px 15px 10px 15px;
      }
    }
  }
  .customer {
    padding: 10px;
    margin: 0 auto;
    width: 343px;
    // height: 128px;
    background-color: white;
    box-shadow: 1px 1px 2px 1px rgba(28, 102, 231, 0.1);
    border-radius: 5px 5px 5px 5px;
    overflow: hidden;
    margin-top: -60px;
    z-index: 2;
    position: relative;
    .customer_title {
      color: #057ff1ff;
      font-size: 16px;
      padding-bottom: 10px;
    }
    .customer_tag {
      margin-bottom: 7px;
      font-size: 11px;
      padding: 2px 0px 3px 4px;
      background-color: rgba(99, 180, 255, 0.6);
      width: 59px;
      border-radius: 3px;
      color: #057ff1ff;
      font-weight: 600;
    }
    .customer_content {
      font-size: 10px;
      color: #333333ff;
    }
  }
  .article-detail__line {
    height: 0.5px;
    margin: 12px 0;
    background: #e6e6e6;
  }
  .fs1 {
    font-size: 12px;
    color: #333333ff;
    margin-bottom: 18px;
  }
}
</style>
<style scoped>
.detail_container {
  padding: 0px 30px 0px 30px;
  /deep/ div,
  p {
    margin: 10px 0;
    color: #666;
    font: 18px PingFang SC-Regular, PingFang SC;
    line-height: 33px;
  }
  /deep/ h3 {
    margin: 10px 0;
    color: #3c3c3c;
    font-size: 16px;
    font-weight: bold;
  }
  /deep/ img[src] {
    width: 100%;
    height: 160px;
  }
}
</style>
