<script setup lang="ts">
import ProductHeader from '~/components/ProductHeader.vue'
import ProductSpeciality from '~/components/ProductSpeciality.vue'
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'
import paasBanner from '~/assets/products/paas-banner.png'

import paasSpeciality1 from '~/assets/products/paas-speciality-1.png'
import paasSpeciality2 from '~/assets/products/paas-speciality-2.png'
import paasSpeciality3 from '~/assets/products/paas-speciality-3.png'

import paasProduct1 from '~/assets/products/paas-product-1.png'
import paasProduct2 from '~/assets/products/paas-product-2.png'
import paasScenar1 from '~/assets/products/paas-scenar-1.png'
import paasScenar2 from '~/assets/products/paas-scenar-2.png'
import BusinessScenario from '~/components/BusinessScenario.vue'

useHead({
	title: '生鲜配送PaaS平台_生鲜配送共享平台_蔬东坡PaaS定制-蔬东坡',
	meta: [
		{
			name: 'description',
			content: '蔬东坡业务定制平台,根据生鲜配送企业业务需要,为生鲜企业提供可自主定制化软件功能开发,帮助企业定义符合自身业务的管理流程,让业务更敏捷,护航企业高速发展。'
		},
		{
			name: 'keywords',
			content: '蔬东坡系统定制开发,生鲜配送软件定制,生鲜管理软件开发',
		},
	],
});

const headerConfig = {
	title: '业务定制平台 PaaS',
	backImg: paasBanner,
	desc: '帮助企业定义符合自身业务的管理流程<br/> 让业务更敏捷，护航企业高速发展',
}
const specialityList = [
	{
		title: '功能强大',
		desc: '整合商品信息流、物流、资金流',
		img: paasSpeciality1
	},
	{
		title: '业财一体',
		desc: '业务处理与财税管理、一体化管理',
		img: paasSpeciality2
	},
	{
		title: '集中管控',
		desc: '集团多公司集中做账，多元化运营',
		img: paasSpeciality3
	},
]
const productSwipeList = [
	{
		title: '',
		desc: ['自定义工作台：基础平台+插拔式应用，可快速搭建个性化', '定制工作台，灵活适配丰富的业态，助力降本增效', '规则引擎+自定义字段：基础平台+规则引擎集数据、业', '务、流程、协同于一体，是企业数字化综合管理中台自定义', '字段适配千面的业务管理方式', '多维权限体系：灵活的角色管理能力，配置细粒度的操作权', '限和资源权限'],
		img: paasProduct1,
	},
	{
		title: '',
		desc: ['云原生+微服务：采用云原生微服务框架提供一站式高效服', '务，以应用服务微服务化为中心实现应用服务全生命周期管', '理和多维度监控', '弹性扩容：自定义伸缩组，支持公有云、私有云集群的自动', '扩缩容', '开放生态：开放、务实、创新、共赢的生态体系，真心实意', '內客户着想，共同帮助客户实现业务超级增长'],
		img: paasProduct2,
	},
]
const sceneList = [
	{
		title: '低代码高效率',
		desc: '可视化设计器，低代码，零门槛 在线开发测试，敏捷高效 更少代码，更高质量',
		img: paasScenar2
	},
	{
		title: '与业务深度融合',
		desc: '流程能力与业务数据操作深入集成 深入业务场景，配置成本更低 业务插件机制，灵活扩展核心业务操作',
		img: paasScenar1
	}
]
</script>

<template>
	<div class="paas-page">
		<product-header :dark="true" :config="headerConfig"></product-header>
		<div class="speciality list-box">
			<p class="common_content__title">
				产品价值
			</p>
			<product-speciality :items="specialityList"></product-speciality>
		</div>
		<div class="flow list-box">
			<p class="common_content__title">PaaS平台介绍</p>
			<img style="width: 100%" src="../../assets/products/paas-intro.png" alt="">
		</div>
		<div class="product list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productSwipeList"></product-swipe-new>
		</div>
		<div class="scene list-box">
			<p class="common_content__title">蔬东坡PaaS的优势</p>
			<business-scenario :items="sceneList"></business-scenario>
		</div>
	</div>
</template>

<style scoped lang="scss">
.paas-page {
	.list-box {
		padding: 10px 30px;
	}
	.list-box:nth-child(odd) {
		background-color: #f6f9fe;
	}
	.enhancement-con {
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		grid-gap: 10px;
		.enhancement-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 14px;
			>img {
				width: 34px;
			}
			>.item-text {
				width: 105px;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
				>.rate {
					font-size: 16px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
				}
			}
		}
	}
	.speciality {
		.common_content__title {
			width: 270px;
		}
		:deep(.sp-item) {
			background-color: #f9fbff;
			>img {
				margin-left: 20px;
			}
		}
	}
	:deep(.scenario-item) {
		height: 140px;
		padding: 0 10px;
	}
}
</style>
