<script setup lang="ts">
import ProductHeader from '~/components/ProductHeader.vue'
import ProductSpeciality from '~/components/ProductSpeciality.vue'
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'
import businessBanner from '~/assets/products/business-banner.png'

import businessSpeciality1 from '~/assets/products/business-speciality-1.png'
import businessSpeciality2 from '~/assets/products/business-speciality-2.png'
import businessSpeciality3 from '~/assets/products/business-speciality-3.png'

import businessProduct1 from '~/assets/products/business-product-1.png'
import businessProduct2 from '~/assets/products/business-product-2.png'
import businessProduct3 from '~/assets/products/business-product-3.png'

useHead({
	title: '生鲜配送财务管理软件_蔬菜配送财务流程_业财一体化-蔬东坡',
	meta: [
		{ name: 'description', content: '蔬东坡业财一体化管理系统现已与微信、支付宝、用友T+、金蝶K3等第三方服务平台对接,实现企业在线支付结算及对账核账数据化管理,帮助企业实现进销存及财务数据一体化管理,数据互通输出运营财务报表。' },
		{
			name: 'keywords',
			content: '生鲜配送财务管理制度,蔬菜配送财务管理流程,生鲜配送财务软件哪个好',
		},
	],
});

const headerConfig = {
	title: '业财一体化',
	backImg: businessBanner,
	desc: '打破业务部门和财务部门由于传统工作分工造成的<br/>信息壁垒，实现数据共享，降低企业成本，减少经营风险',
}
const specialityList = [
	{
		title: '三流合一',
		desc: '整合商品信息流、物流、资金流',
		img: businessSpeciality1
	},
	{
		title: '业财一体',
		desc: '业务处理与财税管理、一体化管理',
		img: businessSpeciality2
	},
	{
		title: '集中管控',
		desc: '集团多公司集中做账，多元化运营',
		img: businessSpeciality3
	},
]
const productSwipeList = [
	{
		title: '应用场景',
		desc: ['集团化公司管控 多公司集中做账 多元化运营', '高效、准确、可控的业务管理', '应收应付 自动生成凭证 明细账汇总账一目了然', '财税系统打通 接入供应链金融 全方位赋能生鲜业财一体化'],
		img: businessProduct1,
	},
	{
		title: '供应链系统',
		desc: ['高效精准的业务处理，订单、采购、出入库、加工，多元化', '及时可控的应收应付管理，采购/客户对账、结算，账目清晰', '灵活真实的绩效管理管理，分拣/司机/业务员/工人等，提成', '精确集中的资金流水管理，销售收入/成本、营业外收入/成本，利润分析'],
		img: businessProduct2,
	},
	{
		title: '打通第三方财税系统',
		desc: [ '发票系统：航信/百望体系无缝对接，明细开票，税率开票，集团开票', '财务系统：金蝶/用友等主流版本系统打通，进销存及凭证同步，支撑多账套及集团模式', '供应链金融：与湖南银联机构达成战略合作目标，赋能生鲜企业稳步成长' ],
		img: businessProduct3
	}
]
</script>

<template>
	<div class="business-page">
		<product-header :dark="true" :config="headerConfig"></product-header>
		<div class="speciality list-box">
			<p class="common_content__title">
				产品价值
			</p>
			<product-speciality :items="specialityList"></product-speciality>
		</div>
		<div class="flow list-box">
			<img style="margin: 10px 0;width: 100%" src="../../assets/products/business-liucheng.png" alt="">
		</div>
		<div class="product list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productSwipeList"></product-swipe-new>
		</div>
	</div>
</template>

<style scoped lang="scss">
.business-page {
	.list-box {
		padding: 10px 30px;
	}
	.list-box:nth-child(odd) {
		background-color: #f6f9fe;
	}
	.enhancement-con {
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		grid-gap: 10px;
		.enhancement-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 14px;
			>img {
				width: 34px;
			}
			>.item-text {
				width: 105px;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
				>.rate {
					font-size: 16px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
				}
			}
		}
	}
	.speciality {
		.common_content__title {
			width: 270px;
		}
		:deep(.sp-item) {
			background-color: #f9fbff;
			>img {
				margin-left: 20px;
			}
		}
	}
	:deep(.scenario-item) {
		height: 140px;
		padding: 0 10px;
	}
}
</style>
