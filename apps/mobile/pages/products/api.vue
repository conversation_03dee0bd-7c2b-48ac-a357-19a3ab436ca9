<script setup lang="ts">
import ProductHeader from '~/components/ProductHeader.vue'
import ProductSpeciality from '~/components/ProductSpeciality.vue'
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'
import apiBanner from '~/assets/products/api-banner.png'

import apiSpeciality1 from '~/assets/products/api-speciality-1.png'
import apiSpeciality2 from '~/assets/products/api-speciality-2.png'
import apiSpeciality3 from '~/assets/products/api-speciality-3.png'

import apiProduct1 from '~/assets/products/api-product-1.png'
import apiProduct2 from '~/assets/products/api-product-2.png'

useHead({
	title: '生鲜财务API_生鲜配送软件开发_蔬东坡生鲜配送软件API-蔬东坡',
	meta: [
		{ name: 'description', content: '蔬东坡生鲜配送软件API,基于供应链生鲜ERP管理系统的开放平台,给蔬东坡客户提供对接第三方系统,现已与微信、支付宝、用友T+、金蝶K3等第三方服务平台对接,自定义业务流程等数据服务,帮助生鲜企业实现信息化管理,为企业降本增效。' },
		{
			name: 'keywords',
			content: '生鲜配送软件API,蔬东坡生鲜配送软件API,生鲜配送软件可打通财务软件吗',
		},
	],
});

const headerConfig = {
	title: '业务定制平台 api',
	backImg: apiBanner,
	desc: '基于生鲜食材供应链ERP管理系统的开放平台<br/> 可对接第三方系统，自研系统，自定义业务流程等',
}
const specialityList = [
	{
		title: '接口丰富',
		desc: '丰富接口帮助企业，满足个性化需求',
		img: apiSpeciality1
	},
	{
		title: '数据同步',
		desc: '业务数据互通，保障业务流转闭环',
		img: apiSpeciality2
	},
	{
		title: '稳定维护',
		desc: '自动化重新启动，方便管理维护',
		img: apiSpeciality3
	},
]
const productSwipeList = [
// 	•企业ERP：订单自动转入，避免重复录入，减少人工错误
// 实时查看库存，实时查看订单状态，降低沟通成本
// •自研系统：丰富接口帮助企业满足个性化需求

// 。服务能力：以同步策略规范业务标准，实现业务数据互通，
// 保障企业业务流转闭环
// •开放接口：提供基础资料、录单、采购以及库房等相关业务
// 几十种接口，支持对接企业自有系统
// •稳定维护：提醒通知数据异常、同步数据失败以及原因，自
// 动化重新启动，方便管理维护
	{
		title: '',
		desc: ['企业ERP：订单自动转入，避免重复录入，减少人工错误', '实时查看库存，实时查看订单状态，降低沟通成本', '自研系统：丰富接口帮助企业满足个性化需求'],
		img: apiProduct1,
	},
	{
		title: '',
		desc: ['服务能力：以同步策略规范业务标准，实现业务数据互通，', '保障企业业务流转闭环', '开放接口：提供基础资料、录单、采购以及库房等相关业务', '几十种接口，支持对接企业自有系统', '稳定维护：提醒通知数据异常、同步数据失败以及原因，自', '动化重新启动，方便管理维护'],
		img: apiProduct2,
	},
]
</script>

<template>
	<div class="api-page">
		<product-header :dark="true" :config="headerConfig"></product-header>
		<div class="speciality list-box">
			<p class="common_content__title">
				产品价值
			</p>
			<product-speciality :items="specialityList"></product-speciality>
		</div>
		<div class="flow list-box">
			<p class="common_content__title">PaaS平台介绍</p>
			<img  style="width: 100%" src="../../assets/products/paas-intro.png" alt="">
		</div>
		<div class="product list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productSwipeList"></product-swipe-new>
		</div>
		<div class="team-scheme-con list-box">
			<p class="common_content__title">协作方案</p>
			<div class="team-scheme">
				<div class="team-scheme-item">
					<img src="../../assets/products/api-team-1.png" alt="">
					<p>订单推送</p>
				</div>
				<div class="team-scheme-item">
					<img src="../../assets/products/api-team-2.png" alt="">
					<p>单据回传</p>
				</div>
				<div class="team-scheme-item">
					<img src="../../assets/products/api-team-3.png" alt="">
					<p>字段映射</p>
				</div>
				<div class="team-scheme-item">
					<img src="../../assets/products/api-team-4.png" alt="">
					<p>接口新增/查询</p>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.api-page {
	.list-box {
		padding: 10px 30px;
	}
	.list-box:nth-child(odd) {
		background-color: #f6f9fe;
	}
	.enhancement-con {
		width: 100%;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		grid-gap: 10px;
		.enhancement-item {
			width: 152px;
			height: 56px;
			background: #2977FE;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 14px;
			>img {
				width: 34px;
			}
			>.item-text {
				width: 105px;
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				text-align: center;
				>.rate {
					font-size: 16px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 500;
				}
			}
		}
	}
	.speciality {
		.common_content__title {
			width: 270px;
		}
		:deep(.sp-item) {
			background-color: #f9fbff;
			>img {
				margin-left: 20px;
			}
		}
	}
	.team-scheme-con {
		display: flex;
		flex-direction: column;
		align-items: center;
		.team-scheme {
			padding: 20px;
			display: grid;
			grid-gap: 24px;
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: repeat(2, 1fr);
			>.team-scheme-item {
				width: 110px;
				height: 110px;
				background-color: white;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-around;
				>img {
					width: 60px;
				}
				p {
					font-size: 12px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 14px;
				}
			}
		}
	}
	
	:deep(.scenario-item) {
		height: 140px;
		padding: 0 10px;
	}
}
</style>
