<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> qin<PERSON><PERSON><EMAIL>
 * @Date: 2023-08-02 10:00:00
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <CommonHeader>
    <template #title>
      <h1>团餐团膳食堂承包供应链<br/>解决方案</h1>
    </template>
    <template #desc>
      <p>颠覆传统管理模式，降低采购成本</p>
      <p>提升企业利润</p>
    </template>
    <template #img>
      <img class="tuancan_header__img" :src="descImg" alt="团餐团膳食堂承包供应链解决方案" />
    </template>
  </CommonHeader>
  <div class="tuancan">
    <div class="business_box">
      <p class="common_content__title">团餐团膳食堂承包供应链行业痛点</p>
      <div class="business_bg"></div>
    </div>

    <div class="solution_box">
      <p class="common_content__title">蔬东坡团餐团膳解决方案全流程</p>
      <div class="solution_box__content">
        <div class="solution_bg"></div>
      </div>
    </div>

    <div class="product_box">
      <p class="common_content__title">产品功能</p>
      <ProductSwipe :items="productList"></ProductSwipe>
    </div>

    <BenchmarkCasesTab :items="benchmarkCasesList"></BenchmarkCasesTab>
  </div>
</template>

<script setup>
import descImg from '~/assets/tuancan/tuancan_header_img_desc.png';

import footerBg from '~/assets/tuancan/tuancan_footer_bg.png';
import footerImgBHQY from '~/assets/tuancan/tuancan_footer_img_bhqy.png';
import footerImgHNJG from '~/assets/tuancan/tuancan_footer_img_hnjg.png';

import contentSwipeXTZDJD from '~/assets/tuancan/tuancan_content_swipe_xtzdjd.png';
import contentSwipeDFXJ from '~/assets/tuancan/tuancan_content_swipe_dfxj.png';
import contentSwipeSTKY from '~/assets/tuancan/tuancan_content_swipe_stky.png';
import contentSwipeXTZDDZ from '~/assets/tuancan/tuancan_content_swipe_xtzddz.png';
import contentSwipeDWSH from '~/assets/tuancan/tuancan_content_swipe_dwsh.png';
import CommonHeader from '~/components/CommonHeader.vue';

useHead({
	title: '团餐团膳食堂承包供应链解决方案_团膳管理软件_团膳管理系统-蔬东坡',
	meta: [
		{ name: 'description', content: '颠覆传统管理模式，降低采购成本，提升企业利润，让团膳食堂配送更高效可控' },
		{
			name: 'keywords',
			content: '团膳食堂管理,团膳食堂食材采购,团膳配送系统,团餐配送管理，团餐团膳食堂承包供应链解决方案',
		},
	],
});

const productList = [
  {
    title: '系统自动接单、拆单，供应商负责分拣发货节省50%人力成本',
    desc: [
      '告别传统手工、纸质接单方式，商城自主接单，效率提升100%',
      '系统自动将订单拆分给供应商，实现0出错',
      '由供应商负责分拣、配送，较少运营中心操作时间',
    ],
    img: contentSwipeXTZDJD,
  },
  {
    title: '多方询价，通过比价机制采购成本立省5%',
    desc: ['系统自动将采购需求发给不同的供应商', '通过供应商发过来的报价，系统自动择优推荐最优方案，降低采购价格'],
    img: contentSwipeDFXJ,
  },
  {
    title: '食堂可以查看各个档口的货品进出情况 可节省人为10%的货品损耗',
    desc: ['每个档口的商品进出情况、库存情况皆可以做到有据可查', '货品明细清晰，减少人为操作空间，减少私下拿货的情况'],
    img: contentSwipeSTKY,
  },
  {
    title: '系统自动对账，效率提升100%，准确率99.9%',
    desc: ['供应商发货后，系统就已经自动完成了对账', '无需安排1-2个人工去根据纸质单据去核对账目，既费时又容易出错'],
    img: contentSwipeXTZDDZ,
  },
  {
    title: '多维损耗报表助力配送企业降低损耗20%',
    desc: [
      '采购损耗、库房损耗、配送损耗实时掌控',
      '各项损耗趋势比重多维度清晰呈现',
      '助力配送企业精细化管控损耗，降低菜品损耗20%',
    ],
    img: contentSwipeDWSH,
  },
];
const benchmarkCasesList = [
  {
    title: '湖南嘉庚',
    desc: '株洲嘉庚、高科嘉庚同为湖南嘉赓供应链管理有限公司的子公司，二者同时承接共30多个项目点的配送业务，采取供应商直供的配送模式。与蔬东坡合作后，实现订单直采，在录入订单的同时绑定对应的直供供应商，自动生成对应的采购单、入库单；实现供应商直供的财务对账，筛选直采订单和供应商进行对账结算；通过账号权限控制可操作的仓库（可多选），独立核算该仓库业财数据',
    img: footerImgHNJG,
    bg: footerBg,
  },
  {
    title: '北海启悦',
    desc: '成立于2021年，专注学校食堂承包配送，年销售额近2000万。与蔬东坡合作后，上线小程序商城，客户可以在商城端直接下单。节省手动制单时间；之前所用系统无法实现多仓管理，手动汇总两个仓库的数据。通过蔬东坡可以实现一套系统多个账套，分开管控双仓库；项目点线上收货，收货数据自动同步至采购单，后台一键汇总收货数据',
    img: footerImgBHQY,
    bg: footerBg,
  },
];
</script>
<style lang="scss" scoped>
.business_box {
  padding: 30px 0px 0px;
}
.business_box .business_bg {
  width: auto;
  height: 330px;
  background: url('../assets/tuancan/tuancan_content_img_info.png') no-repeat;
  background-size: cover;
}
.solution_box {
  padding: 20px 0px 0px;
  background-color: #f9fbff;
}
.solution_box .solution_box__content {
  padding: 16px 35px 30px 35px;
}
.solution_box .solution_box__content .solution_bg {
  height: 175px;
  background: url('../assets/tuancan/tuancan_content_img_solution.png') no-repeat;
  background-size: 100% 100%;
}
.product_box {
  padding: 16px 0px 24px;
}
.product_box > p {
  margin-bottom: 17px;
}
:deep(.sidebar){
  min-height: 390px;
}
</style>
