<script lang="ts" setup>
import suyuanBanner from '~/assets/solutions/suyuanBanner.png'
import suyuanLiucheng1 from '~/assets/solutions/suyuan-liucheng-1.png'
import suyuanLiucheng2 from '~/assets/solutions/suyuan-liucheng-2.png'
import suyuanLiucheng3 from '~/assets/solutions/suyuan-liucheng-3.png'
import suyuanLiucheng4 from '~/assets/solutions/suyuan-liucheng-4.png'
import suyuanLiucheng5 from '~/assets/solutions/suyuan-liucheng-5.png'
import suyuanLiucheng6 from '~/assets/solutions/suyuan-liucheng-6.png'
import suyuanFuneng1 from '~/assets/solutions/suyuan-funeng-1.png'
import suyuanFuneng2 from '~/assets/solutions/suyuan-funeng-2.png'
import suyuanFuneng3 from '~/assets/solutions/suyuan-funeng-3.png'
import suyuanFuneng4 from '~/assets/solutions/suyuan-funeng-4.png'
import suyuanProduct1 from '~/assets/solutions/suyuan-product-1.png'
import suyuanProduct2 from '~/assets/solutions/suyuan-product-2.png'
import suyuanProduct3 from '~/assets/solutions/suyuan-product-3.png'
import suyuanProduct4 from '~/assets/solutions/suyuan-product-4.png'
import suyuanMap1 from '~/assets/solutions/suyuan-map-1.png'
import suyuanMap1b from '~/assets/solutions/suyuan-map-1-b.png'
import suyuanMap2 from '~/assets/solutions/suyuan-map-2.png'
import suyuanMap2b from '~/assets/solutions/suyuan-map-2-b.png'
import suyuanMap3 from '~/assets/solutions/suyuan-map-3.png'
import suyuanMap3b from '~/assets/solutions/suyuan-map-3-b.png'

import CommonHeader from '~/components/CommonHeader.vue';
import ProductSwipeNew from '~/components/ProductSwipeNew.vue'

useHead({
	title: '农产品溯源系统解决方案_农产品追溯系统_食品安全-蔬东坡',
	meta: [
		{ name: 'description', content: '蔬东坡农产品溯源追溯系统,农产品质量追溯系统' },
		{ name: 'keywords', content: '农产品溯源,农产品溯源系统,农产品追溯系统,食品安全溯源' },
	],
});

const handleShowModal = openModal;

const productList = [
	{
		title: '打造生鲜食材专属档案',
		desc: [
			'一物一码，为每份农严品制作 独一无二的溯源档案',
			'涵盖多个检验报告（溯源编码、商品信息、农残检测报告）'
		],
		img: suyuanProduct1
	},
	{
		title: '一码实现全流程追踪',
		desc: [
			'打破生鲜流通中的信息时差 上、下游环节检测报告皆可查',
			'保障采集数据真实性，杜绝 假冒信息混入、篡改'
		],
		img: suyuanProduct2
	},
	{
		title: '3分钟快速实现农产检测',
		desc: [
			'一台设备可满足全部质检需求 检测数据一键打印',
			'快速搭建检测中心，以低成本 获取食品安全检测信息'
		],
		img: suyuanProduct3
	},
	{
		title: '检测数据公开透明',
		desc: [
			'数据大屏，一屏可将溯源信息 与检测信息公开展示',
			'与食品检测设备打通，全流程 跟踪商品流通状况'
		],
		img: suyuanProduct4
	},
]
const flowList = [
	{
		title: '原料',
		desc: '产地品质 可追溯',
		img: suyuanLiucheng1
	},
	{
		title: '生产',
		desc: '加工车间 精准管理',
		img: suyuanLiucheng2
	},
	{
		title: '分拣',
		desc: '智能匹配 自动打单',
		img: suyuanLiucheng3
	},
	{
		title: '仓储',
		desc: '半成品/成品 严格管控',
		img: suyuanLiucheng4
	},
	{
		title: '配送',
		desc: '运输检测 实时监控',
		img: suyuanLiucheng5
	},
	{
		title: '门店',
		desc: '终端消费 复检追踪',
		img: suyuanLiucheng6
	},
]
const empowerList = [
	{
		title: '降低成本',
		desc: '原料、领料、加工、退料<br/> 各环节严控',
		img: suyuanFuneng1
	},
	{
		title: '责任可追',
		desc: '各环节精准掌握<br/> 来源可追去向可查',
		img: suyuanFuneng2
	},
	{
		title: '环节管控',
		desc: '实时采集产品数据 <br/>了解其流通情况',
		img: suyuanFuneng3
	},
	{
		title: '用户信赖',
		desc: '扫码即可展示当前 <br/>商品溯源档案',
		img: suyuanFuneng4
	}
]
const mapList = [
	{
		title: '食堂配送企业',
		desc: '大规模人群 就餐场所',
		img: suyuanMap1,
		icon: suyuanMap1b
	},
	{
		title: '超市配送企业',
		desc: '大众消费者食材采购 主要渠道',
		img: suyuanMap2,
		icon: suyuanMap2b
	},
	{
		title: '生鲜电商平台',
		desc: '线上买菜、送菜 到家备受欢迎',
		img: suyuanMap3,
		icon: suyuanMap3b
	}
]

</script>
<template>
	<CommonHeader>
		<template #title>
			<h1>农产品溯源解决方案</h1>
		</template>
		<template #desc>
			<p>一品一码技术扫码溯源 来源可查</p>
			<p>去向可追、责任可究、成本可控</p>
		</template>
		<template #img>
			<img class="tuancan_header__img" :src="suyuanBanner" alt="农产品溯源解决方案" />
		</template>
	</CommonHeader>
	<div class="suyuan">
		<div class="business_box list-box">
			<p class="common_content__title">从源头开始 全程一码追溯</p>
			<div class="flow-con">
				<div class="flow-item" v-for="item in flowList" :key="item.title">
					<div class="img-con">
						<img :src="item.img" :alt="item.title">
					</div>
					<p class="title">{{ item.title }}</p>
					<p class="desc">{{ item.desc }}</p>
				</div>
				<div class="solid-line"></div>
			</div>
		</div>
		
		<div class="solution_box list-box">
			<p class="common_content__title">农产品溯源 数字化管理全面赋能</p>
			<div class="empower-list">
				<div class="em-item" v-for="item in empowerList">
					<div class="img-con">
						<img :src="item.img" alt="">
					</div>
					<p class="title">{{ item.title }}</p>
					<p class="desc" v-html="item.desc"></p>
				</div>
			</div>
		</div>
		
		<div class="product-box list-box">
			<p class="common_content__title">产品功能</p>
			<product-swipe-new :items="productList"></product-swipe-new>
		</div>
		
		<div class="client-value list-box">
			<p class="common_content__title">
				以客户价值为核心的全生<br/>命周期服务
			</p>
			<div class="client-con">
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-1.png" alt="">
					<p>企业级定制</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-2.png" alt="">
					<p>7x24小时服务</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-3.png" alt="">
					<p>1v1系统顾问</p>
				</div>
				<div class="client-item">
					<img src="@/assets/solutions/suyuan-client-4.png" alt="">
					<p>一线实施专家</p>
				</div>
			</div>
			<button @click="handleShowModal">免费试用</button>
		</div>
		
		<div class="map-box">
			<div class="map-item" v-for="item in mapList" :key="item.title">
				<img :src="item.img" alt="">
				<div class="map-text">
					<div class="img-con">
						<img class="icon" :src="item.icon" alt="">
					</div>
					<p class="title">{{ item.title }}</p>
					<p class="desc">{{ item.desc }}</p>
				</div>
			</div>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.suyuan {
	.business_box {
		.flow-con {
			display: flex;
			justify-content: space-between;
			position: relative;
			>.flow-item {
				position: relative;
				z-index: 1;
				display: flex;
				width: 40px;
				flex-direction: column;
				align-items: center;
				.img-con {
					width: 40px;
					height: 40px;
					background: #2977FE;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					>img {
						width: 19px;
					}
				}
				.title {
					margin: 10px 0;
					font-size: 14px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 16px;
				}
				.desc {
					width: 50px;
					text-align: center;
					font-size: 10px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					color: #666666;
					line-height: 15px;
				}
			}
			>.solid-line {
				z-index: 0;
				top: 20px;
				width: 100%;
				position: absolute;
				border-top: 1px dashed #2977FE;
			}
		}
	}
	.list-box:nth-child(even) {
		background-color: #f6f9fe;
	}
	.solution_box {
		display: flex;
		flex-direction: column;
		align-items: center;
		.empower-list {
			display: grid;
			grid-gap: 18px;
			grid-template-rows: repeat(2, 1fr);
			grid-template-columns: repeat(2, 1fr);
			.em-item {
				background-color: white;
				width: 110px;
				height: 110px;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 10px;
				>.img-con {
					border-radius: 50%;
					width: 40px;
					height: 40px;
					background: #2977FE;
					display: flex;
					align-items: center;
					justify-content: center;
					>img {
						width: 30px;
					}
				}
				.title {
					font-size: 12px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					color: #333333;
					line-height: 14px;
					margin: 4px 0;
				}
				.desc {
					font-size: 8px;
					font-family: PingFang SC, PingFang SC;
					font-weight: 400;
					text-align: center;
					color: #333333;
					line-height: 12px;
				}
			}
		}
	}
	.client-value {
		.client-con {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			grid-template-rows: repeat(2, 1fr);
			grid-gap: 16px;
			>.client-item {
				width: 152px;
				height: 56px;
				background: #2977FE;
				box-shadow: 2px 4px 4px 0px rgba(59,105,220,0.05);
				border-radius: 5px 5px 5px 5px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 14px;
				font-size: 14px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				line-height: 16px;
				>img {
					width: 36px;
				}
			}
		}
		>button {
			width: 174px;
			height: 27px;
			border-radius: 5px;
			background-color: #FF9607;
			margin: 0 auto;
			display: block;
			color: white;
			font-size: 12px;
			margin-top: 16px;
		}
	}
	
	.map-box {
		padding: 25px;
		width: 100%;
		overflow-x: auto;
		overflow-y: hidden;
		display: flex;
		gap: 24px;
		>.map-item {
			flex-shrink: 0;
			width: 120px;
			height: 186px;
			background: #FFFFFF;
			box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.1);
			border-radius: 5px 5px 5px 5px;
			text-align: center;
			>img {
				width: 100%;
				position: relative;
				z-index: 1;
			}
			.title {
				font-size: 12px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 28px;
			}
			.desc {
				font-size: 8px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				color: #333333;
				line-height: 9px;
			}
			.map-text {
				text-align: center;
				z-index: 2;
				>.img-con {
					position: relative;
					z-index: 2;
					margin: 0 auto;
					margin-top: -20px;
					width: 32px;
					height: 32px;
					background: #2977FE;
					border-radius: 50%;
					box-shadow: 2px 2px 2px 0px rgba(15,56,102,0.05);
					display: flex;
					align-items: center;
					justify-content: center;
					>img {
						width: 15px;
					}
				}
			}
		}
	}
}
</style>
