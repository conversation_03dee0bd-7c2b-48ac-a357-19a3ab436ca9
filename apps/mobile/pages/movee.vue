
<template>
  <div class="khal-page">
    <div class="khal-header">
      <img src="../assets/khal/banner.png" alt="">
      <div class="header-content">
        <p>30万+生鲜企业的共同选择</p>
      </div>
    </div>
    <div class="customer">
      <van-swipe style="height: 128px;" vertical :autoplay="3000" :show-indicators="false">
        <van-swipe-item v-for="item in customerList" :key="item.name">
          <div class="customer-item">
<!--            <span class="char">“</span>-->
<!--            <span class="char">”</span>-->
            <div class="customer-item__content">
              <img :src="item.image" alt="">
              <div class="customer-item__info">
                <div class="cus-name">
                  <p class="user-name">
                    {{ item.name }}
                  </p>
                  <p>{{ item.standing }}</p>
                </div>
                <div class="customer-des">
                  {{ item.des }}
                </div>
                <p></p>
              </div>
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </div>

    <div class="solution_box">
      <p class="common_content__title">标杆案例</p>
      <!-- <div class="company-list">
        <div class="company-item" v-for="item in dataList" :key="item?.ID">
          <img :src="item.conver_image" :alt="item.post_title" class="company-item-img">
          <div class="item-info">
            <p class="name">{{ item.post_title }}</p>
            <p class="introduce"><span class="tip">赋能助力：</span><span class="des">{{ item.cooperation_value }}</span></p>
            <p class="article-detail__line"></p>
            <p @click.stop class="article-detail__tags">
                  <div v-if="item.business_type.length" class="tags_container">
                    <span class="tags__item">#{{item.business_type[0]}}</span>
                    <span class="tags__item" @click="handleLookMore(item)">详情 ></span>
                  </div>
            </p>
          </div>
        </div>
      </div> -->
    <CustomerList v-show="dataList && dataList.length" :data-source="dataList"></CustomerList>
    </div>
  </div>
</template>

<script setup lang="ts">
import CustomerList from '@/components/CustomerList.vue';
import renwu1 from '@/assets/khal/renwu1.png';
import renwu2 from '@/assets/khal/renwu2.png'; 
import renwu3 from '@/assets/khal/renwu3.png';
import renwu4 from '@/assets/khal/renwu4.png';
import gongsi1 from '@/assets/khal/company1.png';
import gongsi2 from '@/assets/khal/company2.png';
import gongsi3 from '@/assets/khal/company3.png';
import gongsi4 from '@/assets/khal/company4.png';
import gongsi5 from '@/assets/khal/company5.png';
import gongsi6 from '@/assets/khal/company6.png';
import gongsi7 from '@/assets/khal/company7.png';
import gongsi8 from '@/assets/khal/company8.png';
import gongsi9 from '@/assets/khal/company9.png';
import gongsi10 from '@/assets/khal/company10.png';
// import { onMounted } from 'vue';


const customerList = [
  {
    name: '康栋梁',
    image: renwu1,
    standing: '重庆幼鲜通CEO',
    des: '很高兴在生鲜配送的道路上能遇到像蔬东坡这样的合作伙伴，多年携手，共生共长，从创业初期到发展壮大的各个阶段，蔬东坡见证了幼鲜通“从0到1”的成长。'
  },
  {
    name: '杨彬',
    image: renwu3,
    standing: '长沙彬果科技有限公司',
    des: '从事生鲜行业5年多，与蔬东坡合作两年。蔬东坡让我们能够监控到各个板块的数据，从而来降低成本。蔬东坡系统操作简单，让我们减少分拣的出错。定期回访，解决我们的需求。'
  },
  {
    name: '胡坤龙',
    image: renwu2,
    standing: '长沙全尔美农产品贸易有限公司',
    des: '从事生鲜行业7年，与蔬东坡合作5年。用了蔬东坡后，流程更加标准化，有信心扩大规模。蔬东坡系统操作很简单、很方便，工程师每次上门培训都很有耐心，能帮我们很快的解决问题。'
  },
  {
    name: '纪晓勇',
    image: renwu4,
    standing: '长沙创强供应链管理有限公司',
    des: '从事生鲜行业6年，与蔬东坡合作4年。蔬东坡大大提升了我们的工作效率，从采购、分拣、仓储到结算提升人效。蔬东坡系统容易上手，提升工作效率。最打动我的是蔬东坡的售后服务，每次遇到问题都会细致耐心的帮我们解决。'
  }
]

let companyList = reactive([
  // {
  //   name: '古梯田 (北京) 农产品供应链管理有限公司',
  //   image: gongsi1,
  //   des: '1.按用户习惯生成常买清单，订单可直接批量复制。各环节数据同步，后台自动更改;2.分拣任务落实到个人，随时查看分拣进度;3.内置地图，自动分配线路和司机，自动规划最优线路。'
  // },
  // {
  //   name: '山东食安公社食品科技有限责任公司',
  //   image: gongsi2,
  //   des: '1.规范化管控，损耗率大大下降，各环节数据清晰，原料用量、出成率一目了然;2.系统自动生成多级报表，有效体现企业经营状况、辅助决策。'
  // },
  // {
  //   name: '贵州吉马供应链管理有限责任公司',
  //   image: gongsi3,
  //   des: '1.采用“公司+农户”的形式，根据订单确定种植数量，高产高效;2.通过线上商城下单，产品信息一目了然，减少退货、补货情况;3.分拣后通过软件物流排线，排线效率提升50%以上。'
  // },
  // {
  //   name: '广西吴越农产品配送有限公司',
  //   image: gongsi4,
  //   des: '1.财务报表数据准确性比之前提升30%，财务人效提高50%;2.采购损耗、库房损耗、退货损耗多层管控,损耗率减少5%。'
  // },
  // {
  //   name: '杭州春盎农副产品贸易有限公司-菜无忧',
  //   image: gongsi5,
  //   des: '1.营业额大幅提升：上线不到一年，日营业额翻3番，日流水超25万;2.效率大幅提升：PDA扫码装车极大降低出错率，减少90%的找货时间。优化分拣场地布局，效率提升了1.5倍，人均日分拣金额2万+。'
  // },
  // {
  //   name: '浙江森马现代农业发展有限公司',
  //   image: gongsi6,
  //   des: '1.内部部门沟通更协调，沟通效率大为提升，报表经营数据实现可视化，管理层决策更清晰;2.各种销售情况、财务毛利、库存管理数据等实时输出，运营效率大步提升。'
  // },
  // {
  //   name: '重庆百姓之家农业发展有限公司 (幼鲜通）',
  //   image: gongsi7,
  //   des: '1.全面实现数据化管理和运营，上线一键下单、智能分拣、物流排线、溯源系统等各项功能，提升工作效率，确保规格和重量准确;2.数据云端存储，异地查询，实现简单、清晰的数据化管理。'
  // },
  // {
  //   name: '陕西米禾供应链管理股份有限公司',
  //   image: gongsi8,
  //   des: '1.科学管理进存销，库存预警，先进先出，全流程记录库存变动。订单自动同步库存信息，自动追踪损耗情况，生成多形式报表;2.物流排线+司机APP，系统自动排线后同步司机端。打通各环节财务数据，退换货数据自动同步。'
  // },
  // {
  //   name: '江西兵哥送菜实业有限公司',
  //   image: gongsi9,
  //   des: '1.在分拣环节的提速增效效果显著;2.采购数据准确率高达100%，各环节数据清晰准确，人力、物力成本实现损耗下降16%，成本下降40%，整体毛利提升20%。'
  // },
  // {
  //   name: '武汉联农生鲜食品加工配送有限公司',
  //   image: gongsi10,
  //   des: '1.录单精准快速，报表实时查询，支持导出，每天都能精细化核算成本数据;2.分拣效率提升了2倍，损耗下降了7%，数据准确率高达100%。'
  // }
])
let dataList = ref([])
// onMounted(() => {
//   // getData()
// })
// const getData = async () => {
  const res = await getStudyCenterPages('客户案例')
  // const res2 = await getNewsDetail('40812');
  // console.log('res2', res2);
  if ( res ) {
    dataList.value = res?.data?.list || []
    // console.log('dataList',dataList);
  }
// }


//写一个方法能将des处理成多行, 中文分号英文分号都要处理
function insertNewLines(text: string) {
  return text.replace(/;/g, ';<br/>');
}

</script>

<style scoped lang="scss">
.khal-page {

  .khal-header {
    position: relative;
    height: 162px;
    z-index: 1;

    >img {
      position: relative;
      width: 100%;
    }

    >.header-content {
      text-align: center;
      position: absolute;
      z-index: 2;
      left: 0;
      top: 0;
      height: 100%;
      width: 100%;
      padding-top: 39px;

      p {
        font-weight: 600;
        font-size: 24px;
        color: #FFFFFF;
        line-height: 28px;
        letter-spacing: 2px;
      }
    }


  }

  .customer {
    margin: 0 auto;
    width: 343px;
    height: 128px;
    background-color: white;
    box-shadow: 1px 1px 2px 1px rgba(28, 102, 231, 0.1);
    border-radius: 5px 5px 5px 5px;
    overflow: hidden;
    margin-top: -60px;
    z-index: 2;
    position: relative;

    .customer-item {
      display: flex;
      background-color: white;
      justify-content: space-between;
      align-items: center;
      padding: 10px 14px;
      font-family: PingFang SC, PingFang SC;
      position: relative;
      top: 50%;
      transform: translateY(-50%);
			overflow: visible;
     
      .customer-item__content {
        display: flex;
        align-items: center;

        img {
          width: 80px;

        }

        .customer-item__info {
          margin-left: 10px;

          .cus-name {
            position: relative;
            display: flex;
            // 底部对齐
            align-items: flex-end;
            p {
              font-weight: 600;
              font-size: 9px;
              color: #2977FE;
            }

            .user-name {
              font-size: 14px;
              color: #2977FE;
              margin-right: 6px;
              position: relative;
              &::before {
                position: absolute;
                left: -53%;
                top: -6px;
                font-size: 24px;
                content: "“";
              }
            }
          }

          .customer-des {
						overflow: visible;
						padding-right: 10px;
            margin-top: 4px;
            font-weight: 600;
            font-size: 10px;
            color: #676767;
            position: relative;
            &::after {
              position: absolute;
              right: -4%;
              bottom: -20px;
              font-size: 24px;
              content: "”";
              color: #2977FE;
            }
          }

        }
      }
    }
  }
  
  .company-list {
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding-top: 0;
    >.company-item {
      border-radius: 5px;
      width: 162px;
      box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
      >img {
        width: 100%;
        height: 103px;
        padding: 8px;
        border-radius: 3px;
      }
      >.item-info {
        padding: 8px;
        >.name {
          font-weight: 600;
          font-size: 10px;
          color: #333333;
        }
        .introduce{
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          max-width: 100%;
          font-weight: 400;
          font-size: 9px;
          margin-top: 6px;
        }
        .tip {
          color: #2977FE;
        }
        .des {
          color: #666666;
        }
        .article-detail__line {
          height: 0.5px;
          margin: 12px 0;
          background: #E6E6E6;
        }
        .tags_container {
          display: flex;
          justify-content: space-between;
          .tags__item {
            color: #057FF1FF;
            font-weight: 400;
            font-size: 9px;
          }
        }
      }
    }
  }
}
</style>
