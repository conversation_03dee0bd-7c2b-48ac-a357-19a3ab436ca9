<!--
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 13:53:12
 * @LastEditors: ddcoder <EMAIL>
 * @LastEditTime: 2023-03-17 17:33:53
-->
<!-- <script setup>
</script> -->
<template>
  <div>
    <TheHeader />
		<div class="page-content">
			<div @click="menuHandle()" v-if="showMenu" class="mask" ></div>
			<slot  />
		</div>
		<customer-scroll v-if="!$route.path.includes('gxst') && !$route.path.includes('shian')"></customer-scroll>
    <TheFooter />
    <FormModal />
    <DatumFormModal></DatumFormModal>
  </div>
</template>
<style scoped>
</style>
<script setup lang='ts'>
import DatumFormModal from '~/components/DatumFormModal.vue';
import BenchmarkCasesTab from '~/components/BenchmarkCasesTab.vue'
import CustomerScroll from '~/components/CustomerScroll.vue'
</script>

<style lang="scss">
* {
	box-sizing: border-box;
}
button {
	outline: none;
	border: none;
}
@keyframes showMask {
  0% { opacity: 0 }
  100% { opacity: 1 }
}
.mask {
	position: fixed;
	width: 100%;
  opacity: 0;
	height: 100%;
	background-color: rgba(0,0,0,.2);
	z-index: 9;
  animation: showMask .3s ease-out forwards;
}
.mask-active {
  opacity: 1;
}

.list-box {
	min-height: 100px;
	background-color: white;
	padding: 24px;
}
.overflow-hidden {
	overflow: hidden;
}
.flex {
	display: flex;
}
</style>
