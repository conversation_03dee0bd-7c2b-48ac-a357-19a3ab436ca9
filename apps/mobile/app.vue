<!--
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 10:32:19
 * @LastEditors: hgj
 * @LastEditTime: 2023-05-30 17:06:13
-->
<script setup>
const route = useRoute();

const funcStr = `(function () {
  var userAgent = window.navigator.userAgent;
  var isPc =
    !/phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone/i.test(
      userAgent,
    );

  if (isPc) {
    var path = window.location.pathname;
    var pcPath = path;
    if (path.startsWith("/shucai")) {
      pcPath = "/shicai";
    }
    if (path.startsWith("/roulei")) {
      pcPath = "/roujg";
    }
    var host = window.location.host;
    var pcUrl  = host.includes("test")? 'https://testwww.movee.cn'+pcPath: 'https://www.sdongpo.com' + pcPath;
    window.location.href = pcUrl
  }
})()`
useHead({
  htmlAttrs: {
    lang: "en",
  },
  charset: "utf-8",
  title: "蔬东坡生鲜配送软件系统,蔬菜食材配送系统,生鲜SaaS软件服务平台-蔬东坡",
  meta: [
    {
      name: "description",
      content:
        "蔬东坡提供专业的生鲜供应链SaaS解决方案，包括生鲜配送系统、蔬菜配送软件、食材配送软件等，为超过10000家生鲜配送企业提供数智服务",
    },
    { name: "keywords", content: "生鲜配送系统,生鲜配送软件,蔬东坡" },
    {
      name: "viewport",
      content: "width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no",
    },
    { name: 'applicable-device', content: 'mobile' },
    { 'http-equiv': 'Cache-Control', content: 'no-transform' },
    { 'http-equiv': 'Cache-Control', content: 'no-siteapp' }
  ],
  link: [
    {
      rel: "icon",
      href: "https://website-image.sdongpo.com/website/favicon-new.ico",
    },
    {
      rel: "canonical",
      href: `https://www.sdongpo.com${route.path}`,
    },
  ],
  script: [
    {
      src: "https://hm.baidu.com/hm.js?969e51f6327eb24d4924b994fb0df032",
      bodyClose: false,
      async: true,
      defer: true,
    },
    {
      innerHTML: funcStr,
      tagPosition: 'head'

    },
  ],
});

datumForm.getDataGram();
const showModal = datumForm.noticeDialog;
const activeGramDetail = datumForm.activeGramDetail;
const closeModal = () => {
  showModal.value = false;
};

onMounted(() => {
  setBaiduUrl();
});
</script>
<template>
  <NuxtLayout>
    <NuxtLoadingIndicator />
    <NuxtPage />
    <div class="datum-dialog">
      <van-dialog
        v-model:show="showModal"
        :z-index="100"
        title="资料获取"
        width="89.33vw"
        :overlayStyle="{ backgroundColor: 'rgba(0,0,0,.2)' }"
        :cancel="
          () => {
            datumForm.noticeDialog = false;
          }
        "
        :showConfirmButton="false"
      >
        <div class="modal__header">
          <img
            @click="closeModal"
            class="modal__close"
            src="https://website-image.sdongpo.com/website/icon--close.png"
            alt="关闭"
          />
        </div>
        <div v-if="activeGramDetail.post_content" class="post-content" v-html="activeGramDetail.post_content"></div>
      </van-dialog>
    </div>
  </NuxtLayout>
</template>
<style lang="postcss">
@import url(~/assets/style/common.css);
html,
body,
#__nuxt {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

.datum-dialog .van-dialog__header {
  padding-top: 6px;
}

.datum-dialog .modal__header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #047eef;
  position: relative;
}

.datum-dialog .modal__header .modal__close {
  position: absolute;
  width: 10px;
  top: -15px;
  right: 10px;
  cursor: pointer;
}

.datum-dialog .post-content {
  min-height: 200px;
  padding: 20px;
  word-break: break-all;
  word-wrap: break-word;
}
</style>
