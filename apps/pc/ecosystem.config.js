module.exports = {
    apps: [
      {
        name: 'nuxt-app',
        script: '.output/server/index.mjs',
        node_args: '--max-old-space-size=4096', // 内存限制设为 4GB
        instances: 'max', // 根据 CPU 核心数启动最大实例数
        autorestart: true, // 出现问题时自动重启
        watch: false, // 不监控文件变化
        env: {
          NODE_ENV: 'production', // 设置为生产环境
          PORT: process.env.PORT || 3000, // 使用外部传入的端口，默认值为3000
        },
        env_test: {
          NODE_ENV: 'test', // 设置为测试环境
          PORT: process.env.PORT || 4000, // 使用外部传入的端口，默认值为4000
        },
      },
    ],
  };
  