<template>
  <div class="news_content text-center">
    <ul class="news_content__category inline-flex items-center flex-wrap justify-start">
      <NuxtLink
        v-for="(category, index) in categoryList"
        :key="index"
        class="news_content__category--item"
        :class="{ 'sdp-tag': currentCategory === category.slug }"
        :to="`/xueyuan/${category.slug}`"
      >
        {{category.name}}
      </NuxtLink>
    </ul>
    <div class="news_content__main">
      <ArticleList :list="dataList" :keyMap="keyMap" @on-category-change="hadnleCateChange"></ArticleList>
    </div>
    <div class="news_content__pagination">
      <el-pagination
        prev-text="上一页"
        next-text="下一页"
        layout="slot"
        :page-size="9"
        :pager-count='5'
        :current-page="currentPage"
        :total="totalPage"
        @current-change="() => {}"
      >
        <button type="button" class="btn-prev is-first" :disabled="currentPage <= 1">
          <NuxtLink :to="getLink(currentPage - 1)">
            上一页
          </NuxtLink>
        </button>
        <NewsPager
          :current-page="currentPage"
          :page-count="pageCount"
          :pager-count='5'
        >
          <template #page-num="pager">
            <NuxtLink :to="getLink(pager.number)">
              {{ pager.number }}
            </NuxtLink>
          </template>
          <template #arrow-left>
            <NuxtLink :to="getLink(currentPage - 3)">
              <d-arrow-left/>
            </NuxtLink>
          </template>
          <template #arrow-right>
            <NuxtLink :to="getLink(currentPage + 3)">
              <d-arrow-right/>
            </NuxtLink>
          </template>
        </NewsPager>
        <button type="button" class="btn-next is-last" :disabled="currentPage === pageCount || pageCount === 0">
          <NuxtLink :to="currentPage === pageCount || pageCount === 0 ? '' : getLink(currentPage + 1)" >
            下一页
          </NuxtLink>
        </button>
      </el-pagination>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"

const route = useRoute()
const category = route?.params?.category || ''
const num = route?.params?.num || 1

const keyMap = ref({
  title: 'post_title',
  img: 'thumbnail',
  date: 'post_date_format',
  tag: 'post_category_name'
})

let currentCategory = ref(category)
let currentPage = ref(+num)
let totalPage = ref(9)
let pageCount = ref(1)
let categoryList = ref([])
let dataList = ref([])

let categoryInfo = useState('category', () => ({}))

const getList = async () => {
  const res = await getNewsList(currentCategory.value, currentPage.value)
  totalPage.value = res?.data?.total || 9
  pageCount.value = Math.max(1, Math.ceil(totalPage.value / 9))
  dataList.value = (res?.data?.list || []).map(item => {
    if (!item.post_category_name) item.post_category_name = []
    if (item.post_category_name[0]) {
      item._category = item.post_category_name[0]
      item._slug = (categoryList.value.find(cat => cat.cat_name === item.post_category_name[0]) || {}).slug || ''
    }
    return item
  })
}
const getCategory = async () => {
  const data = await getNewsCategory()
  if (data?.status === 1) {
    categoryList.value = data?.data?.map(item => {
      item.slug = decodeURIComponent(item.slug)
      return item
    })
    categoryInfo.value = {
      name: (categoryList.value.find(item => item.slug === category) || {}).cat_name || '',
      slug: currentCategory.value
    }
  }
}
getCategory()
getList()
// 用于取最大页号和最大id拦截非法请求
getListOrderByIdDesc()

const getLink = (page) => {
  return `/xueyuan/${category ? category + '/' : ''}${page > 1 ? 'page-' + page + '/' : ''}`
}
const hadnleCateChange = (item) => {
  const category = categoryInfo.value
  categoryInfo.value = {
    name: category.slug ? category.name : item.name,
    slug: category.slug || item.slug
  }
}
</script>

<style lang="postcss" scoped>
.news_content {
  color: #333;
  padding: 40px 0 170px;
  background: #f9fbff;

  &__category {
    max-width: 1120px;
    margin-bottom: 50px;

    &--item {
      padding: 2px 10px;
      margin-top: 10px;
      font-size: 18px;
      cursor: pointer;
      &:not(:last-child) {
        margin-right: 30px;
      }
    }
  }
  &__main {
    text-align: left;
  }
  &__pagination {
    padding-top: 30px;
    color: #666;
    font-size: 14px;

    &--number {
      position: relative;
      color: #666;
      background: #f9fbff;
      margin-left: 20px;
    }

    /deep/ .el-pagination {
      justify-content: center;

      button, button:disabled, .el-pager li {
        position: relative;
        color: #666;
        background: #f9fbff;
        & a {
          pointer-events: auto;
        }
      }
      button:disabled a {
        cursor: not-allowed;
      }
      .el-pager li.is-active {
        color: #2977fe;
        font-weight: 400;
        &::after {
          content: '';
          position: absolute;
          bottom: 6px;
          width: 12px;
          height: 2px;
          background-color: #2977fe;
        }
      }
    }
  }
}
</style>