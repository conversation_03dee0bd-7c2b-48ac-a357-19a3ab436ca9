<template>
  <div class="btn-wrap">
    <div class="nav-btn" @mouseover="loopSceneCode" @mouseleave="clearLoop" @click="openTab">{{ btnText }}</div>
    <div class="wxcode-container" v-show="showCode">
      <img class="wxcode-image" v-show="qrcode" :src="qrcode" alt="扫码体验" />
      <div class="wxcode-desc">微信扫码，免费注册体验</div>
    </div>
  </div>
</template>
<script setup>
  const qrcode = ref('');
  const btnText = ref('扫码体验');
  const showCode = ref(false);
  let authUrl = '';
  let sceneCode = '';
  let timer = 0;
  let looping = false;
  const getSceneCode = async () => {
    const data = await $fetch('/index/scene-code', {
      method: 'POST',
      server: false,
    });
    if (data.status === 'success') {
      qrcode.value = data.data.images;
      sceneCode = data.data.scene_code;
      console.log(data.data);
    }
  };
  async function openTab() {
    if (!authUrl) {
      showError('请先扫码绑定');
      return;
    }
    await getSceneCode();
    btnText.value = '扫码体验';
    showCode.value = true;
    const form = document.createElement('form');
    form.action = authUrl;
    form.target = '_blank';
    form.method = 'GET';
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
    authUrl = '';
  }
  const clearLoop = () => {
    showCode.value = false;
    clearInterval(timer);
    looping = false;
  };
  const loopSceneCode = () => {
    if (!authUrl) {
      showCode.value = true;
    }
    clearInterval(timer);
    if (looping || !sceneCode) {
      return false;
    }
    looping = true;
    timer = setInterval(async () => {
      if (!sceneCode) {
        clearInterval(timer);
        return false;
      }
      let res = await $fetch('/index/scene-code-result', {
        method: 'POST',
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
        },
        server: false,
        body: new URLSearchParams({
          scene_code: sceneCode,
        }),
      });
      try {
        res = JSON.parse(res);
      } catch (err) {
        console.error(err);
      }
      if (res.status === 'success') {
        looping = false;
        sceneCode = '';
        showCode.value = false;
        clearInterval(timer);
        authUrl = res.data.url;
        btnText.value = '体验入口';
      }
    }, 2000);
  };
  getSceneCode();
</script>
<style lang="postcss" scoped>
  .wxcode-container {
    position: absolute;
    top: 50px;
    z-index: 9999;
    width: 200px;
    left: 50%;
    transform: translateX(-50%);
    color: #091e42;
    font-size: 14px;
    box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    background: #fff;
    padding: 10px;
    display: none;
  }

  .wxcode-container {
    display: none;
  }

  .wxcode-desc {
    text-align: center;
  }

  .wxcode-image {
    width: 180px;
  }

  .btn-wrap {
    position: relative;
    cursor: pointer;

    &:hover {
      .wxcode-container {
        display: block;
      }
    }
  }
</style>