<template>
  <header class="gxst_header">
    <div class="gxst_header__box">
      <div class="gxst_header__text">
        <h1 class="gxst_header__title">{{ title }}</h1>
        <p class="gxst_header__desc">
          <slot></slot>
        </p>
        <SubmitButton class="small-btn"></SubmitButton>
      </div>
      <slot name="img"></slot>
    </div>
  </header>
</template>
<script setup>
const props = defineProps({
  title: {
    type: [String, Number],
    required: false,
  },
});
</script>
<style scoped lang="postcss">
</style>