<!--
 * @Description: 
 * @Date: 2023-06-07 11:53:49
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-08-17 15:31:32
 * @FilePath: /shudongpo-website/apps/pc/components/ArticleList.vue
-->
<template>
  <div class="article-list">
    <!-- :target="isOutLink(item,hostname)?'_blank':''" -->
    <!-- target="_blank" -->
    <a
      class="article"
      v-in-view-animate="['fly-up']"
      v-for="(item, index) in list" :key="index"
      target="_blank"
      :href="getLink(item)"
      @click="handleClick(item)"
    >
      <div></div>
      <img
        loading="lazy"
        class="img"
        :src="item[keyMap.img] ? `${item[keyMap.img]}?x-oss-process=image/resize,w_600,h_390,` : default_img"
        :alt="item[keyMap.title]"
      />
      <div class="txt-box">
        <div class="name">
          <span v-if="item[keyMap.tag] && item[keyMap.tag][0]">
            {{ item[keyMap.tag][0] }}
          </span>
        </div>
        <p class="title">
          {{ item[keyMap.title] }}
        </p>
        <p class="desc">
          发布时间：{{ item[keyMap.date] }}
        </p>
      </div>
    </a>
  </div>
</template>

<script setup>
import useInViewAnimate from '../hooks/inViewAnimate';
import default_img from '../assets/xueyuan/default.jpg';
const vInViewAnimate = {
  mounted: useInViewAnimate,
}
const emit = defineEmits()
const props = defineProps({
  list: Array,
  keyMap: {
    type: Object,
    default: {
      title: 'post_title',
      img: 'conver_image',
      date: 'post_date_format',
      tag: 'business_config'
    },
  },
});
const list = ref(props.list);
const keyMap = ref(props.keyMap);
const hostname = ref(null)
onMounted(()=>{
  hostname.value = window.location.hostname
})
watch(
  () => props.list,
  newValue => {
    list.value = newValue;
    console.log('article-list', list.value);
  },
);
const isOutLink = (item,hostname) => {
  const urlParams =item.redirect_url
  if(!hostname){
    return false
  }
  if(urlParams&&urlParams.indexOf(hostname)>-1){
    return false
  }else{
    if(!urlParams){
      return false
    }else{
      return true
    }
  }
}
const getLink = (item) => {
  if (item.guid && useRoute().path.includes('xueyuan')) {
    return `/xueyuan/c-${item.ID}.html#content`
  }
  return item.redirect_url || ''
}
const handleClick = (item) => {
  if (item.guid && useRoute().path.includes('xueyuan')) {
    emit('on-category-change', {
      name: item._category,
      slug: item._slug,
    })
  }
}
</script>

<style lang="postcss" scoped>
.article-list {
  color: #333333;
  width: 960px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 30px;
  gap: 30px 30px;
  
  .article {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    width: 300px;
    height: 335px;
    cursor: pointer;
    background-color: #fff;
    box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
    border-radius: 5px;
    .img {
      width: 300px;
      height: 195px;
    }
    .txt-box {
      padding: 20px 20px 13px 15px;
      .name {
        margin-bottom: 15px;
        height: 24px;
        span {
          display: inline-block;
          padding: 2px 10px;
          border-radius: 5px;
          font-size: 12px;
          color: #2977fe;
          background-color: #eaf1ff;
        }
      }
      .title {
        margin-bottom: 10px;
        width: 265px;
        min-height: 48px;
        /* line-height: 20px; */
        font: bolder 18px PingFang SC-Medium, PingFang SC;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
      .desc {
        line-height: 16px;
        font-size: 12px;
        color: #999999;
      }
    }
  }
  @keyframes fly-up {
    0% {
      transform: translateY(100px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .fly-up {
    animation-name: fly-up;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }
}
</style>
