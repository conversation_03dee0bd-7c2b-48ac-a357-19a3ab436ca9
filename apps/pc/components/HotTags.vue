<template>
  <div class="hot-tags" v-if="hotTagsList && hotTagsList.length">
    <h3 class="tags-text">热门标签</h3>
    <div class="tags-container">
      <div v-for="tag in hotTagsList" :key="tag.term_id" class="tag">
        <a v-if="tag.slug" :href="`/tags/${tag.slug}`" class="goto">
          {{ tag.name }}
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const hotTagsList = ref([{ name: "" }]);

const { data } = await getHotTags();
if (data) {
  hotTagsList.value = data.map(tag => ({
    name: tag.name,
    slug: tag.slug,
  }));
}
</script>

<style scoped>
.hot-tags {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}
.tags-text {
  font-size: 24px;
  color: #333;
  font-weight: 500;
  padding: 0px 10px 15px 10px;
  border-bottom: 1px #e5e6eb solid;
  margin-bottom: 10px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 8px 12px;
  /* background-color: #e0f7fa; */
  background-color: #ffffff;
  border-radius: 6px;
  font-size: 16px;
  /* color: #00796b; */
  color: #333;
  cursor: pointer;
  transition: background-color 0.3s;
  min-width: 100px;
  text-align: center;
  border: 1px #e5e6eb solid;
}

.tag:hover {
  background-color: #f2f4f6;
  transform: scale(1.05);
}

.goto {
  text-decoration: none;
}
</style>
