<template>
  <div class="section__container" v-in-view-animate="['fly-up']" :class="{ reverse: reverse }" :style="containerStyle">
    <div class="section__text" :class="{ 'has-btn': btn }" :style="{ ...sectionTextStyle, order: reverse ? 1 : 0 }">
      <div class="section__text-title">
        <slot class="section__text-icon" name="icon"></slot>
        <div v-if="titleFormated.length > 1">
          <h3 v-for="(item, index) in titleFormated" :key="index" :style="titleStyle">{{ item }}</h3>
        </div>
        <h3 v-else :style="titleStyle">{{ titleFormated[0] }}</h3>
      </div>
      <img v-if="titleBg" :class="{ reverse: reverse }" :alt="titleFormated[0]" class="section__text-progress"
        src="../assets/common/progress.png" />
      <div class="section__text-desc" :style="descStyle">
        <div class="section__text-desc__item" v-for="item in descFormated">
          <p v-for="line in item.lines">{{ line }}</p>
        </div>
      </div>
      <SubmitButton v-if="btn" class="section__btn" />
    </div>
    <div class="section__img" v-in-view-animate="['fly-up']">
      <slot name="img"></slot>
    </div>
    <slot></slot>
  </div>
</template>
<script setup>
  import vInViewAnimate from '../hooks/inViewAnimate'
  const props = defineProps({
    title: String,
    desc: {
      type: Array,
      default: () => [],
    },
    descStyle: {
      type: Object,
    },
    titleStyle: {
      type: Object,
    },
    sectionTextStyle: {
      type: Object,
    },
    img: String,
    padding: {
      type: [String, Boolean],
    },
    bgGray: Boolean,
    reverse: Boolean,
    btn: {
      type: Boolean,
      default: true,
    },
    titleBg: {
      type: Boolean,
      default: true,
    },
  });
  const descFormated = props.desc.map(item => ({
    lines: item.split('\n'),
  }));
  const titleFormated = props.title.split('\n');
  const containerStyle = computed(() => {
    let padding = 0;
    if (props.padding === true) {
      padding = '3.64583vw'; // 70px
    } else if (props.padding) {
      padding = props.padding;
    }
    const style = {
      paddingBottom: padding,
      paddingTop: padding,
    };
    if (props.bgGray) {
      style.backgroundColor = '#f5f8ff';
    }
    return style;
  });
</script>
<style lang="postcss" scoped>
  .section {
    &__container {
      width: 1200px;
      margin: 0 auto;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      &.reverse .section__img {
        padding-left: 0;
        padding-right: 100px;
      }
    }

    &__text {
      min-width: 340px;

      &-title {
        font-size: 24px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: start;

        /deep/ img {
          width: 24px;
          height: 24px;
          margin-right: 10px;
        }
      }

      &-progress {
        margin-top: 30px;
        margin-bottom: 70px;

        &.reverse {
          transform: rotate(180deg);
        }
      }

      &-desc {
        &__item {
          margin-bottom: 30px;
          position: relative;
          line-height: 22px;

          &::before {
            content: '';
            display: block;
            position: absolute;
            top: 7px;
            left: -30px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            overflow: hidden;
            background: url(../assets/common/icon-desc-prefix.png) no-repeat center;
            background-size: cover;
          }

          &:first-child {
            margin-top: 30px;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }

        font-size: 18px;
        color: #666;
        font-weight: 400;
        padding-left: 35px;
      }
    }

    &__img {
      padding-left: 100px;

      /deep/ img {
        max-width: 800px;
        min-width: 440px;
      }
    }

    &__btn {
      width: 220px;
      text-align: center;
      height: 56px;
      line-height: 56px;
      background: #2977fe;
      border-radius: 5px 5px 5px 5px;
      font-size: 24px;
      margin-left: 50px;
    }
  }

  @keyframes fly-up {
    0% {
      transform: translateY(100px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .fly-up {
    animation-name: fly-up;
    animation-fill-mode: both;
    animation-duration: .5s;
  }
</style>