<template>
  <ul class="el-pager" @click="onPagerClick" @keyup.enter="onEnter">
    <li
      v-if="pageCount > 0"
      :class="{ 'is-active': currentPage === 1 }"
      class="number"
      :aria-current="currentPage === 1"
      :tabindex="tabindex"
    >
      <slot name="page-num" number="1">1</slot>
    </li>
    <li
      v-if="showPrevMore"
      :class="prevMoreKls"
      :tabindex="tabindex"
      @mouseenter="onMouseEnter(true)"
      @mouseleave="quickPrevHover = false"
      @focus="onFocus(true)"
      @blur="quickPrevFocus = false"
    >
      <template  v-if="(quickPrevHover || quickPrevFocus)">
        <slot name="arrow-left"></slot>
      </template>
      <more-filled v-else />
    </li>
    <li
      v-for="pager in pagers"
      :key="pager"
      :class="{ 'is-active': currentPage === pager }"
      class="number"
      :aria-current="currentPage === pager"
      :tabindex="tabindex"
    >
      <slot name="page-num" :number="pager">{{ pager }}</slot>
    </li>
    <li
      v-if="showNextMore"
      :class="nextMoreKls"
      :tabindex="tabindex"
      @mouseenter="onMouseEnter()"
      @mouseleave="quickNextHover = false"
      @focus="onFocus()"
      @blur="quickNextFocus = false"
    >
      <template  v-if="(quickNextHover || quickNextFocus)">
        <slot name="arrow-right"></slot>
      </template>
      <more-filled v-else />
    </li>
    <li
      v-if="pageCount > 1"
      :class="{ 'is-active': currentPage === pageCount }"
      class="number"
      :aria-current="currentPage === pageCount"
      :tabindex="tabindex"
    >
      <slot name="page-num" :number="pageCount">{{ pageCount }}</slot>
    </li>
  </ul>
</template>
<script lang="ts" setup>
import { computed, ref, watchEffect } from 'vue'
import { MoreFilled } from '@element-plus/icons-vue'

const props = defineProps({
  currentPage: {
    type: Number,
    default: 1,
  },
  pageCount: {
    type: Number,
    required: true,
  },
  pagerCount: {
    type: Number,
    default: 7,
  },
})
const emit = defineEmits(['change'])

const showPrevMore = ref(false)
const showNextMore = ref(false)
const quickPrevHover = ref(false)
const quickNextHover = ref(false)
const quickPrevFocus = ref(false)
const quickNextFocus = ref(false)

const pagers = computed(() => {
  const pagerCount = props.pagerCount
  const halfPagerCount = (pagerCount - 1) / 2
  const currentPage = Number(props.currentPage)
  const pageCount = Number(props.pageCount)
  let showPrevMore = false
  let showNextMore = false
  if (pageCount > pagerCount) {
    if (currentPage > pagerCount - halfPagerCount) {
      showPrevMore = true
    }
    if (currentPage < pageCount - halfPagerCount) {
      showNextMore = true
    }
  }
  const array: number[] = []
  if (showPrevMore && !showNextMore) {
    const startPage = pageCount - (pagerCount - 2)
    for (let i = startPage; i < pageCount; i++) {
      array.push(i)
    }
  } else if (!showPrevMore && showNextMore) {
    for (let i = 2; i < pagerCount; i++) {
      array.push(i)
    }
  } else if (showPrevMore && showNextMore) {
    const offset = Math.floor(pagerCount / 2) - 1
    for (let i = currentPage - offset; i <= currentPage + offset; i++) {
      array.push(i)
    }
  } else {
    for (let i = 2; i < pageCount; i++) {
      array.push(i)
    }
  }
  return array
})
const prevMoreKls = computed(() => [
  'more',
  'btn-quickprev',
  'el-icon',
])
const nextMoreKls = computed(() => [
  'more',
  'btn-quicknext',
  'el-icon',
])
const tabindex = computed(() => 0)

watchEffect(() => {
  const halfPagerCount = (props.pagerCount - 1) / 2
  showPrevMore.value = false
  showNextMore.value = false
  if (props.pageCount! > props.pagerCount) {
    if (props.currentPage > props.pagerCount - halfPagerCount) {
      showPrevMore.value = true
    }
    if (props.currentPage < props.pageCount! - halfPagerCount) {
      showNextMore.value = true
    }
  }
})

function onMouseEnter(forward = false) {
  if (forward) {
    quickPrevHover.value = true
  } else {
    quickNextHover.value = true
  }
}

function onFocus(forward = false) {
  if (forward) {
    quickPrevFocus.value = true
  } else {
    quickNextFocus.value = true
  }
}

function onEnter(e: UIEvent) {
  const target = e.target as HTMLElement
  if (
    target.tagName.toLowerCase() === 'li' &&
    Array.from(target.classList).includes('number')
  ) {
    const newPage = Number(target.textContent)
    if (newPage !== props.currentPage) {
      emit('change', newPage)
    }
  } else if (
    target.tagName.toLowerCase() === 'li' &&
    Array.from(target.classList).includes('more')
  ) {
    onPagerClick(e)
  }
}

function onPagerClick(event: UIEvent) {
  const target = event.target as HTMLElement
  if (target.tagName.toLowerCase() === 'ul') {
    return
  }
  let newPage = Number(target.textContent)
  const pageCount = props.pageCount!
  const currentPage = props.currentPage
  const pagerCountOffset = props.pagerCount - 2
  if (target.className.includes('more')) {
    if (target.className.includes('quickprev')) {
      newPage = currentPage - pagerCountOffset
    } else if (target.className.includes('quicknext')) {
      newPage = currentPage + pagerCountOffset
    }
  }
  if (!Number.isNaN(+newPage)) {
    if (newPage < 1) {
      newPage = 1
    }
    if (newPage > pageCount) {
      newPage = pageCount
    }
  }
  if (newPage !== currentPage) {
    emit('change', newPage)
  }
}
</script>