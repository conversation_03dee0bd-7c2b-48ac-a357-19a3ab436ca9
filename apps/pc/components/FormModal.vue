<template>
  <div class="modal__mask" v-show="_showModal" @click='handleCloseModal'>
    <div @click.stop class="modal__main" :class="{ modal__main_price: isPriceModal }">
      <img @click='handleCloseModal' class='close-icon' src='../assets/common/<EMAIL>' alt=''>
      <div class='image-con'>
        <img class='dialog-img' v-if='isActivity' src='../assets/activity/xsc.png' alt=''>
        <img class='dialog-img' v-else src='../assets/common/<EMAIL>' alt=''>
      </div>
      
      <div class='form-content' :class="{'is-activity': isActivity}">
        <div class='form-header' v-if='textMap[currentModalType]?.title'>
          <h2 class='form-title'>{{ textMap[currentModalType]?.title }}</h2>
          <p class='form-subhead'>{{ textMap[currentModalType]?.subhead }}</p>
        </div>
        <div class="modal__header" v-else>
          <img class="logoimg" src="~/assets/common/logo.png" alt="logo" />
          <span class="divider">|</span>
          <span>生鲜SaaS ERP</span>
        </div>
        <div class='form-main'>
          <div class="modal__content_price" v-if="isPriceModal">
            <p>预估报价</p>
            <p class="modal__content_price_num">
              {{ expectPrice }}
              <span>元</span>
            </p>
          </div>
          <the-form :dataGramList='dataGramList' ref="form" :needEmployee="!isActivity"  @on-change="handleChange"></the-form>
        </div>
        <div class='btn-con'>
          <button
            @click='handleSaveFormData'
            :class="{'disabled': disabledSubmit}"
            class='submit-btn'
          >{{ textMap[currentModalType]?.btnText }}</button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang='ts' setup>
import { computed, currentModalType, ModalType, activeGram } from '#imports';

interface TextMap {
  [key: string]: {
    title: string,
    subhead: string,
    btnText: string
  }
}
const textMap: TextMap = {
  [ModalType.price]: {
    title: '免费获取方案报价',
    subhead: '提交信息5秒获取最新报价',
    btnText: '点击获取报价方案'
  },
  [ModalType.common]: {
    title: '',
    subhead: '',
    btnText: '立即申请'
  },
  [ModalType.datum]: {
    title: '',
    subhead: '',
    btnText: '立即申请'
  },
  [ModalType.xsc]: {
    title: '立即报名参会',
    subhead: '2023年10月21日 中国·昆山 提前抢50人私享晚宴名额',
    btnText: '完成报名'
  }
};

type FormData = {
  user_level: number | undefined,
  phone: string,
  username: string,
  companyName: string
}

let formData = ref({} as FormData);
const form = ref(null);
const _showModal = showModal;
const expectPrice = ref<number | string>(0);
const isPriceModal = computed(() => {
  return currentModalType.value === ModalType.price;
});

const disabledSubmit = computed(() => {
  let checkProps = [formData.value.username, formData.value.phone];
  if (currentModalType.value === ModalType.xsc) checkProps.push(formData.value.companyName);
  return checkProps.some(item => !item);
})
const isCommonModal = computed(() => {
  return currentModalType.value === ModalType.common;
});
const isDatumModal = computed(() => {
  return currentModalType.value === ModalType.datum;
});
const isActivity = computed(() => {
  const activityList = [ModalType.xsc];
  return activityList.includes(currentModalType.value);
})


interface MyObject {
  [key: number]: number;
}

const priceList: MyObject = {
  4: 45600,
  3: 98000,
  2: 168000,
  1: 168000,
};
const handleChange = (data: FormData) => {
  formData.value = data;
  if (isPriceModal && formData.value.username && formData.value.phone) {
    expectPrice.value = priceList[formData.value.user_level as number] || '';
  }
};

const handleCloseModal = () => {
  expectPrice.value = 0;
  (form.value!).resetForm();
  closeModal();
};
const handleSaveFormData = async () => {
  const validate = (form.value!).validate();
  if (!validate.valid) {
    showError(validate.error);
    return;
  }
  if (currentModalType.value === ModalType.xsc) {
    // 删除user_level
    delete formData.value.user_level;
  }
  const { data } = await saveFormData(formData.value);
  const res = JSON.parse(data.value as string);
  if (res.status === 'success') {
    ElMessage.success(res.message)
    expectPrice.value = 0;
    closeModal();
    // 如果有资料包，弹出资料包
    if (activeGram.value?.post_content) {
      setTimeout(() => {
        noticeDialog.value = true
      }, 1000)
    }
  } else {
    ElMessage.error(res.message)
  }
};
</script>
<style scoped lang="postcss">
.btn-con {
  background-color: white;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 10px 0 20px 0;
}
.modal__mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 214748369999949999;
}

.modal__main {
  width: 1310px;
  height: 600px;
  background-color: #fff;
  border-radius: 15px 15px 15px 15px;
  overflow: hidden;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2147483649999;
  .image-con {
    background-color: #4075f5;
    padding: 20px 0;
  }
  .close-icon {
    position: absolute;
    width: 30px;
    right: 15px;
    top: 15px;
    cursor: pointer;
    z-index: 1;
  }
  .dialog-img {
    width: 809px;
    height: 100%;
    display: block;
  }
  .form-content {
    width: 501px;
    z-index: 0;
    position: relative;
    padding-top: 35px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .disabled {
      background-color: #CCCCCC !important;
    }
    .divider {
      margin: 0 10px;
    }
    .form-header {
      h2.form-title {
        font-size: 32px;
        font-weight: 400;
        color: #333333;
        text-align: center;
      }
      p.form-subhead {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        text-align: center;
        margin-top: 2px;
      }
    }
    .modal__header {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #047eef;
      margin-bottom: 27px;
      margin-top: 30px;
      position: relative;
      .logoimg{
        width:140px;
      }
    }
    .form-main {
      margin-top: 12px;
      width: 100%;
      min-height: 55%;
      padding: 0 23px;
      box-sizing: border-box;
      max-height: 70%;
      overflow: auto;
      overflow-x: hidden;
      //height: 260px;
      &::-webkit-scrollbar {
        width: 6px;
        height: 8px;
      }
      &::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background-color: rgba(0, 0, 0, 0.2);
      }
      &::-webkit-scrollbar-track {
        border-radius: 4px;
        background-color: rgba(0, 0, 0, 0.1);
      }
      .modal__content_price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 450px;
        height: 50px;
        margin-bottom: 24px;
        padding: 0px 20px;
        background: #ffffff;
        border-radius: 15px 15px 15px 15px;
        border: 2px solid #2977fe;
        p {
          font-size: 18px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #2977fe;
        }
        .modal__content_price_num {
          font-size: 24px;
          span {
            font-size: 18px;
            margin-left: 19px;
          }
        }
      }
    }
    .submit-btn {
      cursor: pointer;
      margin-top: 14px;
      width: 220px;
      height: 46px;
      background: #005DFF;
      border-radius: 25px 25px 25px 25px;
      font-size: 20px;
      color: white;
    }
  }
  .is-activity {
    .form-header {
      h2.form-title {
        font-size: 32px;
        font-weight: 400;
        color: #333333;
        text-align: center;
      }
      p.form-subhead {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        text-align: center;
        margin-top: 12px;
      }
    }
    .form-main {
      margin-top: 32px;
      width: 100%;
      min-height: 55%;
      padding: 0 33px;
      box-sizing: border-box;
      /deep/.form-item {
        margin-bottom: 33px;
      }
      .modal__content_price {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 414px;
        height: 50px;
        margin-bottom: 24px;
        padding: 0px 20px;
        background: #ffffff;
        border-radius: 15px 15px 15px 15px;
        border: 2px solid #2977fe;
        p {
          font-size: 18px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #2977fe;
        }
        .modal__content_price_num {
          font-size: 24px;
          span {
            font-size: 18px;
            margin-left: 19px;
          }
        }
      }
    }
  }
}
</style>
