<!--
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 13:53:12
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-07-06 10:47:02
-->
<template>
  <div class="nav" :class="{ scroll: scroll }">
    <div class="wrapper flex flex-row items-center">
      <a href="/">
        <img v-show="!scroll && !blueTheme" class="nav-logo" src="../assets/common/logo-white.png" alt="蔬东坡" />
        <img v-show="scroll || blueTheme" class="nav-logo" src="../assets/common/logo.png" alt="蔬东坡" />
      </a>
      <div
        class="nav-item"
        @mouseenter="handleMouseenterNav(nav, index)"
        @mouseleave="handleMouseleaveNav"
        :class="{
          folder: nav.children && nav.children.length > 0,
          active: currentNavIndex === index,
          blueTheme: blueTheme
        }"
        v-for="(nav, index) in navData"
        :key="index"
      >
        <a :href="nav.link" class="nav__title_text">{{ nav.title }} <img :src="hot_new_icon" v-if="nav.title==='产品'" class="hot_new_icon"></a>
        <div class="nav-children-simple" v-if="!nav.isGroup && nav.children && nav.children.length > 0">
          <NuxtLink
            class="nav-children-simple__item"
            v-for="(child, index) in nav.children"
            :key="index"
            external
            @click="goPage(item.link)"
            :to="child.link"
          >
            <img :src="child.icon" :alt="child.title" />
            <div class="nav-children-simple__item-text">{{ child.title }}</div>
          </NuxtLink>
        </div>
      </div>
      <div class="flex flex-row align-middle justify-end relative btn-wrap flex-1 sceneCode" :class="{blueTheme: blueTheme}">
        <ClientOnly>
          <template #fallback>
            <div class="nav-btn">扫码体验</div>
          </template>
          <SceneCode />
        </ClientOnly>
      </div>
      <Transition name="expand" :duration="{ enter: 300, leave: 250 }">
        <div
          class="nav-children flex flex-row"
          :class="[{ show: !!showNavChildren }, navChildName]"
          v-show="showNavChildren"
          @mouseenter="handleMouseenterNavChildren()"
          @mouseleave="handleMouseleaveNavChildren"
        >
          <div v-if="currentNav.id != 2" class="nav-children__group">
            <template v-for="(group, index) in currentNav.children">
              <div
                v-if="!group.link"
                class="nav-children__group-item"
                :class="{ active: currentGroupIndex === index }"
                @mouseenter="handleMouseenterNavChildren(group, index)"
                @click="goPage(group.link)"
              >
                {{ group.title }}
              </div>
              <NuxtLink
                v-if="group.link"
                class="nav-children__group-item"
                :class="{ active: currentGroupIndex === index }"
                @mouseenter="handleMouseenterNavChildren(group, index)"
                :to="group.link"
              >
                {{ group.title }}
              </NuxtLink>
            </template>
          </div>
          <div
            v-if="currentNav.id != 2"
            class="nav-children__content flex flex-row flex-wrap content-start"
            :class="navChildrenContentClassName"
          >
            <a
              class="nav-children__content-item"
              :href="item.link"
              v-for="(item, index) in currentGroup.children"
              :key="index"
              @click="goPage(item.link)"
            >
              <img class="icon" :src="item.icon" :alt="item.title" />
              <div class="title">{{ item.title }}</div>
              <div class="desc">{{ item.desc }}</div>
            </a>
          </div>
          <div v-if="currentNav.id == 2" class="width_auto">
            <div v-for="(group, index) in currentNav.children" :key="index" class="flex flex-row flex-wrap">
              <div class="nav-children__group-item nav-children__group">
                 <a :href="group.link">{{ group.title }}</a>
              </div>
              <div class="nav-children__content flex flex-row flex-wrap" :class="navChildrenContentClassName">
                <NuxtLink
                  class="nav-children__content-item"
                  :to="item.link"
                  v-for="(item, index) in group.children"
                  :key="index"
                  @click="goPage(item.link)"
                >
                  <img class="icon" :src="item.icon" :alt="item.title" />
                  <div class="title">{{ item.title }}</div>
                  <div class="desc">{{ item.desc }}</div>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>
<script setup>
import iconDingdan from '../assets/common/icon-dingdan.png';
import iconCaigou from '../assets/common/icon-caigou.png';
import iconFenjian from '../assets/common/icon-fenjian.png';
import iconKucun from '../assets/common/icon-kucun.png';
import iconPaixian from '../assets/common/icon-paixian.png';
import iconCaiwu from '../assets/common/icon-caiwu.png';
import iconAIld from '../assets/common/icon-aild.png';
import hot_new_icon from '../assets/common/hot_new_icon.png';

import iconfZycfXiadan from '../assets/common/icon-zycf-xiadan.png';
import iconfZycfCaipu from '../assets/common/icon-zycf-caipu.png';
import iconfZycfFenjian from '../assets/common/icon-zycf-fenjian.png';
import iconfZycfFenxi from '../assets/common/icon-zycf-fenxi.png';
import iconfZycfJiagong from '../assets/common/icon-zycf-jiagong.png';
import iconfZycfJihua from '../assets/common/icon-zycf-jihua.png';
import iconfZycfSuyuan from '../assets/common/icon-zycf-suyuan.png';

import iconfFdyBom from '../assets/common/icon-fdy-bom.png';
import iconfFdyCaiwu from '../assets/common/icon-fdy-caiwu.png';
import iconfFdyCaigou from '../assets/common/icon-fdy-caigou.png';
import iconfFdyDaping from '../assets/common/icon-fdy-daping.png';
import iconfFdyFahuo from '../assets/common/icon-fdy-fahuo.png';
import iconfFdyGuanli from '../assets/common/icon-fdy-guanli.png';
import iconfFdyShangcheng from '../assets/common/icon-fdy-shangcheng.png';

import iconSqtgGongyinglian from '../assets/common/icon-sqtg-gongyinglian.png';
import iconSqtgHoutai from '../assets/common/icon-sqtg-houtai.png';
import iconSqtgHuodong from '../assets/common/icon-sqtg-huodong.png';
import iconSqtgXiaochengxu from '../assets/common/icon-sqtg-xiaochengxu.png';

import iconStyptDingzhi from '../assets/common/icon-stypt-dingzhi.png';
import iconStyptOpenapi from '../assets/common/icon-stypt-openapi.png';
import iconStyptYecaiyiti from '../assets/common/icon-stypt-yecaiyiti.png';

import iconZnyjDaping from '../assets/common/icon-znyj-daping.png';
import iconZnyjFenjian from '../assets/common/icon-znyj-fenjian.png';
import iconZnyjJiance from '../assets/common/icon-znyj-jiance.png';
import iconZnyjPda from '../assets/common/icon-znyj-pda.png';

import iconSxpsjjfaGaoxiao from '../assets/common/supply.png';
import iconSxpsjjfaJiudian from '../assets/common/hotel.png';
import iconSxpsjjfaShitang from '../assets/common/canteen.png';
import iconSxpsjjfaTuanshan from '../assets/common/groupdinner.png';

import iconZycfjjfaJingcaijiagong from '../assets/common/process.png';
import iconZycfjjfaRouleijiagong from '../assets/common/meatpack.png';
import iconZycfjjfaYingyangcan from '../assets/common/student.png';
import iconZycfjjfaYuzhicai from '../assets/common/yuzhicai.png';

import iconYgscPeiSongShang from '../assets/common/ygsc_peisongshang.png'
import iconYgscYangGuanCaiGou from '../assets/common/ygsc_yanguancaigou.png'

// 学习中心
import iconActivate from '../assets/common/icon-huodong.png';
import iconData from '../assets/common/iconData.png';
import iconZhuanjia from '../assets/common/icon-zhuanjia.png';
import iconContent from '../assets/common/iconContent.png';
import iconHangye from '../assets/common/hangye.png';
import iconZiliao from '../assets/common/icon-ziliao.png';
import iconFuwu from '../assets/common/icon-fuwu.png';
import iconBangzhu from '../assets/common/icon-bangzhu.png';
import iconJieshao from '../assets/common/icon-jieshao.png';
import iconDongtai from '../assets/common/icon-dongtai.png';
import iconLianxi from '../assets/common/icon-lianxi.png';
import xyhc_icon from '../assets/zhcg/xyhc_icon.png'
import ptyy_icon from '../assets/zhcg/ptyy_icon.png'
import dsjjg_icon from '../assets/zhcg/dsjjg_icon.png'
import xysa_icon from '../assets/zhcg/xysa_icon.png'
import jxld_icon from '../assets/zhcg/jxld_icon.png'
import znyj_icon from '../assets/zhcg/znyj_icon.png'
import { reactive } from 'vue';

// 哪些页面应用蓝色主题头
let blueTheme = ref(false)
onMounted(() => {
  const path = useRoute().path
  /* blueTheme.value = [
    '/products/paas/', '/products/business/', '/products/api/',
    '/sxkt/', '/yckt/', '/zlxz/', '/hymt/', '/epsf/'
  ].includes(path) || path.includes('/xueyuan') */
  blueTheme.value = [
    '/products/paas/', '/products/business/', '/products/api/','/aild/','/tags/','/scygzhcg/','/sdpspecial/sxdj'
  ].includes(path)
})

const navData = reactive([
  {
    title: '首页',
    link: '/',
    children: [],
  },
  {
    title: '产品',
    link: '/shicai/',
    isGroup: true,
    children: [
      {
        title: '生鲜配送系统',
        link: '/shicai',
        children: [
          {
            link: '/shicai#dingdan',
            icon: iconDingdan,
            title: '订单处理',
            desc: '2分钟轻松完成',
          },
          {
            link: '/shicai#caigou',
            icon: iconCaigou,
            title: '采购管理',
            desc: '有效降低35%采购成本',
          },
          {
            link: '/shicai#fenjian',
            icon: iconFenjian,
            title: '分拣称重',
            desc: '0出错、提效60%',
          },
          {
            link: '/shicai#kucun',
            icon: iconKucun,
            title: '库存管理',
            desc: '精准运营',
          },
          {
            link: '/shicai#paixian',
            icon: iconPaixian,
            title: '高效排线',
            desc: '半小时搞定2000家',
          },
          {
            link: '/shicai#caiwu',
            icon: iconCaiwu,
            title: '财务报表',
            desc: '清晰明了',
          },
          {
            link: '/aild/',
            icon: iconAIld,
            out: true,
            title: 'Al录单助手',
            desc: '实现最快1秒下单',
          },
        ],
      },
      {
        title: '食材阳光智慧采购平台',
        link: '/scygzhcg/',
        out: true,
        children: [
          {
            link: '/scygzhcg#ptyy',
            icon: ptyy_icon,
            title: '平台运营系统',
            // desc: '2分钟轻松完成',
          },
          {
            link: '/scygzhcg#dsjjg',
            icon: dsjjg_icon,
            title: '大数据监管系统',
            // desc: '有效降低35%采购成本',
          },
          {
            link: '/scygzhcg#xysa',
            icon: xysa_icon,
            title: '校园食安智慧监管系统',
            // desc: '有效降低35%采购成本',
          },
          {
            link: '/scygzhcg#xyhc',
            icon: xyhc_icon,
            title: '校园后厨管理系统',
            // desc: '有效降低35%采购成本',
          },
          {
            link: '/scygzhcg#jxld',
            icon: jxld_icon,
            title: '家校联动系统',
            // desc: '有效降低35%采购成本',
          },
          {
            link: '/scygzhcg#znyj',
            icon: znyj_icon,
            title: '智能硬件接入平台',
            // desc: '有效降低35%采购成本',
          },
        ],
      },
      {
        title: '中央厨房系统',
        link: '/processing',
        children: [
          {
            link: '/processing#xiadan',
            icon: iconfZycfXiadan,
            title: '智能下单',
            desc: '线上商城，智能定价',
          },
          {
            link: '/processing#caipu',
            icon: iconfZycfCaipu,
            title: '菜谱管理',
            desc: '毛利、营养成分分析',
          },
          {
            link: '/processing#jihua',
            icon: iconfZycfJihua,
            title: '计划管理',
            desc: '汇总需求，生成生产计划',
          },
          {
            link: '/processing#jiagong',
            icon: iconfZycfJiagong,
            title: '加工管理',
            desc: '汇总需求，生成生产计划',
          },
          {
            link: '/processing#fenjian',
            icon: iconfZycfFenjian,
            title: '智能分拣',
            desc: '0出错，提效60%',
          },
          {
            link: '/processing#suyuan',
            icon: iconfZycfSuyuan,
            title: '食安溯源',
            desc: '农残检测，过程追溯',
          },
          {
            link: '/processing#fenxi',
            icon: iconfZycfFenxi,
            title: '数据分析',
            desc: '数据大屏、实时报表',
          },
        ],
      },
      // {
      //   title: '福得云管理系统',
      //   link: '/yzc',
      //   children: [
      //     {
      //       link: '/yzc#shangcheng',
      //       icon: iconfFdyShangcheng,
      //       title: '预制菜商城',
      //       desc: '高效汇总订单',
      //     },
      //     {
      //       link: '/yzc#bom',
      //       icon: iconfFdyBom,
      //       title: 'BOM管理',
      //       desc: '产品关联原料，精准核算成本',
      //     },
      //     {
      //       link: '/yzc#caigou',
      //       icon: iconfFdyCaigou,
      //       title: '采购',
      //       desc: 'MRP反算，准确监管',
      //     },
      //     {
      //       link: '/yzc#guanli',
      //       icon: iconfFdyGuanli,
      //       title: '生产过程精细化管理',
      //       desc: '数据同步，远程监管',
      //     },
      //     {
      //       link: '/yzc#fahuo',
      //       icon: iconfFdyFahuo,
      //       title: '发货配送',
      //       desc: '多仓管理，一键发货',
      //     },
      //     {
      //       link: '/yzc#caiwu',
      //       icon: iconfFdyCaiwu,
      //       title: '财务',
      //       desc: '无缝对接主流财务软件',
      //     },
      //     {
      //       link: '/yzc#daping',
      //       icon: iconfFdyDaping,
      //       title: '数据大屏',
      //       desc: '预制菜大脑，全流程可视化',
      //     },
      //   ],
      // },
      {
        title: '社区团购系统',
        out: true,
        link: '/groupbuy',
        children: [
          {
            link: '/groupbuy/#huodong',
            icon: iconSqtgHuodong,
            title: '营销活动丰富',
            desc: '天天新玩法',
            out: true,
          },
          {
            link: '/groupbuy/#xiaochengxu',
            icon: iconSqtgXiaochengxu,
            title: '团长运营小程序',
            desc: '快速成团',
            out: true,
          },
          {
            link: '/groupbuy/#houtai',
            icon: iconSqtgHoutai,
            title: '商品运营后台',
            desc: '清晰有序',
            out: true,
          },
          {
            link: '/groupbuy/#gongyinglian',
            icon: iconSqtgGongyinglian,
            title: '供应链系统管理',
            desc: '收益最大化',
            out: true,
          },
        ],
      },
      {
        title: '生态云平台',
        columnCount: 2,
        link: '/products/business/',
        out: true,
        children: [
          {
            link: '/products/business/',
            icon: iconStyptYecaiyiti,
            title: '业财一体化（金蝶、用友）',
            desc: '全方位赋能生鲜业财一体化',
            out: true,
          },
          {
            link: '/products/paas/',
            icon: iconStyptDingzhi,
            title: '业务定制平台（PAAS）',
            desc: '功能强大、灵活定制、扩展性高',
            out: true,
          },
          {
            link: '/products/api/',
            icon: iconStyptOpenapi,
            title: '开放平台 Open API',
            desc: '数据同步、开放接口、稳定维护',
            out: true,
          },
        ],
      },
      {
        title: '智能硬件',
        columnCount: 3,
        link: '/products/hardware/#irpds',
        out: true,
        children: [
          {
            link: '/products/hardware/#irpds',
            icon: iconZnyjFenjian,
            title: '智能分拣称重',
            out: true,
          },
          {
            link: '/products/hardware/#pesticide',
            icon: iconZnyjJiance,
            title: '农残检测设备',
            out: true,
          },
          {
            link: '/products/hardware/#screen',
            icon: iconZnyjDaping,
            title: 'BI数据大屏',
            out: true,
          },
          {
            link: '/products/hardware/#pda',
            icon: iconZnyjPda,
            title: '库管PDA',
            out: true,
          },
        ],
      },
    ],
  },
  {
    title: '解决方案',
    link: '/qsy/',
    id: 2,
    isGroup: true,
    layout: 'inline',
    children: [
      {
        title: '生鲜配送解决方案',
        link: '/qsy/',
        children: [
          {
            link: '/qsy/',
            icon: iconSxpsjjfaShitang,
            title: '企事业单位食堂配送',
          },
          {
            link: '/p2/',
            icon: iconSxpsjjfaJiudian,
            title: '餐饮酒店配送',
          },
          // {
          //   link: '/tuanshan/',
          //   icon: iconSxpsjjfaTuanshan,
          //   title: '团餐团膳食堂承包',
          // },
          {
            link: '/gxst/',
            icon: iconSxpsjjfaGaoxiao,
            title: '高校后勤供应链管理',
          },
        ],
      },
      {
        title: '团餐团膳解决方案',
        link: '/xsyyc/',
        children: [
          {
            link: '/xsyyc/',
            icon: iconZycfjjfaYingyangcan,
            title: '学生营养餐',
          },
          {
            link: '/roujg/',
            icon: iconZycfjjfaRouleijiagong,
            title: '肉类加工与分割',
          },
          {
            link: '/jcjg/',
            icon: iconZycfjjfaJingcaijiagong,
            title: '净菜加工',
          },
          {
            link: '/yzc/',
            icon: iconZycfjjfaYuzhicai,
            title: '预制菜',
          },
        ],
      },
      {
        title: '区县阳光食材解决方案',
        link: '/ygsc/',
        children: [
          {
            link: '/ygsc/',
            icon: iconYgscYangGuanCaiGou,
            title: '区县阳光食材采购平台',
          },
          // {
          //   link: '/yyd/',
          //   icon: iconZycfjjfaRouleijiagong,
          //   title: '运营端',
          // },
          // {
          //   link: '/jcjg/',
          //   icon: iconZycfjjfaJingcaijiagong,
          //   title: '运营',
          // },
          // {
          //   link: '/yysxt/',
          //   icon: iconYgscPeiSongShang,
          //   title: '配送商',
          // },
        ],
      },
    ],
  },
  {
    title: '客户案例',
    link: '/movee/',
  },
  {
    title: '学习中心',
    link: '/sxkt/',
    out: true,
    children: [
      // {
      //   title: '研究院简介',
      //   icon: iconZhuanjia,
      //   link: '/sdpcollege/',
      //   out: true,
      // },
      {
        title: '生鲜课堂',
        icon: iconActivate,
        link: '/sxkt/',
      },
      {
        title: '央厨课堂',
        icon: iconHangye,
        link: '/yckt/',
        out: true,
      },
      // {
      //   title: '实操干货',
      //   icon: iconData,
      //   link: '/sdpcollege/class/',
      //   out: true,
      // },
      {
        title: '资料下载',
        icon: iconData,
        link: '/zlxz/',
      },
      {
        title: '行业会议',
        link: '/hymt/',
        icon: iconLianxi,
      },
      {
        title: '专家咨询',
        icon: iconZhuanjia,
        link: '/epsf/',
      },
      {
        title: '新闻报道',
        link: '/xueyuan#content',
        icon: iconContent,
      },
      // {
      //   title: '行业洞察',
      //   link: '/xueyuan/',
      //   icon: iconHangye,
      // },
    ],
  },
  {
    title: '服务与支持',
    link: '/movee/service/',
    out: true,
    children: [
      {
        title: '服务保障',
        link: '/movee/service/',
        icon: iconFuwu,
        out: true,
      },
      {
        title: '帮助中心',
        link: 'https://helpcenter.sdongpo.com',
        icon: iconBangzhu,
      },
    ],
  },
  {
    title: '关于蔬东坡',
    link: '/about/',
    children: [
      {
        title: '企业介绍',
        link: '/about/',
        icon: iconJieshao,
      },
      {
        title: '联系我们',
        link: '/about/contact/#contact',
        icon: iconLianxi,
        out: true,
      },
    ],
  },
]);
const currentNav = reactive({
  children: [],
  id: '',
});
const currentGroup = reactive({
  children: [],
});
const currentGroupIndex = ref(-1);
const currentNavIndex = ref(-1);

const hoverNav = ref(false);
const leaveNavTimer = [];
const handleMouseenterNav = (nav, index) => {
  leaveNavTimer.forEach(item => {
    clearTimeout(item);
  });
  if (nav.children && nav.children && nav.isGroup) {
    currentNav.id = nav.id;
    currentNav.children = nav.children;
    currentGroup.children = currentNav.children[0].children || [];
    currentNavIndex.value = index;
    currentGroupIndex.value = 0;
  } else {
    currentNav.children = [];
    currentGroup.children = [];
    currentGroupIndex.value = -1;
  }
  hoverNav.value = true;
};
const handleMouseleaveNav = () => {
  leaveNavTimer.push(
    setTimeout(() => {
      hoverNav.value = false;
    }, 300),
  );
};

const hoverNavChildren = ref(false);
const showNavChildren = computed(() => {
  return (hoverNav.value || hoverNavChildren.value) && currentNav.children.length > 0;
});
const showNavChildren2 = computed(() => {
  return hoverNavChildren.value && currentNav.children.length > 0 && currentNav.id == 2;
});

const handleMouseenterNavChildren = (group, index) => {
  if (group) {
    currentGroup.children = group.children || [];
    currentGroupIndex.value = index;
  }
  hoverNavChildren.value = true;
};
const handleMouseleaveNavChildren = () => {
  hoverNavChildren.value = false;
};
const navChildName = computed(() => {
  const currentNavDetail = navData[currentNavIndex.value];
  const className = [];
  if (!currentNavDetail) {
    return '';
  }
  if (currentNavDetail.id) {
    className.push(`nav-children-${currentNavDetail.id}`);
  }
  return className;
});
const navChildrenContentClassName = computed(() => {
  const currentNavDetail = navData[currentNavIndex.value];
  const currentGroupDetail = currentNav.children[currentGroupIndex.value];
  const className = [];
  if (!currentNavDetail) {
    return '';
  }
  if (currentNavDetail.columnCount) {
    className.push(`column-${currentNavDetail.columnCount}`);
  }
  if (currentNavDetail.layout) {
    className.push(currentNavDetail.layout);
  }
  if (currentGroupDetail && currentGroupDetail.columnCount) {
    className.push(`column-${currentGroupDetail.columnCount}`);
  }
  return className;
});
const goPage = url => {
  const pattern = /#(.*)$/;
  const match = url.match(pattern);
  const targetId = match ? match[1] : '';
  const targetElement = document.getElementById(targetId);
  if (targetElement) {
    targetElement.scrollIntoView();
  }
};
const scroll = ref(false);

onMounted(() => {
  changeLink(navData);
  const scrollContainer = document.querySelector('#__nuxt');
  const checkScroll = () => {
    if (scrollContainer.scrollTop > 56) {
      scroll.value = true;
    } else {
      scroll.value = false;
    }
  };
  checkScroll();
  scrollContainer.addEventListener('scroll', checkScroll);
});
</script>
<style lang="postcss" scoped>
.nav__title_text {
  position: relative;
}
.hot_new_icon {
  position: absolute;
  top: -27px;
  left: 23px;
}
/deep/ {
  .expand-enter-active,
  .expand-leave-active {
    transition: opacity 300ms ease;
    min-height: 556px;
    max-height: 800px;
  }

  .expand-enter-from,
  .expand-leave-to {
    min-height: 0 !important;
    max-height: 0;
  }
}

.width_auto {
  width: 100%;
  height: 100%;
}

.nav {
  position: fixed;
  width: 100%;
  z-index: 100;
  padding-top: 35px;

  &.scroll {
    background-color: #fff;
  }
}

.wrapper {
  width: 1400px;
  margin: 0 auto;
  /* color: #fff; */
}

.nav-logo {
  height: 59px;
  margin-right: 290px;
}

.nav-item {
  height: 59px;
  line-height: 59px;
  font-size: 18px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  margin-right: 30px;
  color: #fff;
  position: relative;

  &.blueTheme {
    color: #2977FE;
    &.folder {
      background-image: url(../assets/common/arrow-down-blue.png);
    }
  }

  &:hover::after {
    content: '';
    position: absolute;
    display: block;
    left: 0;
    bottom: 0;
    width: 100%;
    border-bottom: 3px solid #2977fe;
  }

  &:hover .nav-children-simple {
    max-height: 500px;
    padding: 24px 24px 4px 24px;
    display: block;
  }
}

.scroll .wrapper {
  .wrapper {
    background: #fff;
  }

  .nav-item {
    color: #333;
  }

  /deep/ .nav-btn {
    background: #2977fe;
  }
}

.nav-item.folder {
  background: url(../assets/common/arrow-down-white.png) no-repeat center right;
  background-position-y: 26px;
  background-position-x: right;
  background-size: 13px;
  padding-right: 16px;
}

.nav.scroll {
  .nav-item.folder {
    background: url(../assets/common/arrow-down.png) no-repeat center right;
    background-position-y: 26px;
    background-position-x: right;
    background-size: 13px;
    padding-right: 16px;
  }
}

.nav-children {
  min-height: 330px;
  transition: all 300ms;
  position: absolute;
  top: 94px;
  width: 1200px;
  background: #ffffff;
  box-shadow: 2px 2px 13px 3px rgba(142, 142, 142, 0.1);
  border-radius: 5px 5px 5px 5px;
  overflow: hidden;

  &-simple {
    top: 59px;
    left: 50%;
    margin-left: -100px;
    width: 200px;
    padding: 0;
    box-sizing: border-box;
    position: absolute;
    max-height: 0;
    overflow: hidden;
    transition: all 300ms;
    background: #ffffff;
    box-shadow: 2px 2px 13px 3px rgba(142, 142, 142, 0.1);
    border-radius: 5px 5px 5px 5px;

    &__item {
      cursor: pointer;
      margin-bottom: 20px;
      color: #000;
      display: flex;
      flex-direction: row;
      align-items: center;

      &:hover {
        color: #2977fe;
      }

      &-text {
        font-size: 18px;
        line-height: 20px;
      }

      img {
        width: 36px;
        height: 36px;
        margin-right: 8px;
      }
    }
  }
}

.nav-children-2 {
  min-height: auto;
  .nav-children__group {
    &-item {
      min-height: 156px;
    }

    &:hover,
    &.active {
      font-weight: 400;
      color: #333333;
      background: #ecf0fb;
    }
  }

  .nav-children__content {
    flex: 1;
    padding: 0 0 0 30px;
    align-items: center;
    border-bottom: 1px solid rgba(41, 109, 204, 0.1);
  }
}

.nav-children__group {
  width: 200px;
  background: #ecf0fb;
  opacity: 1;

  &-item {
    min-height: 79px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid rgba(41, 109, 204, 0.1);
    font-size: 18px;
    font-family: PingFang SC-Medium, PingFang SC;
    cursor: pointer;

    &:hover,
    &.active {
      font-weight: bold;
      color: #2977fe;
      background: #d5e0ff;
    }
  }
}

.nav-children__content {
  flex: 1;
  padding: 0 0 0 80px;

  &.column-2 &-item {
    width: 33.33%;
  }

  &.inline &-item {
    height: 96px;
    min-width: 210px;
    display: block;
    padding: 10px;

    &:hover {
      background: #ecf0fb;
    }
  }

  &.inline {
    .title {
    }
  }

  &.inline &-item::after {
    display: none;
  }

  &.column-3 &-item {
    height: 135px;
  }

  &-item {
    width: 25%;
    padding: 50px 0 19px 0;
    height: 165px;
    position: relative;
    cursor: pointer;

    &:hover {
      color: #2977fe;

      .desc {
        color: #2977fe;
      }
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      display: block;
      width: 150px;
      height: 0px;
      opacity: 0.2;
      border-bottom: 2px solid #296dcc;
      /* border: 1px dashed rgba(41, 109, 204, 1); */
    }
  }

  .icon {
    width: 36px;
    height: 36px;
  }

  .title {
    font-size: 16px;
    margin: 5px 0;
  }

  .desc {
    font-size: 14px;
    color: #666666;
  }
}

/deep/ .nav-btn {
  width: 151px;
  height: 39px;
  line-height: 39px;
  background-color: #fff;
  color: #2977fe;
  border-radius: 5px 5px 5px 5px;
  padding: 0;
  text-align: center;
  font-size: 20px;
}

.sceneCode {
  &.blueTheme {
    /deep/ .nav-btn {
      background: #2977FE !important;
      color: #fff;
    }
  }
}

.nav.scroll {
  /deep/ .nav-btn {
    color: #fff;
  }
}
</style>
