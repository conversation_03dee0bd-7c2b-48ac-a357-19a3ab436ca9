<template>
  <div class="hot_message" v-if="hotMessageList && hotMessageList.length">
    <div class="hot_message-title">热门文章</div>
    <div class="hot_message-list">
      <div class="hot_message-item" v-for="(item, index) in hotMessageList" :key="item.id">
        <div :class="`hot_message-item-index hot_message-item-index${index}`">
          {{ index < 9 ? "0" : "" }}{{ index + 1 }}
        </div>
        <a v-if="item.id && item?.title?.rendered" :href="`https://www.sdongpo.com/xueyuan/c-${item.id}.html`" class="hot_message-item-link" target="_black">
          <span class="hot_message-item-content">{{ item?.title?.rendered || '' }}</span>
          <span v-if="item.date" class="hot_message-item-date">{{ new Date(item.date).toISOString().slice(0, 10) }}</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
// 相关文章
import { ref } from 'vue';

const hotMessageList = ref([])

const _getHotMessageList = async () => {
  const messageList = await getHotNewMessageList();
  if (messageList) {
    hotMessageList.value = messageList;
  }
}
_getHotMessageList()

</script>

<style lang="postcss" scoped>
.hot_message {
  width: 270px;

  &-title {
    font-size: 24px;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #333333;
    padding: 10px 0;
    border-bottom: 1px #e5e6eb solid;
  }

  &-item {
    display: flex;
    flex-wrap: nowrap;
    align-items: flex-start;
    margin-top: 20px;
    padding-bottom: 15px;
    border-bottom: 1px #e5e6eb dashed;

    &-link {
      display: flex;
      flex-wrap: wrap;
    }

    &-content {
      color: #333;
      font-size: 16px;
    }

    &-date {
      color: #a3a4b1;
      font-size: 13px;
      margin-top: 3px;
    }

    &-index {
      font-size: 30px;
      font-weight: bold;
      line-height: 1;
      padding-right: 5px;
      color: #e0e0e0;
    }

    &-index0 {
      color: #e56661;
    }

    &-index1 {
      color: #f2a259;
    }

    &-index2 {
      color: #f8d37b;
    }
  }
}
</style>