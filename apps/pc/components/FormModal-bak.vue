/**
**/

<template>
  <div class="modal__mask" v-show="_showModal">
    <div class="modal__main" :class="{ modal__main_price: isPriceModal }">
      <div class="modal__header" v-if="isCommonModal || isDatumModal">
        <img class="logoimg" src="~/assets/common/logo.png" alt="logo" />
        <span class="divider">|</span>
        <span>生鲜SaaS ERP</span>
        <img
          @click="handleCloseModal"
          class="modal__close"
          src="https://website-image.sdongpo.com/website/icon--close.png"
          alt="关闭"
        />
      </div>
      <div class="modal__header" v-if="isPriceModal">
        <img class="logoimg" src="~/assets/common/logo-white.png" alt="logo" />
        <h2>免费获取方案报价</h2>
        <p>提交信息5秒获取最新报价</p>
        <img
          @click="handleCloseModal"
          class="modal__close"
          src="https://website-image.sdongpo.com/website/icon--close.png"
          alt="关闭"
        />
      </div>
      <div class="modal__content form-modal">
        <div class="modal__content_price" v-if="isPriceModal">
          <p>预估报价</p>
          <p class="modal__content_price_num">
            {{ expectPrice }}
            <span>元</span>
          </p>
        </div>
        <TheForm ref="form" @on-change="handleChange"></TheForm>
      </div>
      <div class="modal__btn_getPrice" v-if="isPriceModal" @click="handleSaveFormData">点击获取报价方案</div>
      <div class="modal__btn" v-if="isCommonModal || isDatumModal" @click="handleSaveFormData">立即申请</div>
      <div class="modal__tips">（如有任何疑问请拨打7*24小时咨询热线：400-075-1863）</div>
    </div>
  </div>
</template>
<script setup>
let formData = {};
const form = ref(null);
const _showModal = showModal;
const expectPrice = ref(0);
const isPriceModal = computed(() => {
  return currentModalType.value === ModalType.price;
});
const isCommonModal = computed(() => {
  return currentModalType.value === ModalType.common;
});
const isDatumModal = computed(() => {
  return currentModalType.value === ModalType.datum;
});

const priceList = {
  4: 45600,
  3: 98000,
  2: 168000,
  1: 168000,
};
const handleChange = data => {
  formData = data;
  if (isPriceModal && formData.username && formData.phone) {
    expectPrice.value = priceList[formData.user_level] || '';
  }
};
const handleCloseModal = () => {
  expectPrice.value = 0;
  form.value.resetForm();
  closeModal();
};
const handleSaveFormData = async () => {
  const validate = form.value.validate();
  if (!validate.valid) {
    showError(validate.error);
    return;
  }
  const { data } = await saveFormData(formData);
  const res = JSON.parse(data.value);
  showSuccess(res.message);
  if (res.status === 'success') {
    expectPrice.value = 0;
    closeModal();
  }
};
</script>
<style scoped lang="postcss">
.modal__mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
}

.modal__main {
  padding: 30px;
  background-color: #fff;
  font-size: 16px;
  display: inline-block;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.modal__header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #047eef;
  margin-bottom: 27px;
  margin-top: 30px;
  position: relative;
  .logoimg {
    width: 140px;
  }
}

.divider {
  margin: 0 10px;
}

.modal__header img {
  width: 75px;
}

.modal__header .modal__close {
  position: absolute;
  width: 28px;
  top: -40px;
  right: -10px;
  cursor: pointer;
}

/deep/ {
  .form-item {
    display: flex;
    flex-direction: row;
    margin-bottom: 24px;
    vertical-align: middle;
  }

  .form-item__required {
    color: #ff3d15;
  }

  .form-item__label-tips {
    font-size: 12px;
    color: #b2b2b2;
  }

  .form-item__label {
    text-align: right;
    padding-top: 10px;
    width: 72px;
  }

  .form-item__content {
    padding-left: 16px;
    max-width: 360px;
  }

  .employee-num__item {
    width: 152px;
    height: 50px;
    line-height: 50px;
    border: 1px solid #cecece;
    border-radius: 6px;
    text-align: center;
    margin-right: 20px;
    margin-bottom: 14px;
    display: inline-block;
    cursor: pointer;
  }

  .employee-num__item.active {
    color: #fff;
    background-color: #ff3d15;
    border: 1px solid #ff3d15;
  }

  .form-item__input {
    border: 1px solid #cecece;
    border-radius: 6px;
    outline: 0;
    width: 330px;
    height: 50px;
    line-height: 50px;
    padding: 14px;
  }
}

.modal__btn {
  text-align: center;
  height: 50px;
  line-height: 50px;
  background: linear-gradient(90deg, #ff7214, #ff1616);
  border-radius: 6px;
  font-size: 20px;
  color: #fff;
  cursor: pointer;
  margin-top: -10px;
}

.modal__tips {
  color: #666;
  margin-top: 30px;
  width: 100%;
  text-align: center;
  font-size: 16px;
}

.modal__main_price {
  padding: 0px 0px 30px;
  border-radius: 15px 15px 15px 15px;
  .modal__header {
    height: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #047eef;
    margin-top: 0px;
    margin-bottom: 27px;
    position: relative;
    background: url('../assets/common/modal_bg.png') no-repeat;
    background-size: 100% 100%;
    .modal__close {
      position: absolute;
      width: 28px;
      top: 15px;
      right: 15px;
      cursor: pointer;
    }

    .logoimg {
      width: 76px;
      position: absolute;
      top: 15px;
      left: 15px;
    }
    h2 {
      padding-top: 27px;
      padding-bottom: 14px;
      font-size: 32px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 38px;
    }
    p {
      font-size: 16px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      line-height: 19px;
    }
  }
  .modal__content {
    padding: 0px 30px;
  }
  .modal__content_price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 411px;
    height: 60px;
    margin: 24px 0px 24px 9px;
    padding: 0px 20px;
    background: #ffffff;
    border-radius: 15px 15px 15px 15px;
    border: 2px solid #2977fe;
    p {
      font-size: 18px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #2977fe;
    }
    .modal__content_price_num {
      font-size: 24px;
      span {
        font-size: 18px;
        margin-left: 19px;
      }
    }
  }
  /deep/ {
    .employee-num__item {
      border: 1px solid #2977fe;
    }
    .employee-num__item.active {
      color: #fff;
      background-color: #2977fe;
      border: 1px solid #2977fe;
    }
    .form-item__content input {
      border: 1px solid #2977fe;
    }
  }
}
.modal__btn_getPrice {
  text-align: center;
  height: 50px;
  line-height: 50px;
  font-size: 20px;
  color: #fff;
  cursor: pointer;
  margin: 0px 20px;
  margin-top: -10px;
  background: #2977fe;
  border-radius: 25px 25px 25px 25px;
}
</style>
