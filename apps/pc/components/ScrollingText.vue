<template>
  <div class="scroll-container">
    <div class="scroll-content" :style="contentStyle" :class="animationClass">
      <transition-group name="scroll-animation">
        <div v-for="(segment, index) in segments" :key="index" class="scroll-segment">
          {{ segment }}
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    direction: {
      type: String,
      default: 'left' // 默认向左滚动
    },
    text: {
      type: String,
      default: ''
    },
    segmentSpacing: {
      type: Number,
      default: 30 // 默认小段之间的间距为30px
    }
  },
  setup(props) {
    const segments = ref([]);

    watch(
      () => props.text,
      (newText) => {
        // 将文字按逗号分隔成小段 为了模拟无限滚动，多复制几份数据
        newText = newText + ',' + newText + ',' + newText + ',' + newText
        segments.value = newText.split(',');
      },
      { immediate: true }
    );

    const contentStyle = computed(() => {
      return {
        'animation-duration': `${animationDuration.value}s`
      };
    });

    const animationClass = computed(() => {
      return props.direction === 'right' ? 'scroll-animation-reverse' : 'scroll-animation';
    });

    const animationDuration = computed(() => {
      // 根据文字总长度和滚动速度计算动画时长
      // const textLength = segments.value.reduce((length, segment) => length + segment.length, 0);
      // return textLength / Math.abs(scrollSpeed.value) * 10;
      return 60
    });

    const scrollSpeed = computed(() => {
      // 根据滚动方向计算滚动速度
      return props.direction === 'left' ? 50 : -50;
    });

    return {
      segments,
      contentStyle,
      animationClass,
      animationDuration,
      scrollSpeed,
    };
  }
};
</script>

<style scoped>
.scroll-container {
  display: inline-block;
  overflow: hidden;
  align-items: center;
}

.scroll-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.scroll-animation {
  animation: scrollAnimation infinite linear;
}

.scroll-animation-reverse {
  justify-content: end;
  animation: scrollAnimationReverse infinite linear;
}

.scroll-segment {
  margin-right: 10px; /* 设置小段之间的间距 */
}

@keyframes scrollAnimation {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes scrollAnimationReverse {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}

.scroll-animation-enter-active,
.scroll-animation-leave-active {
  transition: transform 1s;
}

.scroll-animation-enter,
.scroll-animation-leave-to {
  transform: translateX(-100%);
}

</style>
