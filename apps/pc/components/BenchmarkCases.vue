<template>
  <footer ooter class="sidebar text-center" :style="containerStyle">
    <h3 class="sidebar__title">标杆案例</h3>
    <div class="w">
      <div class="sidebar__tab flex justify-center">
        <div
          class="tab_item"
          :class="{ active: activeTab === index }"
          v-for="(item, index) in items"
          @mouseenter="changeTab(index)"
        >
          {{ item.title }}
        </div>
      </div>
      <div class="sidebar__content">
        <a
          v-show="activeTab === index"
          v-for="(item, index) in items"
          class="content_item flex justify-center items-center"
          target="_blank"
          :href="item.link"
        >
          <p class="content_item__desc">
            {{ item.desc }}
          </p>
          <img :src="item.img" :alt="item.title" />
        </a>
      </div>
      <div></div>
    </div>
  </footer>
</template>
<script setup>
const activeTab = ref(0);
const changeTab = index => (activeTab.value = index);
const props = defineProps({
  bg: String,
  items: Array,
});
const containerStyle = computed(() => {
  return {
    backgroundImage: `url(${props.bg})`,
  };
});
</script>
<style lang="postcss" scoped>
.sidebar {
  color: #fff;
  background-repeat: no-repeat;
  background-position: top;
  background-size: cover;
  padding: 70px 0 142px;

  &__title {
    font: bolder 24px PingFang SC;
    margin-bottom: 40px;
  }

  &__tab {
    .tab_item {
      width: 300px;
      height: 40px;
      line-height: 40px;
      font-size: 20px;

      &:not(.active) {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    .active {
      background-color: #2173ff;
    }
  }

  &__content {
    .content_item {
      margin-top: 70px;
      &__desc {
        width: 550px;
        font-size: 16px;
        padding-right: 50px;
      }
      img {
        width: auto;
        height: 150px;
      }
    }
  }
}
</style>
