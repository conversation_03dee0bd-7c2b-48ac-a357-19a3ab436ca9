<template>
<TheHeader class="scroll"></TheHeader>
<div class="errorWrap">
  <img v-if="errCode==404" class="center_img" src="../assets/common/404.png" alt="500">
  <img v-else class="center_img" src="../assets/common/500.png" alt="500">
  <p v-if="errCode==404" class="describe">很抱歉，您要访问的地址有误......</p>
  <p v-else class="describe">很抱歉，服务器开小差了... </p>
  <BackButton ></BackButton>
</div>
</template>

<script setup>
const props = defineProps({
  errCode: {
    type: [String, Number],
    required: true,
    default:'404'
  },
});
</script>

<style scoped lang="postcss">
.errorWrap{
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.center_img{
  height: 500px;
  width: 500px;
}
.describe{
  font-size: 24px;
  color: #666666;
  text-align: center;
}
</style>