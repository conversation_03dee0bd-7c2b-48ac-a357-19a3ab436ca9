<template>
  <div class="car-img">
    <el-carousel
      height="350px"
      ref="carouselRef"
      indicator-position="outside"
      :autoplay="isAutoplay"
      :interval="4000"
      :loop="true"
      :active-index="activeIndex"
      @change="handleCarouselChange"
    >
      <el-carousel-item v-for="(item, index) in slides" :key="index">
        <div class="carousel-item">
          <img :src="item.image" class="carousel-image"/>
          <div class="carousel-text">
            <h2 class="carousel-text-title" style="font-size: 25px;">{{ item.title }}</h2>
            <h3 class="carousel-text-explain" style="font-size: 15px;">{{ item.explainText }}</h3>
            <p v-for="(point, i) in item.points" :key="i" class="point" style="font-size: 14px;padding-bottom:2px;">{{ point }}</p>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
    <div class="custom-indicators">
      <span
        v-for="(item, index) in slides"
        :key="index"
        :class="['indicator', { active: index === activeIndex }]"
        @click="setActive(index)"
      ></span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import company_icon4 from "~/assets/zhcg/company_icon4.png";
import company_icon1 from "~/assets/zhcg/company_icon1.png";
import company_icon2 from "~/assets/zhcg/company_icon2.png";
import company_icon3 from "~/assets/zhcg/company_icon3.png";

const slides = ref([
  {
    image: company_icon1,
    title: "遵义市红花岗区校园阳光采购平台",
    explainText:
      "遵义市红花岗区教体局为了解决区里中小学食材统一采购问题，搭建了遵义市首个针对122家学校的服务平台",
    points: [
      "通过智能化管理工具解决了各学校订单汇总问题，供应商直供，实现对商品流、物流的全面管理",
      "学校可对每个单品进行采购成本分析、数量汇总，并输出报表",
      "财务上明确了解每个食堂的商品数量以及金额、每个供应商配送商品数量及金额，效率提升至少60%，2-3人即可轻松实现后勤食材保障",
      "教体局随时查看食堂采购价格、数量，查看供应商的服务评分。",
    ],
  },
  {
    image: company_icon2,
    title: " 上海市崇明县教育局阳光采购平台 ",
    explainText:
      "上海市崇明县教育局是上海市崇明县人民政府主管全县教育工作的职能部门，解决区里100多所中小学统一学校下单问题，教育局监管订单和采购，减少规避出错，通过软件加强管理管控",
    points: [
      "教育局统一制定菜谱，响应教育部要求的食堂选餐制",
      "对供应商、食堂行为进行统一管控，平台通过线上询价、竞价，增强学校管控力",
      "方便供应商自行管理商品，直接生成账单，与档口/商户直接对账", 
      "食安溯源，所有食材的溯源信息都有据可查，保障食品安全", 
    ],
  },
  {
    image: company_icon4,
    title: "临沂市沂河新区智慧食安平台",
    explainText:
      "临沂市沂河新区为了提升农副产品流通效率和增强食品安全防控能力，由临沂食安搭建智慧食安平台，通过平台实现统一的食材下单平台、供应链效率提升、食安溯源",
    points: [
      "实现公开透明的采购模式，学校、企事业单位等可以自主的选择供应商下单",
      "供应商通过系统分拣配送以及上传检测报告，极大提升供应商的配送效率，目前有60多家供应商入驻平台",
      "相关监管部门可以通过平台查看食品交易数据、价格指数、溯源信息等",
    ],
  }
]);
// {
//     image: company_icon3,
//     title: "郑州管城区教育局阳光采购平台",
//     explainText:
//       "郑州管城区有64所中小学校，所有学校的食材采购统一在教育平台上下单，通过平台对全地区学校食堂进行统一管理，建立供应商的询价、竞价、定价机制，降低采购成本，提高食材安全性",
//     points: [
//       "建立教育局（学校）食材采购商城，食材采购直接在商城下单，大大减少下单时间，提高学校食材采购工作效率、减少人工、避免差错率",
//       "对供应商实现竞价比价，并可对所有食材产品、供应商的价格进行横向、纵向对比，给学校食材采购提供数据参考",
//       "所有进入学校的食材实现溯源管理，实现商品的溯源功能",
//       "供应商分拣配送，供应商配送的数据与学校收货、验货数据实现实时对接",
//     ],
//   },

const activeIndex = ref(0);
const carouselRef = ref(null);
const isAutoplay = ref(true);

const setActive = (index) => {
  activeIndex.value = index;
  carouselRef.value?.setActiveItem(index);
  isAutoplay.value = false;
  setTimeout(() => {
    isAutoplay.value = true; 
  }, 100);
};
const handleCarouselChange = (index) => {
  console.log("carousel change", index);
  
  activeIndex.value = index;
};



// onMounted(() => {
//   const carousel = document.querySelector(".el-carousel__container");
//   if (carousel) {
//     carousel.addEventListener("transitionend", () => {
//       const activeEl = document.querySelector(".el-carousel__item.is-active");
//       if (activeEl) {
//         activeIndex.value = [...activeEl.parentElement.children].indexOf(activeEl);
//       }
//     });
//   }
// });
</script>

<style scoped>
:deep(.el-carousel__indicators--outside) {
  display: none;
}
.point {
  font-size: 18px;
  position: relative;
}
.point::before {
  content: "";
  position: absolute;
  left: -15px; /* 调整圆点的位置 */
  top: 12px;
  transform: translateY(-50%); /* 垂直居中 */
  width: 6px;
  height: 6px;
  background-color: #3e74f6ff;
  border-radius: 50%;
}
.car-img {
  position: relative;
  width: 1400px;
  height: 350px;
}

.carousel-item {
  display: flex;
  /* align-items: center;
  justify-content: space-between; */
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 0px;
  border-radius: 15px;
}

.carousel-image {
  width: 50%;
  height: 100%;
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}

.carousel-text {
  width: 50%;
  height: 100%;
  padding-top: 25px;
  padding-bottom: 20px;
  padding-left: 70px;
  padding-right: 50px;
}

h2 {
  color: #0056b3;
  font-size: 20px;
  margin-bottom: 10px;
}

p {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}
.carousel-text-title {
  color: #3e74f6ff;
  font-size: 30px;
  padding-bottom: 10px;
  margin-left: -16px;
  font-weight: 600;
}
.carousel-text-explain {
  color: #333333ff;
  font-size: 20px;
  line-height: 1.5;
  margin-bottom: 15px;
  margin-left: -16px;
  font-weight: 600;
}

/* 自定义指示器 */
.custom-indicators {
  position: absolute;
  bottom: -130px;
  left: 700px;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid white;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s;
  opacity: 0.7;
}

.indicator.active {
  background: #007bff;
  border: 2px solid #007bff;
  opacity: 1;
}
</style>
