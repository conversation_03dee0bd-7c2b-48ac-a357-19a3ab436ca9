<template>
  <div class="l-r-layout">
    <div class="sys_item" v-if="!isRight">
      <h3 class="top_content__title">
        <img
          class="top_content__title_img"
          :src="dataS.top_title_icon"
          :alt="dataS.titleTop"
        />
        <div>{{ dataS.titleTop }}</div>
      </h3>
      <div class="w flex justify-between items-center">
        <div
          class="sys_item_detail"
          v-if="dataS.titleCenterTextList && dataS.itemDescList"
        >
          <h3
            v-for="(item, index) in dataS.titleCenterTextList"
            :key="index"
            class="ds_content__title"
            :id="`ds_content__title` + index"
          >
            {{ item }}
          </h3>
          <p class="title_after__text" v-if="dataS.titleAfterText">
            {{ dataS.titleAfterText }}
          </p>
          <img class="title_after_img" src="~/assets/ygsc/title_icon_left.png" alt="" />
          <p v-for="(item, index) in dataS.itemDescList" :key="index" class="item_desc">
            {{ item }}
          </p>
        </div>
        <div class="flex-shrink-0">
          <img
            :src="dataS.imgUrl"
            class="change_img"
            alt="校园食材阳光采购平台运营后台"
            width="660"
          />
        </div>
      </div>
    </div>
    <div class="sys_item" v-if="isRight">
      <div class="w flex justify-between items-center">
        <div class="flex-shrink-0" :class="isRight ? 'rightImg' : ''">
          <img
            :src="dataS.imgUrl"
            class="change_img"
            alt="校园食材阳光采购平台运营后台"
            width="660"
          />
        </div>
        <div
          class="sys_item_detail"
          v-if="dataS.titleCenterTextList && dataS.itemDescList"
        >
          <h3 class="top_content__title">
            <img
              class="top_content__title_img"
              :src="dataS.top_title_icon"
              :alt="dataS.titleTop"
            />
            <div>{{ dataS.titleTop }}</div>
          </h3>
          <h3
            v-for="(item, index) in dataS.titleCenterTextList"
            :key="index"
            class="ds_content__title"
            :id="`ds_content__title` + index"
          >
            {{ item }}
          </h3>
          <p class="title_after__text" v-if="dataS.titleAfterText">
            {{ dataS.titleAfterText }}
          </p>
          <img class="title_after_img" src="~/assets/ygsc/title_icon_left.png" alt="" />
          <p v-for="(item, index) in dataS.itemDescList" :key="index" class="item_desc">
            {{ item }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import top_title_icon from "~/assets/zhcg/top_title_icon.png";
import gystj from "~/assets/zhcg/gystj.png";
const props = defineProps({
  dataSource: {
    type: Object,
    required: true,
    default: () => ({
      titleTop: "食材阳光采购平台",
      imgUrl: gystj,
      imgAlt: "校园食材阳光采购平台运营后台",
      imgWidth: 660,
      imgHeight: 0,
      titleCenterTextList: ["多维度食材价格管控", "让食材采购监管更透明"],
      titleAfterText: "",
      itemDescList: [
        "通过信用评价体系把控食安，清退不达标供应商",
        "大宗商品在线招采竞价，可按周按月管理商品",
        "历史采购数据自动统计，设有报价预警红线保",
        "一键生成对账明细，平台支持线上对账资金路径清晰",
      ],
      titleAfterImg: "~/assets/ygsc/title_icon_left.png",
      titleAfterImgAlt: "",
      titleAfterImgWidth: 330,
      titleAfterImgHeight: 6,
      top_title_icon: top_title_icon,
      top_title_icon_alt: "",
      top_title_icon_width: 24,
      top_title_icon_height: 24,
    }),
  },
  isRight: {
    type: Boolean,
    default: false,
  },
});
let dataS = props.dataSource;
let isRight = props.isRight;
</script>

<style scoped>
.rightImg {
  margin-top: 76px;
}
.ds_content__title {
  color: #3e74f6;
  font-size: 24px;
  font-weight: bold;
}

.title_after__text {
  font-size: 16px;
  color: #333;
  margin: 10px 0;

  /* &::after {
    content: "";
    display: block;
    width: 330px;
    height: 6px;
    margin: 10px 0 30px;
    background: url(~/assets/ygsc/title_icon_left.png) no-repeat center/cover;
  } */
}

.item_desc {
  position: relative;
  margin: 0 0 30px 20px;
  color: #333;
  font-size: 20px;

  &::before {
    content: "";
    position: absolute;
    top: 4px;
    left: -22px;
    display: inline-block;
    width: 15px;
    height: 15px;
    background: url(~/assets/ygsc/item_icon.png) no-repeat center/cover;
  }
}

.w {
  width: 1100px;
}

.sys_item {
  padding: 70px 0 76px;
  .top_content__title {
    display: flex;
    align-items: center;
    color: #000000ff;
    font-size: 24px;
    font-weight: bold;
    padding-bottom: 40px;
    gap: 8px;
    .top_content__title_img {
      width: 30px;
    }
  }
  .title_after_img {
    margin-top: 10px;
    margin-bottom: 20px;
  }

  &:nth-child(2n + 1) {
    /* background: #f9fbff; */
  }

  &:nth-child(2n + 1) {
    .sys_item_detail {
      /* transform: translateX(100px); */
    }
  }

  /* &:nth-child(2n) .title_after__text::after {
    background-image: url(~/assets/ygsc/title_icon_left.png);
  } */

  &:nth-child(1) {
    .ds_content__title::after {
      transform: translateX(-82px);
    }

    img {
      width: 500px;
    }
  }
}
</style>
