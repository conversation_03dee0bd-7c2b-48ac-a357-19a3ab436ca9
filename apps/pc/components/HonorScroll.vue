<!--
 * @Description: 
 * @Date: 2023-05-25 09:54:57
 * @LastEditors: hgj
 * @LastEditTime: 2023-05-25 10:57:24
 * @FilePath: /shudongpo-website/apps/pc/components/HonorScroll.vue
-->
<template>
  <div class="relative sidebar">
    <div class="absolute top-0 left-0 sidebar__hidden"></div>
    <div class="absolute top-0 right-0 sidebar__hidden"></div>
    <div class="overflow-hidden">
      <div class="flex animation-scroll">
        <img loading="lazy" src="~/assets/common/honor-icon-1.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-2.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-3.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-4.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-5.png" alt="公司荣誉" width="170" />
        
        <img loading="lazy" src="~/assets/common/honor-icon-1.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-2.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-3.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-4.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-5.png" alt="公司荣誉" width="170" />

        <img loading="lazy" src="~/assets/common/honor-icon-1.png" alt="公司荣誉" width="170" />

      </div>
      <div class="flex animation-scroll">
        <img loading="lazy" src="~/assets/common/honor-icon-6.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-7.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-8.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-9.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-10.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-11.png" alt="公司荣誉" width="170" />

        <img loading="lazy" src="~/assets/common/honor-icon-6.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-7.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-8.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-9.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-10.png" alt="公司荣誉" width="170" />
        <img loading="lazy" src="~/assets/common/honor-icon-11.png" alt="公司荣誉" width="170" />

        <img loading="lazy" src="~/assets/common/honor-icon-6.png" alt="公司荣誉" width="170" />
      </div>
    </div>
  </div>
</template>
<script setup></script>
<style scoped lang="postcss">
img {
  margin-right: 13px;
}
.sidebar {
/*  background-color: #FFFFFF;*/
  margin: 0 auto;
  width: 1650px;
  img {
    margin-right: 50px;
    width: 170px;
    height: 130px;
  }
  &__hidden {
    width: 100px;
    height: 100%;
    z-index: 1;
    &.left-0 {
      background-image: linear-gradient(to right, rgb(255, 255, 255), rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
    }
    &.right-0 {
      background-image: linear-gradient(to left, rgb(255, 255, 255), rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
    }
  }
}
@keyframes scroll-horizon {
  0% {
    transform: translate(0);
  }
  to {
    transform: translate(calc(-9vw * 6));
  }
}
@keyframes scroll-horizon2 {
  0% {
    transform: translate(0px);
  }
  to {
    transform: translate(calc(-9vw * 6));
  }
}
.animation-scroll {
  margin-bottom: 25px;
  animation: scroll-horizon 30s linear infinite;
  &:nth-child(2) {
    margin-left: -81px;
    animation: scroll-horizon2 30s linear infinite;
  }
}
</style>
