<!--
 * @Description: 
 * @Date: 2023-06-06 11:51:16
 * @LastEditors: hgj
 * @LastEditTime: 2023-06-09 15:35:33
 * @FilePath: /shudongpo-website/apps/pc/components/studyCenterTabs.vue
-->
<template>
  <div class="study-center-tabs-box">
    <el-tabs v-model="activeName" @tab-change="jumpDetail">
      <el-tab-pane v-for="(tab, index) in tabList" :key="tab.path"  :name="tab.path">
        <template #label>
          <NuxtLink :to="tab.path" style="">
            {{ tab.name }}
          </NuxtLink>
        </template>
        <slot/>
      </el-tab-pane>
    </el-tabs>
  </div>
  
</template>

<script setup>
const props = defineProps({
  activeName: String,
})
const tabList = [
  {
    name: '生鲜课堂',
    path: '/sxkt/',
    params: {},
  },
  {
    name: '央厨课堂',
    path: '/yckt/',
    params: {},
  },
  {
    name: '资料下载',
    path: '/zlxz/',
    params: {},
  },
  {
    name: '行业会议',
    path: '/hymt/',
    params: {},
  },
  {
    name: '专家咨询',
    path: '/epsf/',
    params: {},
  },
  {
    name: '新闻报道',
    path: '/xueyuan/',
    params: {},
  },
]
const activeName = ref(tabList[0].path)
const initTab = () => {
  if (props.activeName) {
    activeName.value = props.activeName
    return
  }
  const route = useRoute();
  console.log('route', route.path)
  if(route.path) {
    activeName.value = route.path
  }
}
initTab()
const router = useRouter()
const jumpDetail = (url) => {
  console.log('url----', url)
  if(!url) return 
  router.push(url)
}
</script>
<style lang="postcss" scoped>
.study-center-tabs-box {
  /deep/.el-tabs {
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__nav-scroll {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-tabs__item {
      padding: 30px 45px;
      font-size: 22px;
    }
    .el-tabs__item :not(:has(.is-active)) {
      color: #333333;
    }
  }
}
</style>
