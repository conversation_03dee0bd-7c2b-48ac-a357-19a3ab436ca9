/*
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 17:37:14
 * @LastEditors: hgj
 * @LastEditTime: 2023-06-09 17:01:19
 */
const formInitData = {
  username: '',
  phone: '',
  companyName: '',
  user_level: 4,
};

type Datum = {
  post_title: string;
  post_content: string;
  link: string;
  out: boolean;
};

export const cacheDuration = 60 * 60; // 设置缓存时长，单位为秒，1小时

export const dataGramList = ref<Datum[]>([])
export const activeGram = ref<Datum>({
  post_title: '',
  post_content: '',
  link: '',
  out: false,
});

export const noticeDialog = ref(false)
export const getDataGram = async () => {
  const res = await getStudyCenterPages('资料包')
  if (res) {
    // @ts-ignore
    dataGramList.value = res.data.list || []
    if (dataGramList.value.length === 0) return
    activeGram.value = dataGramList.value[0]
  }
}

export const formDataObj = reactive({
  ...formInitData,
});
export function changeLink(data: any = []) {
  data.forEach((res: any) => {
    if (res.out) {
      res.link = window.location.origin + res.link;
    }
    if (res.children) {
      changeLink(res.children);
    }
  });
}
export const resetForm = () => {
  formDataObj.username = formInitData.username;
  formDataObj.phone = formInitData.phone;
  formDataObj.companyName = formInitData.companyName;
  formDataObj.user_level = formInitData.user_level;
  currentModalType.value = ModalType.common
};
export const phonePattern = /^1[0-9]{10}$/;
export const showError = (msg: string) => alert(msg);
export const showSuccess = (msg: string) => alert(msg);
export const showModal = ref(false);

// 表单类型直接在这里维护和拓展
export enum ModalType {
  common = '试用',
  price = '报价',
  datum = '下载',
  xsc = '学生餐活动'
}
export const currentModalType = ref<ModalType>(ModalType.common);
export function openModal(type : ModalType | Event){
  // 通过@click事件未传参调用openModal时会默认传递一个事件对象，影响默认参数的判断
  if (type instanceof Event) {
    type = ModalType.common;
  }
  currentModalType.value = type || ModalType.common;
  showModal.value = true;
}
export function closeModal() {
  resetForm();
  showModal.value = false;
}

type PageSceneMap = {
  [key: string | number]: string;
};

const pageMap: PageSceneMap = {
  '': '首页',
  '/': '首页',
  '/shicai': '生鲜',
  '/processing': '央厨',
  '/xsyyc': '营养餐',
  '/roujg': '肉类',
  '/qsy': '企事业',
  '/p2': '餐饮',
  '/tuanshan': '团膳',
  '/gxst': '高校',
  '/jcjg': '净菜',
  '/yzc': '预制菜',
}


export const getPageSceneName = () => {
  const pageName = window.location.pathname
  const pageNameFilter = pageName.replace(/\/$/, '')
  const page = pageMap[pageNameFilter]
  if (!page) return undefined;
  const scene = currentModalType.value

  // 自定义渠道逻辑, 如果链接里的query带scene参数, 则使用scene参数
  const query = useRoute().query
  const { scene: customScene } = query || {}

  return `${page}-${scene}${customScene ? '-' + customScene  : ''}${activeGram.value ? '-' + activeGram.value?.post_title : ''}`
}
export async function saveFormData(params: any = {}) {
  const data = {
    ...params,
    companyName: params.companyName || params.username,
  }
  if (getPageSceneName()) {
    data.scene = getPageSceneName()
  }
  const res = await useFetch('/index/save', {
    method: 'POST',
    server: false,
    headers: {
      'content-type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams(data),
  });
  return res;
}
export async function getArticleList() {
  const params = {
    category_name: 'shudongpozixun',
    orderby: 'date',
    order: 'desc',
    per_page: '15',
    no_link: '1',
    page: '1',
  };
  console.time('fetch');
  const { data } = await useFetch( '/wp-json/website/posts', {
    params,
  });
  console.timeEnd('fetch');
  return unref(data);
}
export const getBaseUrl = () => {
  const config = useRuntimeConfig()
  return config.VITE_API_PREFFIX || config.public.VITE_API_PREFFIX
}
export const getStudyCenterPages = async (module: String) => {
  const params = {
    module: module,
  }

  const { data } = await useFetch(getBaseUrl() + '/wp-json/website/pages', {
    params
  });
  return unref(data)
}
export const getStudyCenterMedia = async (module: String) => {
  const params = {
    module: module,
  }

  const { data } = await useFetch('/wp-json/website/media', {
    params
  });
  return unref(data)
}
export const getNewsCategory = async () => {

  const { data } = await useFetch('/wp-json/website/categories', {
  })
  return unref(data)
}


export const getListOrderByIdDesc = async () => {
  const { data } = await useFetch('/wp-json/website/posts', {
    params: {
      orderby: 'id',
      order: 'desc',
      page: 1,
      per_page: 1,
      action: 'cache_max_page_and_max_post_id'
    },
  })
  return unref(data)
}

export const getNewsList = async (category_name: string, page: number) => {

  const { data } = await useFetch('/wp-json/website/posts', {
    params: {
      category_name,
      page,
      per_page: 9
    },
  })
  return unref(data)
}

let getNewsDetailConter = 0
export const getNewsDetail = async (id: string) => {
  console.log('获取新闻详情', getNewsDetailConter++)
  const { data } = await 
    useFetch("/wp-json/website/post", {
      params: {
        id,
      },
    })
  return unref(data);
};

let getNewMessageListConter = 1
export const getNewMessageList = async (req: any) => {
  try {
    const params: any = {};
    if (req.order) params.order = req.order;
    if (req.orderby) params.orderby = req.orderby;
    if (req.per_page) params.per_page = req.per_page;
    if (req.search) {
      if (Array.isArray(req.search)) {
        params.search = req.search.join(' ');
      } else {
        params.search = req.search;
      }
    }
    console.log('getNewMessageListConter', getNewMessageListConter++, params)
    const { data } = await 
    useFetch("/wp-json/wp/v2/posts", {
      method: 'GET',
      params
    })
    console.log("/wp-json/wp/v2/posts", data)
    return unref(data);
  } catch (error) {
    console.log('errorrrrr', getNewMessageListConter++, error)
    return unref(null);
  }
};

// 相关文章
let getMessageListConter = 0
export const getMessageList = async (req: any) => {
  try {
    console.log('相关文章', getMessageListConter++)
    const params: any = {};
    if (req.id) params.id = req.id;
    const { data } = await 
    // useFetch("https://www.sdongpo.com/wp-json/website/hot_posts", {
    useFetch(`https://web-search.sdongpo.com/api/website/lunece/query?id=${params.id}`, {
      method: 'GET',
            key: `getLastMessageList-${params.id}`,
    })
    return unref(data);
  } catch (error) {
    return unref(null)
  }
};

// 热门资讯
let getHotMessageListCounter = 0
export const getHotNewMessageList = async () => {
  try {
    console.log('热门资讯', getHotMessageListCounter++)
    const params: any = {
      order: 'desc',
      orderby: 'date',
      tags: 5194,
      per_page: 10
    };
  
    const { data } = await 
    useFetch("/wp-json/wp/v2/posts", {
      method: 'GET',
      params,
      key: `getLastMessageList-${params.tags}-${params.per_page}`,
    })
    return unref(data);
  } catch (error) {
    console.log('error', error)
    return unref(null);
  }
};
 

// 最新资讯  /wp-json/wp/v2/posts
let getLastMessageListCounter = 0
export const getLastMessageList = async (req: any) => {
  try {
    console.log('最新资讯', getLastMessageListCounter++)
    const { data } = await 
    useFetch(`/wp-json/website/get_posts_by_max_id?max_id=${req.max_id}&per_page=${req.per_page}`, {
      method: 'GET',
      key: `getLastMessageList-${req.max_id}-${req.per_page}`,
    })
    return unref(data);
  } catch (error) {
    return unref(null);
  }
};

// 热门标签
let getHotTagsCounter = 0
export const getHotTags = async () => {
  try {
    console.log('热门标签', getHotTagsCounter++)
    const { data } = await 
    useFetch("/wp-json/website/popular_tag?per_page=15", {
      method: 'GET',
      key: `getHotTags`
    })
    return unref(data);
  } catch (error) {
    console.log('请求失败:', error);
    return unref(null)
  }
};

// 文章聚合页
export const getArtcleCollection = async (req: any) => {


  // 如果缓存不存在，则发起外部 API 请求
  const { data } = await useFetch(
    `/wp-json/wp/v2/posts?order=desc&orderby=date&per_page=20&tags_slug=${req.name}`, 
    {
      method: 'GET',
    }
  );


  return unref(data);
};
