<!--
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-28 10:32:19
 * @LastEditors: ddcoder zuo<PERSON><EMAIL>
 * @LastEditTime: 2023-04-10 14:35:28
-->
<script setup>
import FormModal from "~/components/FormModal.vue";
import { activeGram, getDataGram, noticeDialog } from "~/composables";

const funcStr = `(function(){
  var userAgent = window.navigator.userAgent;
  var isMobile =
    /phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone/i.test(
      userAgent,
    );
  if (isMobile) {
    var path = window.location.pathname;
    var mobilePath = path;
    if (path.startsWith("/shicai")) {
      mobilePath = "/shucai";
    }
    if (path.startsWith("/roufg")) {
      mobilePath = "/roulei";
    }
    var host = window.location.host;
    var mobileUrl  = host.includes("test")? 'https://testm.movee.cn'+mobilePath: 'https://m.sdongpo.com' + mobilePath;
    window.location.href = mobileUrl
  }
})()`


const _showModal = showModal;
const getMobileUrl = path => {
  const noMobilePath = [];
  let mobilePath = path;
  if (path.startsWith("/shicai")) {
    mobilePath = "/shucai";
  }
  if (path.startsWith("/roufg")) {
    mobilePath = "/roulei";
  }
  // 没有对应移动端页面的跳首页
  noMobilePath.forEach(item => {
    if (path.startsWith(item)) {
      mobilePath = "/";
    }
  });
  let host;
  // 判断是在服务端还是客户端获取host
  if (process.server) {
    // SSR 阶段使用 useRequestHeaders 获取 host
    const headers = useRequestHeaders();
    host = headers.host || ""; // 通过 headers 获取 host
  } else if (process.client) {
    // CSR 阶段使用 window.location.host 获取 host
    host = window.location.host;
  }

  if (host?.includes("test")) {
    return `https://testm.movee.cn${mobilePath}`;
  }
  return `https://m.sdongpo.com${mobilePath}`;
};

const route = useRoute();
const metaTags = computed(() => {
  const metaArray = [
    { name: "description", content: "蔬东坡提供专业的生鲜供应链SaaS解决方案，包括生鲜配送系统、蔬菜配送软件、食材配送软件等，为超过10000家生鲜配送企业提供数智服务" },
    { name: "keywords", content: "生鲜配送系统,生鲜配送软件,蔬东坡" },
  ];

  // 检查路径是否包含 '/tags'，如果包含则不添加 mobile-agent meta 标签
  if (!route.path.includes("/tags")) {
    metaArray.push({
      name: "mobile-agent",
      content: `format=html5; url=${getMobileUrl(route.path)}`,
    });
  }

  return metaArray;
});
useHead({
  htmlAttrs: {
    lang: "en",
  },
  charset: "utf-8",
  title: "蔬东坡生鲜配送软件系统,蔬菜食材配送系统,生鲜SaaS软件服务平台-蔬东坡",
  meta: metaTags.value,
  link: [
    {
      rel: "icon",
      href: "https://website-image.sdongpo.com/website/favicon-new.ico",
    },
    {
      rel: "canonical",
      href: `https://www.sdongpo.com${route.path}`,
    },
  ],
  script: [
    {
      innerHTML: funcStr,
      tagPosition: 'head'
    },
  ],
});

const { autoOpen, hasDataGram } = route.query;
if (autoOpen) {
  setTimeout(() => {
    openModal();
  }, 1000);
}
if (hasDataGram) {
  getDataGram();
}

onMounted(() => {
  // if (isMobile()) {
  //   location.href = getMobileUrl(location.pathname);
  // }
  setBaiduUrl();
});
</script>
<template>
  <NuxtLayout>
    <NuxtLoadingIndicator />
    <NuxtPage />
    <client-only>
      <FormModal :dataGramList="dataGramList"></FormModal>
    </client-only>
    <teleport to="body">
      <el-dialog width="400px" :close-on-click-modal="false" center v-model="noticeDialog" title="资料获取">
        <div v-if="activeGram.post_content" class="post-content" v-html="activeGram.post_content"></div>
      </el-dialog>
    </teleport>
  </NuxtLayout>
</template>
<style>
@import url(~/assets/style/common.css);
html,
body,
#__nuxt {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}
#__nuxt {
  overflow-y: scroll;
}
.el-message-me {
  z-index: 99999999999 !important;
}
.post-content {
  word-break: break-all;
  word-wrap: break-word;
  a {
    color: #2271b1;
    text-decoration: underline;
  }
}
</style>
