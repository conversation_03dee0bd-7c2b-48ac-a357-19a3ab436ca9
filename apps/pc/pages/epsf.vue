<!--
 * @Description: 
 * @Date: 2023-06-06 16:46:13
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-07-05 13:58:34
 * @FilePath: /shudongpo-website/apps/pc/pages/epsf.vue
-->
<template>
  <div class="epsf">
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">专家咨询</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">集企业管理咨询、培训、人才培养、品牌打造、行业探索与一体</h2>
      </div>
    </div>
    <StudyCenterTabs>
      <div class="g-block">
        <p class="title2">研究院简介</p>
        <div class="desc-box w">
          <img loading="lazy" src="../assets/epsf/logo.png" alt="研究院简介">
          <p class="desc-txt">
            生鲜研究院由蔬东坡发起设立,是国内最早成立的生鲜农产品领域的垂直研究机构。自成立以来,在北京、长沙、成都、杭州、武汉、西安等地举办了多场超500人的生鲜供应链产业高峰论坛及超100人的生鲜企业沙龙活动，与中国蔬菜配送协会、中国市场学会团餐专委会、全国农贸联、团餐谋、生鲜榜等国内权威行业协会及主流行业媒体强强联合，致力于为全国生鲜企业打造一个集生鲜企业管理咨询与培训、人才培养、品牌打造、资源整合、融资背书、行业探索的学习型平台。
          </p>
        </div>
        <p class="title2">专家咨询</p>
        <div class="person-list w">
          <div class="person" v-for="person in personList" :key="person.src">
            <img :src="person.src" :alt="person.post_content">
            <div class="person-name">
              {{ person.post_title }}
            </div>
            <div class="person-desc">
            {{ 
              person.post_content
             }}</div>
          </div>
        </div>
      </div>
      <div class="w-block">
        <p class="title2">赋能生鲜企业</p>
        <div class="icon-list w mb50">
          <div class="icon" v-for="(icon, index) in iconList" :key="icon.icon">
            <img :src="icon.icon" :alt="icon.txt1">
            <p class="txt1">{{ icon.txt1 }}</p>
            <p class="">{{ icon.txt2 }}</p>
          </div>
        </div>
      </div>
    </StudyCenterTabs>
  </div>
</template>
<script setup>
import icon1 from '../assets/epsf/icon-1.png'
import icon2 from '../assets/epsf/icon-2.png'
import icon3 from '../assets/epsf/icon-3.png'
import icon4 from '../assets/epsf/icon-4.png'
import icon5 from '../assets/epsf/icon-5.png'
import icon6 from '../assets/epsf/icon-6.png'
import icon7 from '../assets/epsf/icon-7.png'
import icon8 from '../assets/epsf/icon-8.png'
useHead({
  title: '蔬东坡-专家咨询',
  meta: [
    { name: 'description', content: '蔬东坡致力于打造一个专属于生鲜人的增值服务平台，洞悉行业未来、打造企业品牌、提供管理咨询与培训、促进资源整合。' },
    { name: 'keywords', content: '专家咨询，生鲜行业分析，蔬东坡' },
  ],
});
const iconList = [
  {
    icon: icon1,
    txt1: '创业项目',
    txt2: '运营扶持'
  },
  {
    icon: icon2,
    txt1: '领导力',
    txt2: '提升培训'
  },
  {
    icon: icon3,
    txt1: '作业流程',
    txt2: '高效规范'
  },
  {
    icon: icon4,
    txt1: '成本',
    txt2: '清晰可控'
  },
  {
    icon: icon5,
    txt1: '降低',
    txt2: '生产成本'
  },
  {
    icon: icon6,
    txt1: '提高',
    txt2: '企业赢利'
  },
  {
    icon: icon7,
    txt1: '提升人员',
    txt2: '素养与技能'
  },
  {
    icon: icon8,
    txt1: '增强',
    txt2: '企业竞争力'
  },
]
const personList = ref([])
const getPersonList = async () => {
  const res = await getStudyCenterMedia('专家咨询')
  if(res.status) {
    console.log('res', res)
    personList.value = res.data
  }
}
getPersonList()
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);

.epsf {
  color: #333333;
  .news_header {
    background: url(../assets/xueyuan/common_bg.png) no-repeat center center;
    background-size: 100% 100%;
    &__desc {
      font-size: 28px;
    }
  }
  .w {
    width: 850px;
    margin: 0 auto;
  }
  .g-block {
    background-color: #f9fbff;
    padding-top: 70px;
    overflow:hidden;
  }
  .w-block {
    background-color: #FFFFFF;
    padding-top: 50px;
    overflow:hidden;
  }
  .title2 {
    font-size: 24px;
    text-align: center;
    font-weight: 500;
  }
  .mb50 {
    margin-bottom: 50px;
  }
  .mb30 {
    margin-bottom: 30px;
  }
  .txt {
    color: #666666;
    line-height: 16px;
  }
  .person-list {
    margin: 50px auto 70px auto;
    display: flex;
    flex-wrap: wrap;
    gap: 30px 60px;
    .person {
      flex-basis: 120px;
      flex-shrink: 0;
      vertical-align: top;
      img {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 1px solid #2977FE;
      }
      .person-name {
        color: #2977FE;
        font-size: 16px;
        margin-top: 5px;
        text-align: center;
      }
      .person-desc {
        color: #999999;
        font-size: 12px;
        text-align: center;
      }
    }
  }
  .icon-list {
    margin-top: 50px;
    display: flex;
    flex-wrap: wrap;
    gap: 30px 40px;
    .icon {
      flex-basis: 70px;
      flex-shrink: 0;
      img {
        width: 70px;
        border-radius: 50%;
      }
      p {
        font-size: 14px;
        color: #666666;
        text-align: center;
      }
      .txt1 {
        margin-top: 7px;

      }
    }
  }
  .desc-box {
    display: flex;
    margin-top: 20px;
    margin-bottom: 70px;
    img {
      width: 140px;
      height: 140px;
    }
    .desc-txt {
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-left: 24px;
      text-indent: 2em;
    }
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
