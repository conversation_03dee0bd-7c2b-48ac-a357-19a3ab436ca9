<template>
  <div class="processing">
    <header class="gxst_header">
      <div class="gxst_header__box">
        <h1 class="gxst_header__title">社区团购系统</h1>
        <div class="gxst_header__detail">建立全流程、一体化的社区团购管理模式，助力企业快速抢占团购市场</div>
        <SubmitButton class="button">获取专属方案</SubmitButton>
      </div>
      <div class="gxst_header__headertab">
        <div class="tab_item">
          <div class="tab_item_left"><img src="../assets/groupbuy/b1.png" alt="" /></div>
          <div class="tab_item_right">
            <div class="tab_title">智慧化会员营销</div>
            <div class="tab_p">帮助平台拉新与留存</div>
            <div class="tab_p">玩转社交流量</div>
          </div>
        </div>
        <div class="tab_item">
          <div class="tab_item_left"><img src="../assets/groupbuy/b2.png" alt="" /></div>
          <div class="tab_item_right">
            <div class="tab_title">强互动直播卖货</div>
            <div class="tab_p">实现用户高效率转化</div>
            <div class="tab_p">刺激用户消费</div>
          </div>
        </div>
        <div class="tab_item">
          <div class="tab_item_left"><img src="../assets/groupbuy/b3.png" alt="" /></div>
          <div class="tab_item_right">
            <div class="tab_title">完善的团长体系</div>
            <div class="tab_p">与平台进行深度绑定</div>
            <div class="tab_p">互利共赢</div>
          </div>
        </div>
        <div class="tab_item">
          <div class="tab_item_left"><img src="../assets/groupbuy/b4.png" alt="" /></div>
          <div class="tab_item_right">
            <div class="tab_title">供应链管理</div>
            <div class="tab_p">打通供应链与销售</div>
            <div class="tab_p">稳定经营</div>
          </div>
        </div>
      </div>
    </header>
    <div class="processing-container">
      <div class="project">
        <h3 class="project_title">多样化核心功能 玩转社区团购</h3>
        <p class="project_titlep">助力社区团购平台销量倍增</p>
        <div class="project_center">
          <div class="project_item">
            <img class="item_img" src="../assets/groupbuy/d1.png" alt="分销裂变" />
            <div class="item_title">分销裂变</div>
            <div class="item_value">
              <p>推荐有奖</p>
              <p>佣金制度</p>
              <p>刺激裂变</p>
            </div>
          </div>
          <div class="project_item">
            <img class="item_img" src="../assets/groupbuy/d2.png" alt="拼团秒杀" />
            <div class="item_title">拼团秒杀</div>
            <div class="item_value">
              <p>吸引大量用户</p>
              <p>低成本获客</p>
            </div>
          </div>
          <div class="project_item">
            <img class="item_img" src="../assets/groupbuy/d3.png" alt="智能分拣" />
            <div class="item_title">智能分拣</div>
            <div class="item_value">
              <p>减少分拣时间</p>
              <p>降低人力成本</p>
            </div>
          </div>
          <div class="project_item">
            <img class="item_img" src="../assets/groupbuy/d4.png" alt="智能仓储" />
            <div class="item_title">智能仓储</div>
            <div class="item_value">
              <p>精细化库存管理</p>
              <p>自动报损报溢</p>
            </div>
          </div>
          <div class="project_item">
            <img class="item_img" src="../assets/groupbuy/d5.png" alt="数据分析难" />
            <div class="item_title">智能物流</div>
            <div class="item_value">
              <p>可视化排揎</p>
              <p>智能合理规划路线</p>
            </div>
          </div>
        </div>
      </div>
      <div class="g-block" id="huodong">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/groupbuy/ss1.png" alt="营销活动丰富 天天新玩法" />
              <span>营销活动丰富 天天新玩法</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div>
                  <p>优惠券</p>
                  <span>新会员注册即可获得优惠券，破冰购买下单更快</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <p>秒杀商品</p>
                  <span>商品倒计时抢购，提高用户活跃度和留存力</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <p>群接龙</p>
                  <span>阶梯拼团，邻居好友一起团，快速起量，打造人气营销。抓住更多流量，实现规模扩张</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/groupbuy/s1.png" alt="智能分拣称重" />
          </div>
        </div>
      </div>
      <div class="w-block" id="xiaochengxu">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/groupbuy/ss2.png" alt="团长运营小程序 快速成团" />
              <span>团长运营小程序 快速成团</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div>
                  <span>会员下单，团长收益立即可见，总部结算，自由提现</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>订单到货自动通知，一键取货快捷操作</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>团长销售排名随时掌握，经营数据清晰可见，随时掌握团长动态</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>团长分级，差异化佣金管理，多劳多得，互利双赢</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/groupbuy/s2.png" alt="农残检测设备" />
          </div>
        </div>
      </div>
      <div class="g-block" id="houtai">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/groupbuy/ss3.png" alt="商品运营后台 清晰有序" />
              <span>商品运营后台 清晰有序</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div>
                  <span>商品订单管理，内置云商品库，省去大量建档工作</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>自动进行订单汇总统计，极大减少人工成本</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>商品经营多维度展示，销量排名，毛利报表，损益分析等信息一目了然</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>后台多元化的数据报表分析，销量、销售额、毛利、团长实时生成，分析增长趋势</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/groupbuy/s3.png" alt="商品运营后台" />
          </div>
        </div>
      </div>
      <div class="w-block" id="gongyinglian">
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text">
            <div class="top">
              <img loading="lazy" src="../assets/groupbuy/ss4.png" alt="供应链系统管理 收益最大化" />
              <span>供应链系统管理 收益最大化</span>
            </div>
            <div class="content">
              <div class="text-box">
                <div>
                  <span>精准化采购管理，自动发布采购任务，减少人工计算</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>精细化库房管理，库区库位规划，一秒定位，减少找货时间</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>智能化的分拣管理，支持多种分拣模式，商品标签批量打印</span>
                </div>
              </div>
              <div class="text-box">
                <div>
                  <span>配送线路提前规划，根据团长批量发货，降低物流成本，提高企业效率</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="../assets/groupbuy/s4.png" alt="供应链系统管理" />
          </div>
        </div>
      </div>
      <div class="qsy_content">
        <div class="qsy_content__pain text-center">
          <h3 class="qsy_content__title">社区团购全业态场景</h3>
          <p class="qsy_content__titlep">覆盖全渠道经营场景，占据每个流量入口</p>
          <ul class="flex flex-row justify-between w">
            <li class="pain_item">
              <img class="inline-block pain_item__img" src="~/assets/groupbuy/e1.png" alt="社区团购 门店模式" />
              <h5 class="pain_item__title">社区团购 门店模式</h5>
              <p class="pain_item__desc">
                利用邻里关系和实体店优
                <br />
                势，结合线上线下突破区
                <br />
                域限制，扩大业务范围
              </p>
            </li>
            <li class="pain_item">
              <img
                class="inline-block pain_item__img"
                src="~/assets/groupbuy/e2.png"
                alt="社区团购 生活服务"
                width="132"
              />
              <h5 class="pain_item__title">社区团购 生活服务</h5>
              <p class="pain_item__desc">
                搭建满足社区居民用户生
                <br />
                活、家政、旅游等社区业
                <br />
                态全场景的电商平台
              </p>
            </li>
            <li class="pain_item">
              <img
                class="inline-block pain_item__img"
                src="~/assets/groupbuy/e3.png"
                alt="社区团购 一件代发"
                width="132"
              />
              <h5 class="pain_item__title">社区团购 一件代发</h5>
              <p class="pain_item__desc">
                满足产地+厂家+供应链，
                <br />
                直接发货到会员，构建轻
                <br />
                量化社区团购供应链体系
              </p>
            </li>
            <li class="pain_item">
              <img
                class="inline-block pain_item__img"
                src="~/assets/groupbuy/e4.png"
                alt="社区团购 前置仓"
                width="132"
              />
              <h5 class="pain_item__title">社区团购 前置仓</h5>
              <p class="pain_item__desc">
                采用线上社区团购平台+线下实体
                <br />
                门店自提的方式，加速传统商超商
                <br />
                业转型升级，实现一体化融合发展
              </p>
            </li>
          </ul>
        </div>
      </div>
      <div class="newMessage-container" v-if="newMessageList.length > 0">
        <div class="newMessage-title">蔬东坡最新资讯</div>
        <Message :info-list="newMessageList" :max-length="15" />
        <div class="indicator"></div>
        <a class="newMessage-more" href="https://www.sdongpo.com/xueyuan/" target="_blank">查看更多 ></a>
      </div>
    </div>
  </div>
</template>
<script setup>
import useInViewAnimate from "../hooks/inViewAnimate";
import useAnchor from "../hooks/useAnchor";
import Message from "./message.vue";

const vInViewAnimate = {
  mounted: useInViewAnimate,
}

useHead({
  title: "社区团购系统",
  meta: [
    {
      name: "description",
      content: "蔬东坡社区团购管理系统,建立全流程、一体化的社区团购管理模式，助力企业快速抢占团购市场",
    },
    {
      name: "keywords",
      content: "蔬东坡社区团购管理系统,建立全流程、一体化的社区团购管理模式，助力企业快速抢占团购市场",
    },
  ],
  script: [
    {
      src: "https://hm.baidu.com/hm.js?d48f6da230615bef4b3765442ede582d",
      bodyClose: false,
      async: true,
      defer: true,
    },
  ],
});
useAnchor();

const mergeArraysAlternating = (arr1, arr2) => {
  const newArr = [];
  const existingIds = new Set();

  let i = 0,
    j = 0;
  const len1 = arr1.length;
  const len2 = arr2.length;

  while (i < len1 || j < len2) {
    // 处理arr1
    if (i < len1) {
      const obj1 = arr1[i];
      if (!existingIds.has(obj1.id)) {
        newArr.push(obj1);
        existingIds.add(obj1.id);
        i++;
      } else {
        i++;
      }
    }

    // 处理arr2
    if (j < len2) {
      let added = false;
      while (j < len2 && !added) {
        const obj2 = arr2[j];
        if (!existingIds.has(obj2.id)) {
          newArr.push(obj2);
          existingIds.add(obj2.id);
          added = true;
        } else {
          j++;
        }
      }
    }
  }

  return newArr.slice(0, 10);
};
// 最新资讯
const newMessageList = ref([]);
const res1 = await getNewMessageList({
  order: "desc",
  per_page: 10,
  orderby: "date",
  search: ["社区团购"],
});
const res2 = await getNewMessageList({
  order: "desc",
  per_page: 10,
  orderby: "date",
  search: ["社区团购系统"],
});
newMessageList.value = mergeArraysAlternating(res1, res2);
</script>

<style lang="postcss" scoped>
.newMessage {
  &-title {
    font-size: 24px;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #333333;
    margin: 0 auto 40px;
    text-align: center;
  }

  &-container {
    width: 1200px;
    margin: 40px auto 80px;
    position: relative;
  }

  &-more {
    position: absolute;
    bottom: -30px;
    font-size: 16px;
    right: 0;
  }
}
.gxst_header {
  height: 620px;
  padding-top: 195px;
  background: url(../assets/groupbuy/bg.png) no-repeat top/cover;
  color: #fff;
  position: relative;
  &__headertab {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 1000px;
    display: flex;
    height: 100px;
    align-items: center;
    justify-content: space-between;
    .tab_item {
      background: linear-gradient(180deg, #ffffff 0%, #f1f9ff 100%);
      border-radius: 3px 3px 3px 3px;
      opacity: 1;
      width: 200px;
      height: 100px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .tab_item_left {
        width: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .tab_item_right {
        flex: 1;
        .tab_title {
          font-size: 14px;
          color: #333333;
        }
        .tab_p {
          font-size: 12px;
          color: #666666;
        }
      }
    }
  }

  &__box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-direction: column;
    height: 300px;
  }
  &__title {
    font-size: 56px;
    color: #ffffff;
  }
  &__detail {
    font-size: 32px;
  }

  &__desc {
    margin-top: 60px;
    font-weight: 400;
    font-size: 20px;
    font-weight: lighter;
  }

  &__img {
    margin-left: 70px;
    width: 420px;
    height: 320px;
  }
}

.processing {
  min-width: 1300px;

  .processing-banner {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #ffffff;
    background: url(../assets/processing/banner.png) no-repeat center center;
    height: 694px;
    background-size: cover;

    .title {
      margin-top: 200px;
      font-weight: 400;
      font-size: 56px;
    }

    .tips1 {
      margin-top: 53px;
      font-weight: 400;
      font-size: 32px;
    }

    .mb76 {
      margin-bottom: 76px;
    }
  }
  .processing-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 82px;
      font-weight: 500;
      font-size: 24px;
    }

    .underline {
      width: 142px;
      height: 3px;
      margin-top: 10px;
      background: #2977fe;
      border-radius: 5px;
    }

    .up-box {
      width: 100%;
      margin-top: 53px;
      margin-bottom: 90px;
      display: flex;
      align-items: center;
      justify-content: center;

      .up {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 220px;
        width: 220px;
        background: linear-gradient(180deg, #ffffff -32.39%, #f1f9ff 132.39%);
        box-shadow: 5px 5px 5px rgba(165, 181, 214, 0.2);
        border-radius: 5px;
        margin-right: 30px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;

        span {
          margin-top: 8px;
        }

        .percent {
          margin-top: 12px;
          font-weight: 500;
          font-size: 24px;
          line-height: 24px;
        }

        img {
          width: 68px;
          height: 92px;
          flex-shrink: 0;
        }
      }
    }

    .desc-box {
      display: flex;
      justify-content: center;
      margin-top: 70px;
      margin-bottom: 70px;

      .desc-text {
        color: #333333;
        width: 420px;
        margin-right: 30px;
        .top {
          display: flex;
          align-items: center;
          margin-bottom: 30px;

          span {
            font-weight: 500;
            font-size: 24px;
            line-height: 24px;
          }

          img {
            width: 34px;
            height: 34px;
          }
        }
        .content {
          font-size: 16px;
          font-weight: 400;

          .text-box {
            display: flex;
            margin-bottom: 30px;

            span {
              display: block;
            }
            .img {
              margin-top: 5px;
              margin-right: 5px;
              flex-shrink: 0;
              img {
                width: 15px;
                height: 15px;
              }
            }
          }

          .text-box:last-of-type {
            margin-bottom: 50px;
          }
        }
      }

      .desc-img {
        padding-left: 200px;
        display: flex;
        align-items: center;
        background: url(~/assets/groupbuy/bbg.png) no-repeat;
        background-size: contain;
        img {
          width: 400px;
          object-fit: cover;
        }
      }
    }
    .img-list-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 70px;
      padding-bottom: 150px;
      color: #333333;
      .img-list-title {
        text-align: center;
        margin-bottom: 10px;
        font-weight: 500;
        font-size: 24px;
      }
      .img-list-desc {
        text-align: center;
        color: #666666;
        font-size: 16px;
        font-weight: 400;
      }
      .img-list {
        margin-top: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        div {
          margin-right: 40px;
          width: 250px;
          position: relative;
          img {
            width: 250px;
            height: 300px;
          }
          p {
            position: absolute;
            color: #ffffff;
            width: 250px;
            bottom: 30px;
            left: 0;
            text-align: center;
          }
        }
      }
    }
  }

  .w-block {
    width: 100%;
    background: #ffffff;
  }

  .g-block {
    width: 100%;
    background: #f9fbff;
  }
  .advantage-bg {
    background-size: cover;
    background: url(../assets/processing/advantage-bg.png) no-repeat center center;
    .advantage-title {
      color: #333333;
      font-size: 24px;
      font-weight: 500;
      text-align: center;
    }
  }

  .button {
    width: 200px;
    font-size: 20px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-weight: 400;
    padding: 0;
  }
  .advantage-box {
    width: 100%;
    margin-top: 80px;
    margin-bottom: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    .advantage {
      color: #333333;
      font-size: 20px;
      font-weight: 400px;
      width: 230px;
      height: 250px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      margin-right: 30px;
      padding-top: 90px;
      .title2 {
        color: #333333;
        font-size: 20px;
        font-weight: 500;
      }
      p {
        margin-bottom: 30px;
      }
      .tips2 {
        width: 180px;
        text-align: center;
        font-size: 14px;
      }
      img {
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        width: 181px;
        height: 99px;
      }
    }
  }
  .tips3 {
    color: #666666;
    font-size: 24px;
    font-weight: 400;
    text-align: center;
  }
  .sidebar {
    &__title {
      color: #333333;
      font-size: 40px;
      padding-top: 86px;
      padding-bottom: 42px;
    }
  }
  .pb100 {
    padding-bottom: 100px;
  }

  @keyframes fly-up {
    0% {
      transform: translateY(100px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .fly-up {
    animation-name: fly-up;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  @keyframes scale-large {
    0% {
      transform: scale(0.2);
      opacity: 0;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .scale-large {
    animation-name: scale-large;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .fade-in {
    animation-name: fade-in;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  .animate-element {
    opacity: 0;
  }
}
.project {
  width: 100%;
  height: 685px;

  .project_title {
    text-align: center;
    width: 100%;
    font-size: 24px;
    padding: 120px 0 0 0;
  }
  .project_titlep {
    text-align: center;
    width: 100%;
    font-size: 20px;
    padding: 10px 0 50px 0;
    color: #666666;
  }

  .project_center {
    width: 1200px;
    display: flex;
    margin: 50px auto 0 auto;
    justify-content: space-between;

    .project_item {
      width: 200px;
      height: 250px;
      position: relative;
      background: url("../assets/jcjg/Rectangle69.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      flex-direction: column;

      /* justify-content: space-evenly; */
      .item_img {
        height: 110px;
        width: 110px;
        position: absolute;
        top: -50px;
      }

      .item_title {
        font-size: 20px;
        color: #333333;
        margin-top: 85px;
        margin-bottom: 11px;
      }

      .item_value {
        width: 278px;
        height: 106px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 35px;

        p {
          text-align: center;
        }
      }
    }
  }
}
.qsy_content {
  margin-top: 50px;
  width: 100%;
  background: url(../assets/groupbuy/bge.png) no-repeat center/cover;
  &__title {
    margin-bottom: 10px;
    color: #333;
    font: 24px PingFang SC;
  }
  &__titlep {
    margin-bottom: 94px;
    color: #666;
    font: 18px PingFang SC;
  }
  &__pain {
    margin-bottom: 100px;
    .pain_item {
      position: relative;
      width: 250px;
      padding: 96px 0 22px;
      background: #fff;
      box-shadow: 2px 4px 4px rgba(59, 105, 220, 0.05);
      border-radius: 5px;
      &__img {
        position: absolute;
        top: -50px;
        left: 0;
        right: 0;
        margin: auto;
        width: 130px;
      }
      &__title {
        margin-bottom: 15px;
        color: #333;
        font: 20px PingFang SC;
      }
      &__desc {
        color: #333;
        font-size: 14px;
        line-height: 216.5%;
      }
    }
  }
  &__process {
    background-color: #f5f8ff;
    .process_illustration {
      width: 1034px;
      margin: 0 auto;
      padding: 100px 0;
    }
  }
  &__main {
    .ds_content__title {
      display: inline-block;
      color: #333;
      font-size: 24px;
      &::after {
        content: "";
        display: block;
        width: 330px;
        height: 6px;
        margin: 10px 0 30px;
        background: url(~/assets/qsy/title_icon_right.png) no-repeat center/cover;
      }
    }
    .item_desc {
      position: relative;
      margin: 0 0 30px 20px;
      color: #333;
      font-size: 16px;
      &::before {
        content: "";
        position: absolute;
        top: 4px;
        left: -22px;
        display: inline-block;
        width: 15px;
        height: 15px;
        background: url(~/assets/qsy/item_icon.png) no-repeat center/cover;
      }
    }
    .w {
      width: 1020px;
    }
    .sys_item {
      padding: 70px 0 76px;
      &:nth-child(2n) {
        background: #f9fbff;
      }
      &:nth-child(2n + 1) {
        .sys_item_detail {
          transform: translateX(100px);
        }
      }
      &:nth-child(2n) .ds_content__title::after {
        background-image: url(~/assets/qsy/title_icon_left.png);
      }
      &:nth-child(1) {
        .ds_content__title::after {
          transform: translateX(-82px);
        }
        img {
          width: 560px;
        }
      }
      &:nth-child(2) {
        img {
          width: 497px;
        }
      }
      &:nth-child(3) {
        .ds_content__title::after {
          transform: translateX(-26px);
        }
        img {
          width: 462px;
        }
      }
      &:nth-child(4) {
        img {
          width: 527px;
        }
      }
      &:nth-child(5) {
        .ds_content__title::after {
          transform: translateX(-42px);
        }
        img {
          width: 544px;
        }
      }
    }
  }
}
</style>
