<template>
  <div class="modal__mask" v-if="show">
    <div @click.stop class="modal__main">
      <div class="modal__main__img">
        <img class="modal__main__img__bg" :src="data.conver_image" :alt="data.post_title"/>
        <img class="modal__main__img__close" src="../../../assets/common/circle_close.png" @click="setShow(false)"/>
      </div>
      <div class="modal__main__content">
        <div class="modal__main__content__title">{{data.post_title}}</div>
        <p class="modal__main__content__introduce">
          <span class="modal__main__content__introduce--label">企业介绍：</span>
          <span class="modal__main__content__introduce--value">{{data.introduce}}</span>
        </p>
        <p class="modal__main__content__introduce">
          <span class="modal__main__content__introduce--label">合作价值：</span>{{data.cooperation_value}}
        </p>
        <p class="modal__main__content__line"></p>
        <p class="modal__main__content__tags" v-if="data.business_type.length">
          <span class="modal__main__content__tags__item sdp-tag" v-for="(item, index) in data.business_type" :key="index">{{item}}</span>
        </p>
      </div>
    </div>
  </div>
</template>
<script setup>

const data = ref({});
const show = ref(false);

const setShow = (isShow, info) => {
  show.value = isShow;
  if (isShow) {
    data.value = info;
  }
}

defineExpose({
  setShow,
});

</script>
<style lang="scss" scoped>
.modal__mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 214748369999949999;
}
.modal__main {
  width: 600px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2147483649999;
  font-size: 12px;
  background: #FFFFFF;
  box-shadow: 0px 4px 40px 0px rgba(0,0,0,0.35);
  border-radius: 8px 8px 8px 8px;
  border: 2px solid #FFFFFF;
  overflow-y: auto;
  &__img {
    position: relative;
    width: 100%;
    height: 240px;
    &__bg {
      width: 100%;
      height: 100%;
    }
    &__close {
      position: absolute;
      top: 20px;
      right: 20px;
      height: 40px;
      width: 40px;
      cursor: pointer;
    }
  }
  &__content {
    padding: 0 24px 14px;
    &__title {
      position: relative;
      height: 34px;
      margin-top: 24px;
      padding-left: 8px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 24px;
      color: #000000;
      line-height: 34px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      &::before {
        content: '';
        position: absolute;
        top: 5px;
        left: 0;
        width: 2px;
        height: 24px;
        background: #2977FE;
      }
    }
    &__introduce {
      margin-top: 10px;
      color: #666;
      font-size: 12px;
      &--label {
        color: #2977fe;
      }
    }
    &__line {
      height: 1px;
      margin: 24px 0;
      background: #E6E6E6;
    }
    &__tags {
      display: flex;
      flex-wrap: wrap;
      &__item {
        margin-right: 8px;
        margin-bottom: 10px;
        padding: 2px 10px;
        font-size: 12px;
      }
    }
  }
}
</style>