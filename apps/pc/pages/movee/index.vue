<template>
  <div>
    <div class="movee_header text-center">
      <h1 class="movee_header__title">30万+生鲜企业的共同选择</h1>
      <!-- <SubmitButton class="small-btn">获取专属方案</SubmitButton> -->
    </div>
    <div class="movee_content">
      <div class="w movee_content__filter">
        <div class="movee_content__category">
          <p class="movee_content__category--title inline-block">业务：</p>
          <ul class="inline-flex items-center">
            <li
                v-for="(category, index) in categoryList"
                :key="index"
                class="movee_content__category--item"
                :class="{ 'movee_content__category--item--active': currentCategory === category }"
                @click="handleChangeCategory(category)"
            >
              {{category}}
            </li>
          </ul>
        </div>
        <div class="movee_content__category movee_content__area">
          <p class="movee_content__category--title inline-block">地区：</p>
          <ul class="inline-flex items-center">
            <li
                v-for="(area, index) in areaList"
                :key="index"
                class="movee_content__category--item"
                :class="{ 'movee_content__category--item--active': currentArea === area }"
                @click="handleChangeArea(area)"
            >
              {{area}}
            </li>
          </ul>
        </div>
      </div>
      <div class="movee_content__main">
        <ul class="w flex flex-wrap">
          <li
            v-for="(data) in dataListFilter"
            :key="data.ID"
            class="movee_content__main--article">
            <a target="_blank" :href="data.redirect_url">
              <img class="article-img" :src="data.conver_image" :alt="data.post_title">
              <div class="article-detail">
                <h4 class="article-detail__title">{{data.post_title}}</h4>
                <p class="article-detail__introduce">
                  <span class="article-detail__introduce--label">企业介绍：</span>
                  <span class="article-detail__introduce--value">{{data.introduce}}</span>
                </p>
                <p class="article-detail__line"></p>
                <p @click.stop class="article-detail__tags">
                  <template v-if="data.business_type.length">
                    <span class="article-detail__tags__item sdp-tag" v-for="(item, index) in data.business_type" :key="index">{{item}}</span>
                    <span class="article-detail__tags__more"></span>
                  </template>
                  <span class="article-detail__tags__text sdp-tag" @click="handleLookMore(data)">更多 →</span>
                </p>
              </div>
            </a>
          </li>
        </ul>
      </div>
    </div>
    <introduce-detail ref="introDetailRef"/>
  </div>
</template>

<script setup>
import IntroduceDetail from "./components/introduce-detail.vue";

useHead({
  title: '蔬东坡成功案例,蔬东坡大客户,生鲜蔬菜配送平台,社区团购平台案例分享_蔬东坡',
  meta: [
    {
      name: 'description',
      content:
        '蔬东坡成功案例展示最新的生鲜配送app案例,了解最新加盟到蔬东坡的生鲜配送平台与蔬菜配送平台,借助蔬东坡软件系统后业绩翻几番,让生鲜企业做大做强。',
    },
    { name: 'keywords', content: '生鲜配送平台,蔬菜配送平台,社区团购平台,生鲜软件' },
  ],
  script: [
    {
      src:'https://hm.baidu.com/hm.js?37f9e8255a5fe1f567b5720f6cba2d2c',
      bodyClose: false,
      async: true,
      defer: true,
    }
  ]
})

let currentCategory = ref('全部')
let currentArea = ref('全部')
let categoryList = reactive([])
let dataList = reactive([])
let areaList = reactive([])
let dataListFilter = reactive([])
let introDetailRef = ref(null);

const getData = async () => {
  const res = await getStudyCenterPages('客户案例')
  if (res.status === 1) {
    let categorySet = new Set()
    let areaSet = new Set()
    dataList = (res.data.list || []).map(item => {
      if (!item.business_type) item.business_type = []
      if (item.business_type.length) categorySet.add(...item.business_type)
      if (item.zone) areaSet.add(item.zone)
      return item
    })
    // 组装分类数据
    categoryList.push(
      '全部', ...categorySet
    )
    // 组装地区数据
    areaList.push(
      '全部', ...areaSet
    )
    dataListFilter = dataList
  }
}

getData()

const handleChangeCategory = (category) => {
  currentCategory.value = category
  // 本地筛选
  dataListFilter = dataList.filter(item => (category === '全部' || item.business_type.includes(category)) && (currentArea.value === '全部' || item.zone === currentArea.value))
}
const handleChangeArea = (area) => {
  currentArea.value = area
  // 本地筛选
  dataListFilter = dataList.filter(item => (currentCategory.value === '全部' || item.business_type.includes(currentCategory.value)) && (area === '全部' || item.zone === area))
}
const handleToArticleDetail = (url) => {
  if (url) location.href = url
}
const handleLookMore = (data) => {
  introDetailRef.value.setShow(true, data)
}
</script>

<style scoped lang="postcss">
.w {
  width: 1000px;
}
.movee_header {
  height: 400px;
  background: url('~/assets/movee/movee_header_bg.png') bottom center/cover no-repeat;
  &__title {
    margin-bottom: 70px;
    padding-top: 200px;
    color: #fff;
    font: 500 56px PingFang SC-Semibold, PingFang SC;
    letter-spacing: 4px;
  }
}
.movee_content {
  padding: 70px 0 20px;
  color: #333;
  background-color: #f9fbff;

  &__filter {
    padding: 16px 0 16px 40px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.05);
    border-radius: 10px 10px 10px 10px;
  }

  &__category{
    &--title {
      font: 24px PingFang SC-Medium, PingFang SC;
      vertical-align: -3px;
    }
    &--item {
      padding: 2px 10px;
      margin-left: 20px;
      font-size: 20px;
      cursor: pointer;
      &--active {
        color: white;
        background: #2977FE;
        border-radius: 5px 5px 5px 5px;
      }
    }
  }
  &__area {
    margin-top: 12px;
  }
  &__main {
    margin-top: 50px;
    &--article {
      position: relative;
      width: 300px;
      /* height: 525px; */
      margin: 0 30px 25px 0;
      background-color: #fff;
      box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
      border-radius: 5px;
      cursor: pointer;
    }
    .article-img {
      width: 300px;
      height: 150px;
    }
    .article-detail {
      padding: 24px 20px 20px;
      &__title {
        color: #333;
        font-size: 16px;
      }
      &__introduce {
        height: 54px;
        margin-top: 14px;
        color: #666;
        font-size: 13px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical; /* 或者 -webkit-box-direction: normal; */
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5; /* 根据实际行高调整 */
        &--label {
          color: #2977fe;
        }
      }
      &__line {
        height: 0.5px;
        margin: 12px 0;
        background: #E6E6E6;
      }
      &__tags {
        position: relative;
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        height: 22px;
        overflow: hidden;
        &__item {
          flex-shrink: 0;
          margin-right: 4px;
          line-height: 22px;
          padding: 2px 10px;
          font-size: 12px;
        }
        &__more {
          position: absolute;
          right: 64px;
          bottom: 0;
          width: 104px;
          height: 22px;
          text-align: right;
          background: linear-gradient( 90deg, rgba(255,255,255,0) 0%, #FFFFFF 85%);
        }
        &__text {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 64px;
          height: 22px;
          line-height: 22px;
          text-align: right;
          font-size: 12px;
          font-weight: 500;
          background: white;
        }
      }
    }
  }
}
</style>