<template>
  <div>
    <header class="service_header contact_header">
      <h1 class="contact_header__title text-center">以客户价值为核心的全生命周期服务</h1>
    </header>
    <div class="service_content text-center">
      <div class="service_content__flow">
        <h3 class="service_content__title">全流程实施服务</h3>
        <ul class="w flex flex-wrap justify-between">
          <li class="flow-item">
            <img class="flow-item__img" src="~/assets/movee/flow_icon1.png" alt="项目启动" />
            <h4 class="flow-item__title">项目启动</h4>
            <p class="flow-item__desc">组建项目小组<br>编制实施计划<br>专项启动会议</p>
          </li>
          <li class="flow-item">
            <img class="flow-item__img" src="~/assets/movee/flow_icon2.png" alt="需求调研" />
            <h4 class="flow-item__title">需求调研</h4>
            <p class="flow-item__desc">业务现状调研<br>业务需求确认<br>系统需求分析</p>
          </li>
          <li class="flow-item">
            <img class="flow-item__img" src="~/assets/movee/flow_icon3.png" alt="蓝图设计" />
            <h4 class="flow-item__title">蓝图设计</h4>
            <p class="flow-item__desc">整体方案设计<br>核心流程分析<br>业务蓝图确认</p>
          </li>
          <li class="flow-item">
            <img class="flow-item__img" src="~/assets/movee/flow_icon4.png" alt="系统实现与测试" />
            <h4 class="flow-item__title">系统实现与测试</h4>
            <p class="flow-item__desc">流程设置与角色权限<br>系统测试计划<br>系统测试培训<br>系统操作手册</p>
          </li>
          <li class="flow-item">
            <img class="flow-item__img" src="~/assets/movee/flow_icon5.png" alt="上线培训" />
            <h4 class="flow-item__title">上线培训</h4>
            <p class="flow-item__desc">培训计划<br>各部门培训与管理员培训<br>系统上线确认</p>
          </li>
          <li class="flow-item">
            <img class="flow-item__img" src="~/assets/movee/flow_icon6.png" alt="验收交付" />
            <h4 class="flow-item__title">验收交付</h4>
            <p class="flow-item__desc">编写总结报告<br>召开总结大会<br>项目上线交付验收</p>
          </li>
        </ul>
      </div>
      <div class="service_content__circle">
        <h3 class="service_content__title">全周期成功服务</h3>
        <ul class="w flex justify-between items-center">
          <li class="circle-item">
            <h4 class="circle-item__title">生存期 破局新生</h4>
            <p class="circle-item__desc flex flex-col	justify-around">现场实施培训<br>线上直播培训<br>远程连线指导<br>系统操作手册<br>专属服务顾问</p>
          </li>
          <li>
            <img class="circle-item__img" src="~/assets/movee/arrow-down-full.png" alt="arrow-down-full.png">
          </li>
          <li class="circle-item">
            <h4 class="circle-item__title">发展期 能力建设</h4>
            <p class="circle-item__desc flex flex-col	justify-around">定期现场回访<br>最佳实践应用<br>客户案例分享<br>管理员认证培训<br>行业沙龙邀约<br>系统使用报告<br>版本迭代推送</p>
          </li>
          <li>
            <img class="circle-item__img" src="~/assets/movee/arrow-down-full.png" alt="arrow-down-full.png">
          </li>
          <li class="circle-item">
            <h4 class="circle-item__title">平台期 陪伴成功</h4>
            <p class="circle-item__desc flex flex-col	justify-around">企业运营诊断<br>落地实战培训<br>企业管理咨询<br>行业发展峰会<br>企业管理研修班<br>年度奖项评选</p>
          </li>
        </ul>
      </div>
      <div class="service_content__standard">
        <h3 class="service_content__title">SLA服务标准</h3>
        <img class="service_content__standard--img" src="~/assets/movee/service_standard_illu.jpg" alt="SLA服务标准">
      </div>
    </div>
  </div>
</template>

<script setup>
useHead({
  title: '蔬东坡客户成功体系,上门服务,实时联系蔬东坡客服,客服联系方式-蔬东坡',
  meta: [
    {
      name: 'description',
      content:
        '蔬东坡客户成功体系,7×24小在线客服联系方式,蔬东坡售后服务在线联系,5秒响应10分钟内解决问题',
    },
    { name: 'keywords', content: '蔬东坡电话,蔬东坡客服,蔬东坡联系方式,蔬东坡售后' },
  ],
  script: [
    {
      src:'https://hm.baidu.com/hm.js?37f9e8255a5fe1f567b5720f6cba2d2c',
      bodyClose: false,
      async: true,
      defer: true,
    }
  ]
})
</script>

<style scoped lang="postcss">
@import url(~/assets/style/contact.css);

.w {
  width: 940px;
  margin: 0 auto;
}
.service_content {
  padding-top: 70px;
  &__title {
    color: #333;
    font: 500 24px PingFang SC-Medium, PingFang SC;
  }
  &__flow {
    padding-bottom: 102px;
    .flow-item {
      position: relative;
      width: 250px;
      height: 270px;
      margin-top: 140px;
      color: #333;
      background-color: #F9FBFF;
      box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
      border-radius: 5px;
      &__title {
        padding: 94px 0 15px;
        font: 500 20px PingFang SC-Medium, PingFang SC;
      }
      &__desc {
        font-size: 14px;
        line-height: 30px;
      }
      &__img {
        position: absolute;
        top: -80px;
        left: 0;
        right: 0;
        margin: auto;
        width: 150px;
      }
    }
  }
  &__circle {
    padding: 70px 0 80px;
    background-color: #F5F8FF;
    .w {
      margin-top: 50px;
    }
    .circle-item {
      width: 250px;
      height: 330px;
      background-color: #fff;
      border-radius: 10px;
      &__title {
        height: 59px;
        color: #fff;
        font-size: 20px;
        line-height: 59px;
        background-color: #2977FE;
        border-radius: 10px 10px 0 0
      }
      &__desc {
        height: 270px;
        color: #333;
        font-size: 16px;
        line-height: 35px;
      }
      &__img {
        width: 60px;
      }
    }
  }
  &__standard {
    padding: 70px 0 125px;
    &--img {
      margin: 70px auto 0;
      width: 1001px;
    }
  }
}
</style>