<template>
    <div class="group-up-sdpspecial">
    <solution-header title="蔬东坡社区团购解决方案" class="">
      建立全流程、一体化的社区团购管理模式
      <br />
       助力企业快速抢占团购市场
      <template #img>
        <div class="right_location"></div>
      </template>
    </solution-header>    
    </div>
    <div class="case1">
      <el-row class="top-container" justify="center">
        <el-col :span="6" class="top-main"> 蔬东坡社区团购解决方案</el-col>
      </el-row>
       <ul class="flex flex-row justify-between w center-container" style="gap: 20px">
          <li class="pain_item">
            <img
              class="inline-block pain_item__img"
              src="~/assets/groupbuy_sdpspecial/case1_icon1.png"
              alt="分销裂变"
              width="132"
            />
            <h5 class="pain_item__title">分销裂变</h5>
            <p class="pain_item__desc">
              推荐有奖
              <br />
              佣金制度
              <br />
              刺激裂变
            </p>
            <div class="pain_item__bottom"></div>
          </li>
          <li class="pain_item">
            <img
              class="inline-block pain_item__img"
              src="~/assets/groupbuy_sdpspecial/case1_icon2.png"
              alt="群接龙"
              width="132"
            />
            <h5 class="pain_item__title">群接龙</h5>
            <p class="pain_item__desc">
              吸引大量用户
              <br />
              低成本获客
            </p>
            <div class="pain_item__bottom"></div>
          </li>
          <li class="pain_item">
            <img
              class="inline-block pain_item__img"
              src="~/assets/groupbuy_sdpspecial/case1_icon3.png"
              alt="智能分拣"
              width="132"
            />
            <h5 class="pain_item__title">智能分拣</h5>
            <p class="pain_item__desc">
              减少分拣时间
              <br />
              降低人力成本
            </p>
            <div class="pain_item__bottom"></div>
          </li>
          <li class="pain_item">
            <img class="inline-block pain_item__img" src="~/assets/groupbuy_sdpspecial/case1_icon4.png" alt="数据分析难" width="132" />
            <h5 class="pain_item__title">智能仓储</h5>
            <p class="pain_item__desc">
              精细化库存管理
              <br />
              自动报损报溢
            </p>
            <div class="pain_item__bottom"></div>
          </li>
          <li class="pain_item">
            <img class="inline-block pain_item__img" src="~/assets/groupbuy_sdpspecial/case1_icon5.png" alt="数据分析难" width="132" />
            <h5 class="pain_item__title">智能物流</h5>
            <p class="pain_item__desc">
              可视化排线
              <br />
              智能合理规划路线
            </p>
            <div class="pain_item__bottom"></div>
          </li>
        </ul>
        
      <el-row justify="center">
        <el-col :span="4" class="custom-btn-outer"> <SubmitButton class="custom-btn"></SubmitButton></el-col>
      </el-row>
    </div>
    <div class="case2">
      <el-row class="top-container" justify="center">
        <el-col :span="6" class="top-main"> 蔬东坡社区团购解决方案全流程</el-col>
      </el-row>
      <div class="center-container">
        <img src="~/assets/groupbuy_sdpspecial/case2_icon1.png" alt="蔬东坡社区团购解决方案全流程">
        <div class="dot1">平台</div>
        <div class="dot2">社区居民</div>
        <div class="dot3">团长自提点</div>
      </div>
      <el-row justify="center">
        <el-col :span="4" class="custom-btn-outer"> <SubmitButton class="custom-btn"></SubmitButton></el-col>
      </el-row>
    </div>     
    <div class="case3">
      <el-row class="top-container" justify="center">
        <el-col :span="6" class="top-main">多场景，全覆盖</el-col>
      </el-row>
      <div class="center-container">
        <div class="box1">
           <div class="left">
            <div class="left-title">
              <img src="~/assets/groupbuy_sdpspecial/case3_icon1.png" alt="">
              <div class="ft1">应用场景一：</div>
              <div class="ft2">团长自提</div>
            </div>
            <div class="left-desc">会员在平台快捷下单，平台发货到团长自提点，会员线下自提完成购物。覆盖3种角色，组成1个闭环</div>
           </div>
           <div class="right">
              <img src="~/assets/groupbuy_sdpspecial/case3_img1.png" alt="">
           </div>
        </div>
        <div class="box2">
           <div class="left">
              <img src="~/assets/groupbuy_sdpspecial/case3_img2.png" alt="">
           </div>
           <div class="right">
            <div class="right-title">
              <img src="~/assets/groupbuy_sdpspecial/case3_icon2.png" alt="">
              <div class="ft1">应用场景二：</div>
              <div class="ft2">本地生活服务</div>
            </div>
            <div class="right-desc">第三方商家入驻平台，会员在平台下单购买商家服务，会员线下去商家完成核销和消费</div>
           </div>
        </div>
        <div class="box3">
           <div class="left">
            <div class="left-title">
              <img src="~/assets/groupbuy_sdpspecial/case3_icon3.png" alt="">
              <div class="ft1">应用场景三：</div>
              <div class="ft2">物流直发</div>
            </div>
            <div class="left-desc">会员在平台下单购买商品，商家直接快递发货送到会员手中，省去仓库管理费用</div>
           </div>
           <div class="right">
              <img src="~/assets/groupbuy_sdpspecial/case3_img3.png" alt="">
           </div>
        </div>
      </div>
      <el-row justify="center">
        <el-col :span="4" class="custom-btn-outer"> <SubmitButton class="custom-btn"></SubmitButton></el-col>
      </el-row>
    </div>
    <div class="case4">
      <el-row class="top-container" justify="center">
        <el-col :span="6" class="top-main">您身边的朋友都在用</el-col>
      </el-row>
      <img src="~/assets/groupbuy_sdpspecial/case4_img1.png" alt="蔬东坡社区团购解决方案">
      <div class="middle-header">大庆九佰街</div>
      <p>成立于2019年，专注于社区团购业务，开设了26家直营门店，仓库面积超万平，自有配送车50多辆，所有品牌商品全部选择知名品牌合，2024年营收达4亿。</p>
      <div class="outer">
        <div class="show-case">
          <div class="main">
            <div class="title">26 <span class="ft3">家</span></div>
            <div class="desc">直营门店</div>
          </div>
          <div class="divider"></div>
          <div class="main">
            <div class="title">50+</div>
            <div class="desc">配送车辆</div>
          </div>
          <div class="divider"></div>
          <div class="main">
            <div class="title">4 <span class="ft3">亿</span></div>
            <div class="desc">年营收</div>
          </div>
        </div>
      </div> 
      <el-row justify="center">
        <el-col :span="4" class="custom-btn-outer"> <SubmitButton class="custom-btn"></SubmitButton></el-col>
      </el-row> 
    </div>
 </template>
<script setup lang='ts'>
             
</script>
<style scoped  lang="scss">
.ft1 {
  font-size: 20px;
  color: #333333;
  font-weight: 600;
}
.ft2 {
  font-size: 24px;
  color: #FF8501;
  font-weight: bold;
}
.custom-btn-outer {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.custom-btn {
  margin: 36px auto 20px auto;
  background-color: #FF8501;
  color: #FFFFFF;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
}
.group-up-sdpspecial {
  ::v-deep .gxst_header__text {
    margin-top: 20px;
  }
  ::v-deep .gxst_header {
    background: url(~/assets/groupbuy_sdpspecial/header_illustration.png) no-repeat top / cover !important;
  }
  ::v-deep .gxst_header__title {
    color: #333333;
  }
  ::v-deep .gxst_header__desc {
    color: #999999;
    margin-top: 30px;
    font-weight: 500;
  }
  ::v-deep .gxst_header .small-btn{
    background-color: #FF8501;
    color: #FFFFFF;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    margin-top: 38px;
  }
  .right_location {
    width: 530px;
  }

}
.case1 {
  padding: 60px 20px 20px 20px;
  background-color: #F6F7FA;
  .top-container {
    .top-main {
      text-align: center;
      color: #333333;
      font-size: 30px;
      font-weight: 600;
    }
  }
  .center-container {
    padding: 20px 0;
    margin-top: 40px;
    .pain_item {
      text-align: center;
      background-color: #FFFFFF;
      position: relative;
      width: 250px;
      padding: 96px 0 22px;
      //background: linear-gradient(313.8deg, #eef5ff 3.23%, #f9fafd 98.67%);
      //box-shadow: 2px 4px 4px rgba(59, 105, 220, 0.05);
      //border-radius: 5px;
      &__img {
        position: absolute;
        top: -30px;
        left: 0;
        right: 0;
        margin: auto;
        width: 180px;
      }
      &__title {
        margin-bottom: 15px;
        color: #FF8501;
        font: 20px HarmonyOS_Sans_SC_Bold;
        font-weight: 700;
      }
      &__desc {
        color: #333;
        font-size: 14px;
        line-height: 216.5%;
        font-weight: 500;
      }
      &__bottom {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 8px;
        background-color: #FF8501;
        //border-radius: 0 0 5px 5px;
      }
  }
  }

}
.case2 {
  padding: 60px 20px 30px 20px;
  .top-container {
    .top-main {
      text-align: center;
      color: #333333;
      font-size: 30px;
      font-weight: 600;
    }
  }
  .center-container {
    margin-top: 30px;
    padding-bottom: 60px;
    position: relative;
    img {
      width: 900px;
      margin: 0 auto;
    }
    .dot1, .dot2, .dot3 {
      position: absolute;
      color: #FF8501;
      font-size: 28px;
      font-weight: 600;
    }
    .dot1 {
      top: 248px;
      left: 900px;
    }
    .dot2 {
      bottom: 10px;
      left: 590px;
    }
    .dot3 {
      bottom: 10px;
      right: 565px;
    }
  }
}
.case3 {
  padding: 60px 20px 30px 20px;
  background-color: #F6F7FA;
  .top-container {
    .top-main {
      text-align: center;
      color: #333333;
      font-size: 30px;
      font-weight: 600;
    }
  }
  .center-container {
    padding-top: 150px;
    .box1 {
      margin: 10px auto;
      position: relative;
      width: 1000px;
      height: 300px;
      background: #FFFFFF;
      padding: 50px 50px 60px 40px;
      .left {
        width: 35%;
        .left-title {
          display: flex;
          align-items: center;
          img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
          }
        }
        .left-desc {
          padding-top: 30px;
          padding-left: 40px;
        }
      }
      .right {
        img {
          width: 600px;
          position: absolute;
          right: 0;
          top: -130px;
        }
      }
    }
    .box2 {
      margin: 140px auto 10px auto;
      position: relative;
      width: 1000px;
      height: 300px;
      background: #FFFFFF;
      padding: 50px 50px 60px 40px;
      display: flex;
      justify-content: end;
      .right {
        width: 35%;
        .right-title {
          display: flex;
          align-items: center;
          img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
          }
        }
        .right-desc {
          padding-top: 30px;
          padding-left: 40px;
        }
      }
      .left {
        img {
          width: 600px;
          position: absolute;
          left: 0;
          top: -130px;
        }
      }
    }
    .box3 {
      margin: 140px auto 10px auto;
      position: relative;
      width: 1000px;
      height: 300px;
      background: #FFFFFF;
      padding: 50px 50px 60px 40px;
      .left {
        width: 35%;
        .left-title {
          display: flex;
          align-items: center;
          img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
          }
        }
        .left-desc {
          padding-top: 30px;
          padding-left: 40px;
        }
      }
      .right {
        img {
          width: 600px;
          position: absolute;
          right: 0;
          top: -130px;
        }
      }
    }
  }
}
.case4 {
  padding: 60px 20px 30px 20px;
  .top-container {
    .top-main {
      text-align: center;
      color: #333333;
      font-size: 30px;
      font-weight: 600;
    }
  }
  .middle-header {
    text-align: center;
    font-size: 28px;
    color: #333333;
    font-weight: 600;
    margin-top: 20px;
  }
  .outer{
    display: flex;
    .show-case {
      margin: 30px auto 30px auto;
      display: flex;
      .main {
        display: flex;
        flex-direction: column;
        align-items: center;
        .title {
          font-size: 48px;
          color: #FF8501;
          font-weight: 600;
        }
        .desc {
          font-size: 20px;
          color: #333333;
          margin-top: 10px;
          font-weight: 600;
        }
      }
      .divider {
        width: 1px;
        background-color: #9A9A9A;
        margin: 0 90px;
      }
    }
  }
  img {
    width: 1200px;
    margin: 30px auto 30px auto;
  }
  P {
    margin: 30px auto 30px auto;
    width: 1200px;
    text-align: left;
    font-size: 20px;
    color: #333333;
  }
}
.ft3 {
  font-size: 20px;
  margin-left: -5px;
}
</style>