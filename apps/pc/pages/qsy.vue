<template>
  <div>
    <solution-header title="企事业单位食堂配送解决方案">
      保障食品安全，降低经营风险，颠覆传统配送模式
      <br />
      减少中间环节损耗让企事业单位食堂配送管理更高效
      <template #img>
        <img
          loading="lazy"
          class="gxst_header__img"
          src="~/assets/qsy/header_illustration.png"
          alt="企事业单位食堂配送解决方案"
        />
      </template>
    </solution-header>
    <div class="qsy_content">
      <div class="qsy_content__pain text-center">
        <h3 class="qsy_content__title">企事业单位食堂配送行业痛点</h3>
        <ul class="flex flex-row justify-between w">
          <li class="pain_item">
            <img
              class="inline-block pain_item__img"
              src="~/assets/qsy/pain_icon1.png"
              alt="企业经营管理难"
              width="132"
            />
            <h5 class="pain_item__title">企业经营管理难</h5>
            <p class="pain_item__desc">
              生鲜管理制度不明确
              <br />
              作业环节多
              <br />
              人才培养难
            </p>
          </li>
          <li class="pain_item">
            <img
              class="inline-block pain_item__img"
              src="~/assets/qsy/pain_icon2.png"
              alt="生产过程管控难"
              width="132"
            />
            <h5 class="pain_item__title">生产过程管控难</h5>
            <p class="pain_item__desc">
              人力成本高
              <br />
              采购成本高
              <br />
              难以提升业绩
            </p>
          </li>
          <li class="pain_item">
            <img
              class="inline-block pain_item__img"
              src="~/assets/qsy/pain_icon3.png"
              alt="食品安全溯源难"
              width="132"
            />
            <h5 class="pain_item__title">食品安全溯源难</h5>
            <p class="pain_item__desc">
              对账慢，流水账记录
              <br />
              账目易混乱，对账清算难
              <br />
              资金压力大
            </p>
          </li>
          <li class="pain_item">
            <img class="inline-block pain_item__img" src="~/assets/qsy/pain_icon4.png" alt="数据分析难" width="132" />
            <h5 class="pain_item__title">数据分析难</h5>
            <p class="pain_item__desc">
              损耗层级多，成本损耗不清晰
              <br />
              产品转换，成本毛利无法统计
              <br />
              数据不全，生产预测难，易浪费
            </p>
          </li>
        </ul>
      </div>
      <div class="qsy_content__process">
        <img
          class="process_illustration"
          src="~/assets/qsy/process_illustration.png"
          alt="企事业单位食堂配送流程图"
          width="1204"
        />
      </div>
      <ul class="qsy_content__main">
        <li class="sys_item">
          <div class="w flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;订单处理 一键完成</h3>
              <p class="item_desc">50个菜品，2分钟高效精准下单</p>
              <p class="item_desc">
                支持套餐订单，可按照套餐数量
                <br />
                在购物车分解成原料
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/qsy/sys_info1.png" alt="订单处理 一键完成" width="760" />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="w flex flex-row-reverse justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">
                数据化采购管控、智能定价
                <br />
                采购成本立省5%
              </h3>
              <p class="item_desc">根据客户下单明细，实时汇总生成采购需求</p>
              <p class="item_desc">供应商在线询价报价，智能比价，采购成本更可控</p>
              <p class="item_desc">采购收货智能称实重，采购供应商监管有据可依</p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/qsy/sys_info2.png" alt="数据化采购管控、智能定价 采购成本立省5%" width="633" />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="w flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;分拣称重
                提效60%
              </h3>
              <p class="item_desc">
                智能电子秤同步分拣重量，免去手工统计
                <br />
                杜绝错分漏分
              </p>
              <p class="item_desc">
                分拣任务支持按商品、分类、客户等多种
                <br />
                维度提前分配
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/qsy/sys_info3.png" alt="分拣称重 提效60%" width="642" />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="w flex flex-row-reverse justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">物流配送 轻松排线</h3>
              <p class="item_desc">自带司机APP，送货有导航</p>
              <p class="item_desc">支持按商品金额、重量、体积排线，降低车辆空载率</p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/qsy/sys_info4.png" alt="物流配送 轻松排线" width="636" />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="w flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;生鲜溯源 食安保障
              </h3>
              <p class="item_desc">区块链+大数据，一品一码，扫码溯源</p>
              <p class="item_desc">
                数据大屏与食品检测设备打通
                <br />
                清晰展示订单溯源信息
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/qsy/sys_info5.png" alt="生鲜溯源 食安保障" width="748" />
            </div>
          </div>
        </li>
      </ul>
    </div>
    <footer class="qsy_sidebar text-center">
      <h3 class="qsy_sidebar__title">标杆案例</h3>
      <div class="w">
        <div class="qsy_sidebar__tab flex justify-center">
          <!--          <div class="tab_item" :class="{ active: activeTab === 'mjd' }" @mouseenter="changeTab('mjd')">上海麦金地</div>-->
          <div
            class="tab_item"
            :class="{ active: activeTab === 'yxt' }"
            target="__blank"
            href="/xueyuan/c-37181.html#content"
            @mouseenter="changeTab('yxt')"
          >
            重庆幼鲜通
          </div>
          <div
            class="tab_item"
            :class="{ active: activeTab === 'mw' }"
            target="__blank"
            href="/xueyuan/c-36752.html#content"
            @mouseenter="changeTab('mw')"
          >
            四川玛王农业
          </div>
        </div>
        <div class="qsy_sidebar__content">
          <a v-show="activeTab === 'mjd'" class="content_item flex justify-around items-center">
            <p class="content_item__desc">
              为华为、阿里巴巴、西门子、微软、中国商飞等500多家企事业单位提供高端团膳服务。与蔬东坡合作后，麦金地的产品和服务都有了显著提升，极大地提高了在学校营养餐及企事业单位团膳的配送效率。
            </p>
            <img src="~/assets/qsy/company_icon1.png" alt="上海麦金地" width="305" />
          </a>
          <a
            v-show="activeTab === 'yxt'"
            href="/xueyuan/c-37181.html#content"
            target="__blank"
            class="content_item flex justify-around items-center"
          >
            <p class="content_item__desc">
              重庆百姓之家旗下品牌“幼鲜通”专注幼儿园营养膳食食材配送服务，有专业高效的物流团队和配送网络。面对创业初期分拣效率低、人工算单、发车慢等难题，选择与蔬东坡合作，解决了业务瓶颈，进一步提升了管理效率。
            </p>
            <img src="~/assets/qsy/company_icon2.png" alt="重庆幼鲜通" width="327" />
          </a>
          <a
            v-show="activeTab === 'mw'"
            target="__blank"
            href="/xueyuan/c-36752.html#content"
            class="content_item flex justify-around items-center"
          >
            <p class="content_item__desc">
              品牌起源于2014年，专注团膳食材配送业务，主要服务对象为企事业、学校、医院等。现有厂房占地20余亩，总建筑面积10000㎡+。与蔬东坡合作后，既保障食品安全，又做到控制损耗，为企业降低了成本和经营风险。
            </p>
            <img src="~/assets/qsy/company_icon3.png" alt="四川玛王农业" width="236" />
          </a>
        </div>
        <div></div>
      </div>
    </footer>
  </div>
</template>

<script setup>
useHead({
  title: "企事业单位食堂配送解决方案_食材配送系统_单位食堂配送解决方案_蔬菜配送-蔬东坡",
  meta: [
    {
      name: "description",
      content: "保障食品安全，降低经营风险，颠覆传统配送模式，减少中间环节损耗让企事业单位食堂配送更高效",
    },
    {
      name: "keywords",
      content: "食材配送系统,企事业单位食堂食材采购,团膳配送系统,团膳食堂ERP,企事业单位食堂解决方案",
    },
  ],
});
const activeTab = ref("yxt");
const changeTab = tab => {
  activeTab.value = tab;
};
</script>

<style scoped>
.qsy_header {
  height: 620px;
  padding-top: 194px;
  background: url(~/assets/qsy/qsy_header_bg.png) no-repeat bottom/cover;
  color: #fff;
  .small-btn {
    width: 200px;
    margin: 50px 0 0;
    color: #2977fe;
    background-color: #fff;
  }
  &__title {
    font: bolder 36px PingFang SC;
    letter-spacing: 4px;
  }
  &__desc {
    margin-top: 60px;
    font-size: 20px;
    font-weight: lighter;
  }
  &__img {
    width: 420px;
    transform: translateX(48px);
  }
}
.qsy_content {
  margin-top: 50px;
  &__title {
    margin-bottom: 94px;
    color: #333;
    font: 24px PingFang SC;
  }
  &__pain {
    margin-bottom: 100px;
    .pain_item {
      position: relative;
      width: 250px;
      padding: 96px 0 22px;
      background: linear-gradient(313.8deg, #eef5ff 3.23%, #f9fafd 98.67%);
      box-shadow: 2px 4px 4px rgba(59, 105, 220, 0.05);
      border-radius: 5px;
      &__img {
        position: absolute;
        top: -50px;
        left: 0;
        right: 0;
        margin: auto;
        width: 130px;
      }
      &__title {
        margin-bottom: 15px;
        color: #333;
        font: 20px PingFang SC;
      }
      &__desc {
        color: #333;
        font-size: 14px;
        line-height: 216.5%;
      }
    }
  }
  &__process {
    background-color: #f5f8ff;
    .process_illustration {
      width: 1034px;
      margin: 0 auto;
      padding: 100px 0;
    }
  }
  &__main {
    .ds_content__title {
      display: inline-block;
      color: #333;
      font-size: 24px;
      &::after {
        content: "";
        display: block;
        width: 330px;
        height: 6px;
        margin: 10px 0 30px;
        background: url(~/assets/qsy/title_icon_right.png) no-repeat center/cover;
      }
    }
    .item_desc {
      position: relative;
      margin: 0 0 30px 20px;
      color: #333;
      font-size: 16px;
      &::before {
        content: "";
        position: absolute;
        top: 4px;
        left: -22px;
        display: inline-block;
        width: 15px;
        height: 15px;
        background: url(~/assets/qsy/item_icon.png) no-repeat center/cover;
      }
    }
    .w {
      width: 1020px;
    }
    .sys_item {
      padding: 70px 0 76px;
      &:nth-child(2n) {
        background: #f9fbff;
      }
      &:nth-child(2n + 1) {
        .sys_item_detail {
          transform: translateX(100px);
        }
      }
      &:nth-child(2n) .ds_content__title::after {
        background-image: url(~/assets/qsy/title_icon_left.png);
      }
      &:nth-child(1) {
        .ds_content__title::after {
          transform: translateX(-82px);
        }
        img {
          width: 560px;
        }
      }
      &:nth-child(2) {
        img {
          width: 497px;
        }
      }
      &:nth-child(3) {
        .ds_content__title::after {
          transform: translateX(-26px);
        }
        img {
          width: 462px;
        }
      }
      &:nth-child(4) {
        img {
          width: 527px;
        }
      }
      &:nth-child(5) {
        .ds_content__title::after {
          transform: translateX(-42px);
        }
        img {
          width: 544px;
        }
      }
    }
  }
}
.qsy_sidebar {
  height: 540px;
  color: #fff;
  background: url(~/assets/qsy/qsy_sidebar_bg.jpg) no-repeat top/cover;
  &__title {
    padding: 75px 0 40px;
    font: bolder 24px PingFang SC;
  }
  .w {
    width: 900px;
  }
  &__tab {
    .tab_item {
      width: 300px;
      height: 40px;
      line-height: 40px;
      font-size: 20px;
      &:not(.active) {
        background: rgba(255, 255, 255, 0.2);
      }
    }
    .active {
      background-color: #2173ff;
    }
  }
  &__content {
    .content_item {
      margin-top: 34px;
      &:nth-child(n + 2) {
        margin-top: 64px;
      }
      &__desc {
        width: 550px;
        font-size: 16px;
      }
      &:nth-child(1) img {
        width: 280px;
      }
      &:nth-child(2) img {
        width: 300px;
      }
      &:nth-child(3) img {
        width: 217px;
      }
    }
  }
}
</style>
