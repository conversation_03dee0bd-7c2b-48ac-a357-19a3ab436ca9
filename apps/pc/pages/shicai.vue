<template>
  <div class="shicai">
    <header class="ds_header">
      <div class="w text-center">
        <h1 class="ds_header__title">生鲜配送系统</h1>
        <p class="ds_header__desc">生鲜企业效能提升50%+，让生鲜人每天多睡两小时</p>
        <p class="ds_header__desc__mini">
          生鲜配送全流程数字化，实现业务降本增效；功能全面且实用性强，帮助企业大幅降低损耗，效能至少可提升50%
        </p>
        <SubmitButton class="small-btn">获取专属方案</SubmitButton>
      </div>
    </header>
    <div class="ds_content">
      <div class="ds_content__intro w1065 text-center">
        <h3 class="ds_content__title">3~5% 净利润提升</h3>
        <ul class="flex flex-row justify-between">
          <li class="intro_item">
            <img class="inline-block" src="~/assets/delivery_sys/intro_icon1.png" alt="商品损耗降低20%" />
            <p class="intro_item__desc">商品损耗降低</p>
            <p class="intro_item__desc em">20%</p>
          </li>
          <li class="intro_item">
            <img class="inline-block" src="~/assets/delivery_sys/intro_icon2.png" alt="分拣速度提高60%" width="62" />
            <p class="intro_item__desc">分拣速度提高</p>
            <p class="intro_item__desc em">60%</p>
          </li>
          <li class="intro_item">
            <img class="inline-block" src="~/assets/delivery_sys/intro_icon2.png" alt="订单效率提高90%" width="62" />
            <p class="intro_item__desc">订单效率提高</p>
            <p class="intro_item__desc em">90%</p>
          </li>
          <li class="intro_item">
            <img class="inline-block" src="~/assets/delivery_sys/intro_icon2.png" alt="准确率高达99.9%" width="62" />
            <p class="intro_item__desc">准确率高达</p>
            <p class="intro_item__desc em">99.9%</p>
          </li>
        </ul>
      </div>
      <ul class="ds_content__main">
        <li class="sys_item" id="dingdan">
          <div class="w1065 flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">订单处理，2分钟轻松完成</h3>
              <p class="item_desc">
                下单汇总
                <br />
                独家四合一商城，再来一单、代客下单等多
                <br />
                种下单方式，一键汇总全部订单
              </p>
              <p class="item_desc">
                补单改单
                <br />
                实时同步改单补单操作，随时同步库存、采购
                <br />
                以及订单信息
              </p>
              <p class="item_desc">
                联动库存
                <br />
                精准计算损耗、预测采购
              </p>
              <SubmitButton class="small-btn" />
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/delivery_sys/sys_info1.png" alt="订单处理，2分钟轻松完成" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="caigou">
          <div class="w1065 flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">采购管理，有效降低35%采购成本</h3>
              <p class="item_desc">
                供应商比价
                <br />
                日常询价记录对比，拒绝贪腐，有效降低采
                <br />
                购成本
              </p>
              <p class="item_desc">
                自动分配采购任务
                <br />
                自动分配，无需人工干预，扫码随时看明细
              </p>
              <p class="item_desc">
                采购进度可视化
                <br />
                通过采购APP随时查看任务、追踪采购情况，
                <br />
                一目了然
              </p>
              <SubmitButton class="small-btn" />
            </div>
            <div class="add-padding flex-shrink-0">
              <img src="~/assets/delivery_sys/sys_info2.png" alt="采购管理，有效降低35%采购成本" width="612" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="fenjian">
          <div class="w1065 flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">分拣称重0出错，提效60%</h3>
              <p class="item_desc">
                一体化智能称
                <br />
                一键打印分拣标签，同步分拣进度，回传商
                <br />
                品实重
              </p>
              <p class="item_desc">
                分拣进度可视化
                <br />
                时监控分拣情况，避免影响发货
              </p>
              <p class="item_desc">
                分拣缺货标记
                <br />
                同步库存情况，杜绝错分、漏分
              </p>
              <p class="item_desc">
                支持多种分拣方式
                <br />
                支持客户分拣、商品分拣、路线分拣等多种方
                <br />
                式，有效优化分拣流程
              </p>
              <SubmitButton class="small-btn" />
            </div>
            <div class="add-padding flex-shrink-0">
              <img src="~/assets/delivery_sys/sys_info3.png" alt="分拣称重0出错，提效60%" width="687" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="kucun">
          <div class="w1065 flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">库存管理，精准运营</h3>
              <p class="item_desc">
                进销存管理
                <br />
                精细化库区、库位、货架管理，减少90%找
                <br />
                货时间
              </p>
              <p class="item_desc">
                掌上库房
                <br />
                扫码收货、盘点，效率提升200%
              </p>
              <p class="item_desc">
                无纸化库房管理
                <br />
                移动操作，记录不丢失，随时随地查看库房情况
              </p>
              <SubmitButton class="small-btn" />
            </div>
            <div class="add-padding2 flex-shrink-0">
              <img src="~/assets/delivery_sys/sys_info4.png" alt="库存管理，精准运营" width="652" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="paixian">
          <div class="w1065 flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">高效排线，半小时搞定2000家</h3>
              <p class="item_desc">
                可视化物流排线
                <br />
                用画图的方式进行排线，可视化且高效
              </p>
              <p class="item_desc">
                多种排线方式
                <br />
                支持按商品金额、重量、体积排线，降低
                <br />
                空载率
              </p>
              <p class="item_desc">
                配送地图
                <br />
                实时查看司机位置
              </p>
              <SubmitButton class="small-btn" />
            </div>
            <div class="add-padding3 flex-shrink-0">
              <img src="~/assets/delivery_sys/sys_info5.png" alt="高效排线，半小时搞定2000家" width="674" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="caiwu">
          <div class="w1065 flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">财务报表，清晰明了</h3>
              <p class="item_desc">
                账务数据自动生成
                <br />
                每笔订单均有据可查，流水记录清晰明了
              </p>
              <p class="item_desc">
                灵活统计
                <br />
                支持事后改价、抹零等；报表、图表模式任意
                <br />
                切换，随时查看经营状况
              </p>
              <p class="item_desc">
                全流程损耗统计
                <br />
                实时掌控采购、库房、配送、手动报损等数据，
                <br />
                降低至少20%损耗
              </p>
              <SubmitButton class="small-btn" />
            </div>
            <div class="add-padding flex-shrink-0">
              <img src="~/assets/delivery_sys/sys_info6.png" alt="财务报表，清晰明了" width="690" />
            </div>
          </div>
        </li>
      </ul>
      <div class="ds_content__core text-center">
        <h3 class="ds_content__title">核心优势</h3>
        <ul class="flex flex-row justify-between w">
          <li class="core_item">
            <img
              class="inline-block core_item__img"
              src="~/assets/delivery_sys/core_icon1.png"
              alt="场景全"
              width="136"
            />
            <h5 class="core_item__title">场景全</h5>
            <p class="core_item__desc">
              覆盖多种业务形态
              <br />
              全面支持上下游业务对接整合
            </p>
          </li>
          <li class="core_item">
            <img
              class="inline-block core_item__img"
              src="~/assets/delivery_sys/core_icon2.png"
              alt="管理细"
              width="136"
            />
            <h5 class="core_item__title">管理细</h5>
            <p class="core_item__desc">
              全流程信息化管控
              <br />
              损耗 ↓ 20%，效能 ↑ 50%
            </p>
          </li>
          <li class="core_item">
            <img
              class="inline-block core_item__img"
              src="~/assets/delivery_sys/core_icon3.png"
              alt="服务好"
              width="136"
            />
            <h5 class="core_item__title">服务好</h5>
            <p class="core_item__desc">
              1对1专属客服
              <br />
              8大服务中心，覆盖200+城市
            </p>
          </li>
          <li class="core_item">
            <img
              class="inline-block core_item__img"
              src="~/assets/delivery_sys/core_icon4.png"
              alt="技术强"
              width="136"
            />
            <h5 class="core_item__title">技术强</h5>
            <p class="core_item__desc">
              99%上线率
              <br />
              年度更新功能1600+
              <br />
              灵活对接第三方软件
            </p>
          </li>
        </ul>
      </div>
      <div class="ds_content__apply w text-center">
        <h3 class="ds_content__title">应用场景</h3>
        <p class="ds_content__desc">覆盖30W+生鲜配送企业业务场景</p>
        <Swiper
          effect="coverflow"
          :modules="[SwiperEffectCoverflow, SwiperNavigation]"
          :loop="true"
          :centered-slides="true"
          :initial-slide="1"
          :slides-per-view="2"
          :slide-to-clicked-slide="true"
          :coverflow-effect="{
            rotate: 0,
            stretch: 70,
            depth: 100,
            modifier: 3,
            slideShadows: false,
          }"
          :navigation="{
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
          }"
          @swiper="setSwiper"
        >
          <SwiperSlide>
            <a href="/p2/" target="_blank" style="display: block;">
              <img
                class="apply_img"
                src="~/assets/delivery_sys/apply_item2.jpg"
                alt="餐饮酒店配送解决方案"
                width="427"
              />
              <p class="apply_desc">餐饮酒店配送解决方案</p>
            </a>
          </SwiperSlide>
          <SwiperSlide>
            <a href="/qsy/" target="_blank" style="display: block;">
              <img
                class="apply_img"
                src="~/assets/delivery_sys/apply_item3.jpg"
                alt="企事业单位食堂解决方案"
                width="427"
              />
              <p class="apply_desc">企事业单位食堂解决方案</p>
            </a>
          </SwiperSlide>
          <SwiperSlide>
            <a href="/tuanshan/" target="_blank" style="display: block;">
              <img class="apply_img" src="~/assets/delivery_sys/apply_item4.png" alt="团餐供应链解决方案" width="427" />
              <p class="apply_desc">团餐供应链解决方案</p>
            </a>
          </SwiperSlide>
          <SwiperSlide>
            <a href="/gxst/" target="_blank" style="display: block;">
              <img
                class="apply_img"
                src="~/assets/delivery_sys/apply_item5.jpg"
                alt="高校食堂食材供应链管理解决方案"
                width="427"
              />
              <p class="apply_desc">高校食堂食材供应链管理解决方案</p>
            </a>
          </SwiperSlide>
          <SwiperSlide>
            <a href="/qsy/" target="_blank" style="display: block;">
              <img
                class="apply_img"
                src="~/assets/delivery_sys/apply_item3.jpg"
                alt="企事业单位食堂解决方案"
                width="427"
              />
              <p class="apply_desc">企事业单位食堂解决方案</p>
            </a>
          </SwiperSlide>
        </Swiper>
        <img class="swiper-button swiper-button-prev" src="~/assets/common/arrow-left.png" alt="应用场景" />
        <img class="swiper-button swiper-button-next" src="~/assets/common/arrow-right.png" alt="应用场景" />
        <div class="ds_content__apply--btn small-btn" @click="handleSlideNext">下一页</div>
      </div>
    </div>
    <footer class="ds_sidebar text-center">
      <h3 class="ds_sidebar__title">上万家客户信赖选择</h3>
      <CustomerScroll></CustomerScroll>

      
    </footer>
    <div class="newMessage-container" v-if="newMessageList.length">
        <div class="newMessage-title">蔬东坡最新资讯</div>
        <Message :info-list="newMessageList" :max-length="15" />
        <div class="indicator"></div>
        <a class="newMessage-more" href="https://www.sdongpo.com/xueyuan/" target="_blank">查看更多 ></a>
      </div>
  </div>
</template>

<script setup>
// 页面锚点定位
import useAnchor from "../hooks/useAnchor";
import Message from "./message.vue";
useAnchor();
useHead({
  title: "生鲜配送系统_生鲜配送软件_蔬菜配送软件-蔬东坡",
  meta: [
    {
      name: "description",
      content:
        "蔬东坡生鲜配送系统,专为生鲜配送企业开发的业务管理软件,广泛应用于生鲜配送、蔬菜配送、食材配送、农产品配送等场景,已帮助超10000家生鲜企业实现降本增效",
    },
    { name: "keywords", content: "生鲜配送软件,蔬菜配送软件,生鲜配送系统,蔬东坡蔬菜配送软件,蔬菜配送管理系统" },
  ],
  script: [
    {
      src: "https://hm.baidu.com/hm.js?37f9e8255a5fe1f567b5720f6cba2d2c",
      bodyClose: false,
      async: true,
      defer: true,
    },
  ],
});
const data = reactive({
  swiper: null,
});
const setSwiper = swiper => {
  // swiper.slideToLoop(2)
  data.swiper = swiper;
};
const handleSlideNext = () => {
  data.swiper && data.swiper.slideNext();
};
// 最新资讯
let newMessageList = ref([]);

const mergeArraysAlternating = (arr1, arr2) => {
  const newArr = [];
  const existingIds = new Set();

  let i = 0, j = 0;
  const len1 = arr1.length;
  const len2 = arr2.length;

  while (i < len1 || j < len2) {
    // 处理arr1
    if (i < len1) {
      const obj1 = arr1[i];
      if (!existingIds.has(obj1.id)) {
        newArr.push(obj1);
        existingIds.add(obj1.id);
        i++;
      } else {
        i++; 
      }
    }

    // 处理arr2
    if (j < len2) {
      let added = false;
      while (j < len2 && !added) {
        const obj2 = arr2[j];
        if (!existingIds.has(obj2.id)) {
          newArr.push(obj2);
          existingIds.add(obj2.id);
          added = true; 
        } else {
          j++; 
        }
      }
    }
  }

  return newArr.slice(0, 10);
};

const res1 = await getNewMessageList({
  order: "desc",
  per_page: 10,
  orderby: "date",
  search: ["生鲜配送系统"],
});
const res2 = await getNewMessageList({
  order: "desc",
  per_page: 10,
  orderby: "date",
  search: ["蔬菜配送系统"],
});

newMessageList.value = mergeArraysAlternating(res1, res2)

</script>
<style>
.shicai .ds_sidebar  .overflow-hidden {
  width: 1900px !important;
}
</style>
<style scoped>
@import url(~/assets/style/ds.css);
.newMessage-container {
  margin: 40px auto 80px; 
  width: 100%;
}
</style>
