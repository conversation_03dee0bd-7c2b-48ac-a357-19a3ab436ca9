<template>
  <div class="sxkt">
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">生鲜课堂</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">Fresh Research Institute</h2>
      </div>
    </div>
    <StudyCenterTabs>
    <div class="g-block">
      <div class="search-box">
        <!-- <div class="search-line">
          <div class="label">基础配置：</div>
          <div class="category-list">
            <div
              class="name" :class="{
                'is-active': item === curC1
              }"
              v-for="(item, index) in category1" :key="index"
              @click="selectC1(item)"
            >
              {{ item }}
            </div>
          </div>
        </div> -->
        <div class="search-line">
          <span class="label">业务：</span>
          <div class="category-list">
            <div
              class="name"
              :class="{
                'is-active': item === curC2
              }"
              v-for="(item, index) in category2" :key="index"
              @click="selectC2(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <ArticleList
        :list="showList"
      />
    </div>
    </StudyCenterTabs>
  </div>
</template>
<script setup>
useHead({
  title: '蔬东坡-生鲜课堂',
  meta: [
    { name: 'description', content: '蔬东坡生鲜课堂涵盖系统演示、客户端体验、新手引导、疑难问题查询等对于生鲜配送系统使用的不同方面内容，以不断优化用户体验和服务质量。' },
    { name: 'keywords', content: '生鲜研究院，生鲜课堂，蔬东坡' },
  ],
});
let category1 = ref([])
let category2 = ref([])
const allList = ref([])
let showList = ref([])
let curC1 = ref('查看全部')
let curC2 = ref('查看全部')

const getArticleList = async () => {
  let res = await getStudyCenterPages('生鲜课堂')
  if(res.status) {
    console.log('res', res)
    allList.value = res.data.list || []
    showList.value = res.data.list || []
    console.log('allList', allList)
    let c1 = res.data.base_config || []
    let c2 = res.data.business_config || []
    // allList.value.forEach(item => {
    //   console.log('item', item)
    //   if(Array.isArray(item.base_config) && item.base_config.length > 0) {
    //     console.log('base_config', item.base_config)
    //     c1=[
    //       ...item.base_config,
    //       ...c1,
    //     ]
    //   }
    //   if(Array.isArray(item.business_config) && item.business_config.length > 0) {
    //     console.log('business_config', item.business_config)
    //     c2 = [
    //       ...item.business_config,
    //       ...c2
    //     ]
    //   } 
    // })
    c1.unshift('查看全部')
    c2.unshift('查看全部')
    category1.value = Array.from(new Set(c1))
    category2.value = Array.from(new Set(c2))
    console.log('category1', category1)
    console.log('category2', category2)
  }
}
getArticleList()

const getShowArticleList = () => {
  const c1 = curC1.value
  const c2 = curC2.value
  showList.value = allList.value
  if(c1 !== '查看全部') {
    showList.value = showList.value.filter(item => {
      return item.base_config.findIndex((base) => c1 === base) !== -1
    })
  }
  if(c2 !== '查看全部') {
    showList.value = showList.value.filter(item => {
      return item.business_config.findIndex((business) => c2 === business) !== -1
    })
  }
  console.log('showList-value', showList.value)

}
const selectC1 = (c1) => {
  curC1.value = c1
  getShowArticleList()
}
const selectC2 = (c2) => {
  curC2.value = c2
  getShowArticleList()
}
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);

.sxkt {
  color: #333333;
  .news_header {
    background: url(../assets/xueyuan/common_bg.png) no-repeat center center;
    background-size: 100% 100%;
  }
  .search-box {
    width: 980px;
    margin: 0 auto;
    
    .search-line {
      margin-bottom: 40px;
      display: flex;
    }
    .label {
      margin-right: 20px;
      font-size: 20px;
      vertical-align: top;
    }
    .category-list {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 10px 20px;
      .name {
        cursor: pointer;
        font-size: 16px;
        font-weight: 400;
        padding: 2px 10px;
        border-radius: 5px;
      }
    }
  }
  .is-active {
    color: #2977FE;
    background-color: #EAF1FF;
  }
  .g-block {
    background-color: #F9FBFF;
    padding-top: 50px;
  }
}
</style>