<template>
  <div class="api">
    <div class="api__banner">
      <div class="wrap">
        <h1>
          <span>食材阳光智慧采购平台</span>
        </h1>
        <h2>
          <p>数字化助力阳光食材采购</p>
          <p>构建校园食安与供应链监管新生态</p>
        </h2>
        <SubmitButton class="small-btn">立即咨询</SubmitButton>
      </div>
    </div>
    <div class="api__tenlight">
      <div class="wrap">
        <h2>
          <div>
            <span class="light_color">一个</span>中心、<span class="light_color"
              >六大</span
            >运营系统、<span class="light_color">十大</span>核心亮点
          </div>
          <div>构建校园食安与供应链监管新生态</div>
        </h2>
        <img
          class="linner_tab"
          src="../assets/zhcg/linner_tab.png"
          alt="食材阳光智慧采购平台"
        />
        <div class="tenlight_bg_wrap">
          <img
            class="tenlight_bg"
            src="../assets/zhcg/tenlight_bg.png"
            alt="食材阳光智慧采购平台"
          />
          <div class="point tenlight_text1">学校食堂进销存管理</div>
          <div class="point tenlight_text2">多维度食材价格管控</div>
          <div class="point tenlight_text3">智能化菜谱库</div>
          <div class="point tenlight_text4">三方对账</div>
          <div class="point tenlight_text5">供应商全生命周期管控</div>
          <div class="point tenlight_text6">晨检巡检 AI识别监管 配送车载GPS</div>
          <div class="point tenlight_text7">校园食安管控</div>
          <div class="point tenlight_text8">多维度溯源监管</div>
          <div class="point tenlight_text9">监管驾驶舱大屏</div>
          <div class="point tenlight_text10">家校联动</div>
          <div class="tenlight_text11 light_color2">蔬东坡食材阳光</div>
          <div class="tenlight_text12 light_color2">智慧采购平台</div>
        </div>
      </div>
    </div>
    <div class="api__operationflow">
      <div class="wrap">
        <h2>
          <!-- <div><span class="light_color">一个</span>中心、<span class="light_color">六大</span>运营系统、<span class="light_color">十大</span>核心亮点</div> -->
          <div>校园食材阳光采购平台业务流程示意</div>
        </h2>
        <img
          class="linner_tab"
          src="../assets/zhcg/linner_tab.png"
          alt="食材阳光智慧采购平台"
        />
        <div class="tenlight_bg_wrap">
          <img
            class="tenlight_bg"
            src="../assets/zhcg/operationflow_bg.png"
            alt="食材阳光智慧采购平台"
          />
          <ul class="text_ul">
            <li>生产基地</li>
            <li>合作社</li>
            <li>批发市场</li>
          </ul>
          <div class="color_size2 tenlight_text1">平台运营方</div>
          <div class="color_size2 tenlight_text2">供应商</div>
          <div class="color_size2 tenlight_text3">学校自营食堂或团餐公司</div>
          <div class="color_size2 tenlight_text4">食材检测</div>
          <div class="color_size2 tenlight_text12">教体局监管</div>
          <div class="color_size1 tenlight_text5">
            <span class="color_size3">1.</span>平台下单
          </div>
          <div class="color_size1 tenlight_text6">
            <span class="color_size3">2.</span>食材采购 (大宗)
          </div>
          <div class="color_size1 tenlight_text7">
            <span class="color_size3">3.</span> 订单分配
          </div>
          <div class="color_size1 tenlight_text8">
            <span class="color_size3">4.</span>食材入库
          </div>
          <div class="color_size1 tenlight_text9">
            <span class="color_size3">5.</span>食材配送<br /><span
              style="visibility: hidden"
              >检</span
            >检测报告<br /><span style="visibility: hidden">标</span>标准单据
          </div>
          <div class="color_size1 tenlight_text10">
            <span class="color_size3">6.</span>支付结算
          </div>
          <div class="color_size1 tenlight_text11">
            <span class="color_size3">7.</span>对 账 清 算
          </div>
        </div>
      </div>
    </div>
    <div class="api__gystj" id="ptyy">
      <div :class="'outer' + index" v-for="(item, index) in dataSetList" :key="index">
        <div class="wrap">
          <LRLayout :data-source="item" :is-right="item.isRight"></LRLayout>
        </div>
      </div>
    </div>
    <div class="api__dsjjgxyt" id="dsjjg">
      <div :class="'outer' + index" v-for="(item, index) in bigNumList" :key="index">
        <div class="wrap">
          <LRLayout :data-source="item" :is-right="item.isRight"></LRLayout>
        </div>
      </div>
    </div>
    <div class="api__xysa" id="xysa">
      <div :class="'outer' + index" v-for="(item, index) in schoocsafeList" :key="index">
        <div class="wrap">
          <LRLayout :data-source="item" :is-right="item.isRight"></LRLayout>
        </div>
      </div>
    </div>
    <div class="api__xyhc" id="xyhc">
      <div
        :class="'outer' + index"
        v-for="(item, index) in schoolManageList"
        :key="index"
      >
        <div class="wrap">
          <LRLayout :data-source="item" :is-right="item.isRight"></LRLayout>
        </div>
      </div>
    </div>
    <div class="api__jxld" id="jxld">
      <div
        :class="'outer' + index"
        v-for="(item, index) in familyManageList"
        :key="index"
      >
        <div class="wrap">
          <LRLayout :data-source="item" :is-right="item.isRight"></LRLayout>
        </div>
      </div>
    </div>
    <div class="api__znyj" id="znyj">
      <div :class="'outer' + index" v-for="(item, index) in smartList" :key="index">
        <div class="wrap">
          <LRLayout :data-source="item" :is-right="item.isRight"></LRLayout>
        </div>
      </div>
    </div>
    <div class="api__footer">
      <div class="wrap">
        <h1 class="footer_header">
            选择蔬东坡，食安管理更放心
        </h1>
        <CarImg></CarImg>
      </div>
    </div>
  </div>
</template>
<script setup>
import gystj from "~/assets/zhcg/gystj.png";
import zncp from "~/assets/zhcg/zncp.png";
import gysgl from "~/assets/zhcg/gysgl.png";
import cwdz from "~/assets/zhcg/cwdz.png";
import syjg from "~/assets/zhcg/syjg.png";
import jgdp from "~/assets/zhcg/jgdp.png";
import znyj from "~/assets/zhcg/znyj.png";
import jxld from "~/assets/zhcg/jxld.png";
import xyhc from "~/assets/zhcg/xyhc.png";
import mczm from "~/assets/zhcg/mczm.png";
import top_title_icon from "~/assets/zhcg/top_title_icon.png";
useHead({
  title: "食材阳光智慧采购平台—蔬东坡",
  meta: [
    {
      name: "description",
      content:
        "食材阳光智慧采购平台—蔬东坡，多个区域监管平台上线，数字化助力阳光食材采购，构建校园食安与供应链监管新生态，创新构建一个中心、六大运营系统、十大核心亮点功能",
    },
    {
      name: "keywords",
      content: "食材阳光智慧采购平台—蔬东坡，政府食材采购监管，校园食材采购安全监管，学校食材集中采购",
    },
  ],
});
const dataSetList = [
  {
    titleTop: "平台运营系统",
    imgUrl: gystj,
    imgAlt: "平台运营系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["多维度食材价格管控", "让食材采购监管更透明"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "支持平台运营方对食材进行市场询价",
      "供应商在线报价比价",
      "平台供应商报价审核",
      "下浮率限价管理",
    ],
  },
  {
    titleTop: "平台运营系统",
    imgUrl: zncp,
    imgAlt: "平台运营系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["智能菜谱下单，减少食材浪费", "保障营养均衡"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "智能菜品库自动生成菜谱",
      "结合营养成分分析，实现学校菜谱智能下单",
      "对原料进行采购量反算",
      "支持选餐制",
    ],
    isRight: true,
  },
  {
    titleTop: "平台运营系统",
    imgUrl: cwdz,
    imgAlt: "平台运营系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["财务对账，高效透明"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "供应商、学校、监管方线上三方对账",
      "科学管理膳食经费，支持财务结算审批",
      "与第三方财务系统灵活对接",
    ],
  },
  {
    titleTop: "平台运营系统",
    imgUrl: gysgl,
    imgAlt: "平台运营系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["供应商全生命周期管控", "有据可依"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "供应商准入，供应商履约过程监管  ",
      "供应商评价盲选：结合商品品质、送货及时率",
      "缺货率、服务态度等维度对供应商进行综合评价",
    ],
    isRight: true,
  },
];
const bigNumList = [
  {
    titleTop: "大数据监管系统",
    imgUrl: syjg,
    imgAlt: "大数据监管系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["全链条溯源监管，保障食品安全", "源头可溯、过程可控"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "索证索票",
      "多维度溯源监管",
      "交易数据可视化监管",
      "供应商准入及过程监管",
    ],
  },
  {
    titleTop: "大数据监管系统",
    imgUrl: jgdp,
    imgAlt: "大数据监管系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["监管驾驶舱数据大屏，阳光公示", "数据实时呈现"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: ["食安大屏", "溯源大屏", "配送大屏", "阳光交易监管大屏"],
    isRight: true,
  },
];
const schoocsafeList = [
  {
    titleTop: "校园食安智慧监管系统",
    imgUrl: mczm,
    imgAlt: "校园食安智慧监管系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["校园食安安全管控后台", "实时预警，食安风险早发现！"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "明厨亮灶系统",
      "AI监管识别",
      "IOT智能化物联设备",
      "校园食堂餐厅数据大屏公示",
    ],
  },
];

const schoolManageList = [
  {
    titleTop: "校园后厨管理系统",
    imgUrl: xyhc,
    imgAlt: "校园后厨管理系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["学校食堂进销存管理", "电子台账一目了然"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "线上食材比价",
      "学校食堂进销存管理",
      "学校财务管理",
      "线上食材采购",
      "移动端索票索证",
    ],
    isRight: true,
  },
];
const familyManageList = [
  {
    titleTop: "家校联动系统",
    imgUrl: jxld,
    imgAlt: "家校联动系统",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["家校联动系统", "实现让家长“看得见”的安全！"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: [
      "缴费管理",
      "菜谱公示",
      "线上请假、报餐",
      "陪餐管理",
      "投诉建议及评价",
    ],
  },
];
const smartList = [
  {
    titleTop: "智能硬件接入平台",
    imgUrl: znyj,
    imgAlt: "智能硬件接入平台",
    imgWidth: 660,
    imgHeight: 0,
    titleCenterTextList: ["整合智能化设备", "实现物联网高效监管"],
    titleAfterText: "",
    top_title_icon: top_title_icon,
    itemDescList: ["智能收货核磅   ", "晨检巡检", "AI识别监管", "配送车载GPS"],
    isRight: true,
  },
];
</script>
<style>
.light_color2 {
  color: #3E74F6FF;
  font-size: 28px;
  font-weight: 600;
}
.api .api__znyj .l-r-layout .change_img {
  height: 400px !important;
  /* transform: translateY(-30px); */
}
.api .api__znyj .l-r-layout .rightImg {
  height: 470px;
  /* transform: translateY(-30px); */
}
.api .api__jxld .l-r-layout .change_img {
  height: 400px !important;
  transform: translateY(-30px);
}
.api .api__dsjjgxyt .l-r-layout .change_img {
  transform: translateY(-40px);
}
</style>
<style lang="postcss" scoped>
.api {
  &__banner {
    background-image: url("../assets/zhcg/banner.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 600px;
    .wrap {
      width: 1400px;
      margin: 0 auto;
      padding-left: 15px;
      padding-top: 205px;
    }
    h1 {
      display: flex;
      align-items: center;
      margin-bottom: 45px;
      span {
        &:first-child {
          font-size: 56px;
          font-family: AlimamaShuHeiTi-Bold;
          font-weight: 600;
          color: #333333ff;
          line-height: 66px;
        }
      }
    }
    h2 {
      margin-bottom: 30px;
      p {
        font-size: 20px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #3e74f6ff;
        line-height: 23px;
        margin-bottom: 15px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
  &__tenlight {
    background-color: #f6f8feff;
    height: 900px;
    .wrap {
      width: 1400px;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      h2 {
        margin-top: 60px;
        div {
          font-size: 36px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 600;
          color: #333333ff;
          line-height: 50px;
          text-align: center;
          &:last-child {
            margin-bottom: 30px;
          }
        }
        .light_color {
          color: #3e74f6ff;
        }
      }
      .linner_tab {
        width: 80px;
      }
      .tenlight_bg_wrap {
        position: relative;
        .point {
          position: relative;
        }
        .point::before {
          content: "";
          position: absolute;
          left: -15px; /* 调整圆点的位置 */
          top: 50%;
          transform: translateY(-50%); /* 垂直居中 */
          width: 6px;
          height: 6px;
          background-color: #3e74f6ff;
          border-radius: 50%;
        }
        .tenlight_bg {
          width: 1200px;
        }
        .tenlight_text1 {
          position: absolute;
          top: 88px;
          left: 65px;
        }
        .tenlight_text2 {
          position: absolute;
          top: 255px;
          left: 65px;
        }
        .tenlight_text3 {
          position: absolute;
          top: 283px;
          left: 65px;
        }
        .tenlight_text4 {
          position: absolute;
          top: 310px;
          left: 65px;
        }
        .tenlight_text5 {
          position: absolute;
          top: 338px;
          left: 65px;
        }
        .tenlight_text6 {
          position: absolute;
          top: 530px;
          left: 65px;
        }
        .tenlight_text7 {
          position: absolute;
          top: 88px;
          right: 130px;
        }
        .tenlight_text8 {
          position: absolute;
          top: 282px;
          right: 110px;
        }
        .tenlight_text9 {
          position: absolute;
          top: 310px;
          right: 110px;
        }
        .tenlight_text10 {
          position: absolute;
          top: 530px;
          right: 165px;
        }
        .tenlight_text11 {
          position: absolute;
          top: 340px;
          right: 500px;
        }
        .tenlight_text12 {
          position: absolute;
          top: 380px;
          right: 512px;
        }
      }
    }
  }
  &__operationflow {
    height: 900px;
    .wrap {
      width: 1400px;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      h2 {
        margin-top: 60px;
        div {
          font-size: 36px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 600;
          color: #333333ff;
          line-height: 50px;
          text-align: center;
          &:last-child {
            margin-bottom: 30px;
          }
        }
        .light_color {
          color: #3e74f6ff;
        }
      }
      .linner_tab {
        width: 80px;
      }
      .tenlight_bg_wrap {
        position: relative;
        .tenlight_bg {
          width: 1200px;
        }
        .color_size1 {
          color: #3f3f3fff;
          font-size: 19px;
          font-weight: 600;
        }
        .color_size2 {
          color: #ffffffff;
          font-size: 24px;
          font-weight: 400;
        }
        .color_size3 {
          color: #3e74f6ff;
        }
        .text_ul {
          position: absolute;
          top: 80px;
          left: 70px;
          display: flex;
          gap: 30px;
          flex-direction: column;
          li {
            width: 10px;
            color: #ffffffff;
            font-size: 24px;
            font-weight: 400;
          }
        }
        .tenlight_text1 {
          position: absolute;
          top: 205px;
          left: 468px;
        }
        .tenlight_text2 {
          position: absolute;
          top: 475px;
          left: 435px;
        }
        .tenlight_text3 {
          position: absolute;
          top: 205px;
          right: 23px;
        }
        .tenlight_text4 {
          position: absolute;
          top: 475px;
          right: 73px;
        }
        .tenlight_text5 {
          position: absolute;
          top: 98px;
          right: 380px;
        }
        .tenlight_text6 {
          position: absolute;
          top: 150px;
          left: 180px;
        }
        .tenlight_text7 {
          position: absolute;
          top: 280px;
          left: 410px;
          width: 10px;
        }
        .tenlight_text8 {
          position: absolute;
          top: 440px;
          left: 180px;
        }
        .tenlight_text9 {
          position: absolute;
          top: 450px;
          right: 260px;
        }
        .tenlight_text10 {
          position: absolute;
          top: 198px;
          right: 380px;
        }
        .tenlight_text11 {
          position: absolute;
          top: 280px;
          left: 520px;
          width: 10px;
        }
        .tenlight_text12 {
          position: absolute;
          top: 350px;
          right: 365px;
        }
      }
    }
  }
  &__gystj {
    height: 2450px;
    background-color: #f6f8feff;
    .wrap {
      width: 1100px;
      margin: 0 auto;
      padding-top: 50px;
    }
    .outer1,
    .outer3 {
      background-color: #ffffffff;
    }
  }
  &__dsjjgxyt {
    height: 1215px;
    background-color: #f6f8feff;
    .wrap {
      width: 1100px;
      margin: 0 auto;
      padding-top: 50px;
    }
    .outer1,
    .outer3 {
      background-color: #ffffffff;
    }
  }
  &__xyhc {
    height: 700px;
    .wrap {
      width: 1200px;
      margin: 0 auto;
      padding-top: 50px;
    }
    .outer1,
    .outer3 {
      background-color: #ffffffff;
    }
  }
  &__jxld {
    height: 700px;
    background-color: #f6f8feff;

    .wrap {
      width: 1100px;
      margin: 0 auto;
      padding-top: 50px;
    }
    .outer1,
    .outer3 {
      background-color: #ffffffff;
    }
  }
  &__znyj {
    height: 700px;
    .wrap {
      width: 1200px;
      margin: 0 auto;
      padding-top: 0px;
    }
    .outer1,
    .outer3 {
      background-color: #ffffffff;
    }
  }
  &__xysa {
    height: 700px;
    background-color: #f6f8feff;
    .wrap {
      width: 1100px;
      margin: 0 auto;
      padding-top: 50px;
    }
    .outer1,
    .outer3 {
      background-color: #ffffffff;
    }
  }
  &__footer {
    background-image: url("../assets/zhcg/footer_bg.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100%;
    height: 650px;
    .wrap {
      width: 1400px;
      padding-top: 55px;
      margin: 0 auto;
      .footer_header {
        text-align: center;
        font-size: 18px;
        color: #FFFFFFFF;
        padding-bottom: 40px;
      }
    }
  }
}
</style>
