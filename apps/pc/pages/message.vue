<template>
    <div class="info-list" v-if="infoList && infoList.length">
      <!-- 左列资讯 -->
      <div class="info-column" v-if="_isDouble">
        <el-link
          v-for="(item, index) in leftList"
          :key="'left-' + index"
          class="info-item"
          :class="{ 'hide-dot': !item?.title?.rendered }"
          :href="`/xueyuan/c-${item.id}.html`"
          target="_blank"
          :underline="false"
          @click="handleClick(item)"
        >
          <span class="ellipsis">{{ item?.title?.rendered }}</span>
        </el-link>
      </div>
      <!-- 分割线 -->
      <el-divider direction="vertical" v-if="_isDouble" />
      <!-- 右列资讯 -->
      <div class="info-column" v-if="_isDouble" >
        <el-link
          v-for="(item, index) in rightList"
          :key="'right-' + index"
          class="info-item"
          :class="item.title.rendered?'has-title':''"
          :href="`/xueyuan/c-${item.id}.html`"
          target="_blank"
          :underline="false"
          @click="handleClick(item)"
        >
          <span class="ellipsis">{{ item?.title?.rendered || '' }}</span>
        </el-link>
      </div>
      <!-- 单列资讯 -->
       <template v-if="!_isDouble&&lastList.length">
        <div class="info-column">
        <el-link
          v-for="(item, index) in lastList"
          :key="'left-' + index"
          class="info-item"
          :class="{ 'hide-dot': !item.post_title }"
          :href="`/xueyuan/c-${item.ID}.html`"
          target="_blank"
          :underline="false"
          @click="handleClick(item)"
        >
          <span v-if="item.post_title" class="ellipsis">{{ item?.post_title }}</span>
        </el-link>
      </div>
       </template>
      <template v-if="!_isDouble&&leftList.length">
        <div class="info-column">
        <el-link
          v-for="(item, index) in leftList"
          :key="'left-' + index"
          class="info-item"
          :class="{ 'hide-dot': !item.title.rendered }"
          :href="`/xueyuan/c-${item.id}.html`"
          target="_blank"
          :underline="false"
          @click="handleClick(item)"
        >
          <span class="ellipsis">{{ item?.title?.rendered }}</span>
        </el-link>
      </div>
      </template> 
    </div>
  </template>
  
  <script setup>
  import { computed, onMounted } from "vue";
  const props = defineProps({
    infoList: {
      type: Array,
      required: true,
    },
    maxLength: {
      type: Number,
      default: 15,
    },
    isDouble: {
      type: Boolean,
      default: true
    }
  });
  const emit = defineEmits(["item-click"]);
  let _isDouble = props.isDouble
  let leftList = []
  let rightList = []
  let lastList = []

  if(_isDouble){
      // 计算左右两列的资讯列表
       leftList = computed(() =>
        props.infoList.slice(0, Math.ceil(props.infoList.length / 2))
      );
       rightList = computed(() =>
        props.infoList.slice(Math.ceil(props.infoList.length / 2))
      );
  }else{
    const temp = props.infoList
    const temp2 = temp[0] ||{}
    if(temp.length > 0 && temp2.hasOwnProperty('post_title')){
      lastList = temp
    }else{
      leftList = temp
    }
  }
  
  
  // 处理点击事件
  const handleClick = (item) => {
    emit("item-click", item);
  };
  </script>
  
  <style scoped>
.info-list {
  display: flex;
  /* justify-content: space-between;
  align-items: center;  */
  padding: 16px;
  background-color: #fff;
}

.info-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
}

.divider {
  width: 1px;
  background-color: #666666;
  margin: 0 16px;
}

.info-item {
  display: inline-block;
  color: #333;
  font-size: 18px;
  text-decoration: none; 
  position: relative;
  line-height: 25px; 
  text-align: left;
  margin-left: 20%;
}

/* 默认小圆点 */
.info-item::before {
  content: "";
  position: absolute;
  left: -18px; /* 调整圆点的位置 */
  top: 50%;
  transform: translateY(-50%); /* 垂直居中 */
  width: 8px;
  height: 8px;
  background-color: #444448;
  border-radius: 50%;
}

/* 当 title 为空时隐藏小圆点 */
.info-item.hide-dot::before {
  content: none; /* 隐藏伪元素 */
}

.info-item:hover {
  color: #449af6;
}

/* 鼠标悬停的小圆点颜色变蓝 */
.info-item:not(.hide-dot):hover::before {
  background-color: #449af6;
}

/* 文字超出一行省略号处理 */
.ellipsis {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 360px;
}

.el-divider {
  height: auto;
  align-self: stretch; 
  margin: 0 16px;
  background-color: #666666; 
  width: 1px; 
}
</style>
