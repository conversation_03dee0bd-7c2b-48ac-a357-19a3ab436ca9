<template>
    <div class="ai-order-component">
        <!-- 标题 -->
        <div class="title">
            {{ obj1.header_text1 }} <span class="status">{{ obj1.header_text2 }}</span>
        </div>
        <div class="outer_container">
            <!-- 业务场景 -->
            <div class="section">
                <div class="section-header">
                    <span class="blue-block"></span>{{ obj1.title1 }}
                </div>
                <!-- <div class="text_container"> -->
                <p class="section-content">{{ obj1.title1_text }}</p>
                <!-- </div> -->
                <div v-if="sceneImages && sceneImages.length" class="image-container">
                    <img v-for="(img, index) in sceneImages" :key="index" :src="img" alt="业务场景图片" />
                </div>
            </div>

            <!-- 操作演示 -->
            <div class="section">
                <div class="section-header">
                    <span class="blue-block"></span>{{ obj1.title2 }}
                </div>
                <p class="section-content">{{ obj1.title2_text }}</p>
                <!-- <div class="img-outer-container"> -->
                <div v-if="demoImages && demoImages.length" class="image-container">
                    <img v-for="(img, index) in demoImages" :key="index" :src="img" alt="操作演示图片"
                        style="max-width: 800px;" />
                </div>
                <!-- </div> -->
            </div>

            <!-- 按钮 -->
            <div class="button-container">
                <SubmitButton class="action-button">免费试用名额预约</SubmitButton>
            </div>
        </div>
    </div>
</template>
<script setup>
const props = defineProps({
    infoObj: {
        type: Object,
        default() {
            return {
                header_text1: '',
                header_text2: '',
                title1: '',
                title1_text: '',
                title2: '',
                title2_text: '',
                sceneImages: [],
                demoImages: [],
            }
        }
    },

});
const obj1 = props.infoObj;
const sceneImages = obj1.sceneImages;
const demoImages = obj1.demoImages;
</script>
<style scoped>
.ai-order-component {
    border-radius: 8px;
    padding: 20px;
    font-family: Arial, sans-serif;
    min-width: 1110px;
}

.outer_container {
    background: #F6F8FE;
    border-radius: 20px;
}

.title {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin: 20px auto 0 auto;
    width: 200px;
    line-height: 40px;
    background-color: #32b8f1;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;

}

.status {
    font-size: 14px;
    font-weight: 300;
}

.section {
    padding-top: 20px;
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.blue-block {
    width: 6px;
    height: 16px;
    background-color: #007aff;
    margin-right: 8px;
    border-radius: 2px;
}


.section-content {
    width: 1000px;
    margin: 0 auto;
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 1.6;
    margin-bottom: 10px;
    text-align: left;
}


.image-container {
    width: 1000px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    flex: 1;
    gap: 10px;
}

.image-container img {
    width: 100%;
    max-width: 450px;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.button-container {
    text-align: center;
    margin-top: 20px;
}

.action-button {
    width: 160px;
    background-color: #007aff;
    color: white;
    font-size: 16px;
    /* padding: 10px 20px; */
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.action-button:hover {
    background-color: #005bb5;
}
</style>