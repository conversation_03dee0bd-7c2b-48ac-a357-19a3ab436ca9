<!--
 * @Description: 行业会议页面
 * @Date: 2023-06-07 15:51:57
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-07-07 09:37:04
 * @FilePath: /shudongpo-website/apps/pc/pages/hymt.vue
-->
<template>
  <div class="hymt">
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">行业会议</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">集结前沿观点，分享大咖见解，引领行业新风尚</h2>
      </div>
    </div>
    <StudyCenterTabs>
      <div class="g-block">
        <div class="w">
          <p class="title2">最新行业活动</p>
          <p class="desc2">了解行业前沿动态，解读优秀企业发展路径</p>
          <div class="article-list w">
            <div
              class="article"
              v-in-view-animate="['fly-up']"
              v-for="(item, index) in curList"
              :key="index"
              @click="jumpDetail(item?.redirect_url)"
            >
              <img loading="lazy" class="img" :src="item.conver_image" :alt="item.post_title" />
              <div class="txt-box">
                <p class="title text-two-line">
                  {{ item.post_title }}
                </p>
                <p class="article__desc">发布时间：{{ item.post_date_format }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="old-box" v-show="oldList.length>0">
          <div class="old-desc">
            <div class="left">
              <p class="old-title">往期活动：</p>
              <p class="old-txt" v-for="(item, index) in oldList" :key="index" @click="jumpDetail(item.redirect_url)">
              {{ item.post_title }}
              </p>
            </div>
            <div class="right" v-show="oldList[0]?.conver_image">
              <img @click="jumpDetail(oldList[0]?.redirect_url)" loading="lazy" :src="oldList[0]?.conver_image" :alt="oldList[0]?.post_title">
            </div>
          </div>
        </div>
      </div>
    </StudyCenterTabs>
  </div>
</template>
<script setup>
useHead({
  title: '蔬东坡-行业会议',
  meta: [
    { name: 'description', content: '了解行业前言动态，解读优秀企业发展路径，集结前沿观点，分享大咖见解，引领行业新风尚。' },
    { name: 'keywords', content: '行业会议，蔬东坡' },
  ],
});
let curList = ref([]);
let oldList = ref([]);

const getArticleList = async () => {
  const res = await getStudyCenterPages('行业会议')
  if (res.status) {
    const data = res.data.list || []
    curList.value = data.filter(item => item.activity_type === '活动预告');
    oldList.value = data.filter(item => item.activity_type === '往期活动').splice(0,3);
  }
};
getArticleList();
const jumpDetail = (url) => {
  if(!url) return
  const urlParams =url&&new URL(url);
  window.open(url,'_blank')
  /* if(urlParams&&urlParams.hostname == window.location.hostname){
     window.location.href = url
  }else{
    window.open(url,'_blank')
  } */

}
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);
@import url(~/assets/style/hymt.css);
</style>
