<!--
 * @Description: 行业会议页面
 * @Date: 2023-06-07 15:51:57
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-07-07 09:37:04
 * @FilePath: /shudongpo-website/apps/pc/pages/hymt.vue
-->
<template>
  <div class="hymt">
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">行业会议</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">集结前沿观点，分享大咖见解，引领行业新风尚</h2>
      </div>
    </div>
    <StudyCenterTabs>
      <div class="g-block">
        <div class="w">
          <p class="title2">最新行业活动</p>
          <p class="desc2">了解行业前沿动态，解读优秀企业发展路径</p>
          <div class="article-list w">
            <div
              class="article"
              v-in-view-animate="['fly-up']"
              v-for="(item, index) in curList"
              :key="index"
              @click="jumpDetail(item?.redirect_url)"
            >
              <img loading="lazy" class="img" :src="item.conver_image" :alt="item.post_title" />
              <div class="txt-box">
                <p class="title text-two-line">
                  {{ item.post_title }}
                </p>
                <p class="article__desc">发布时间：{{ item.post_date_format }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="old-box" v-show="oldList.length>0">
          <div class="old-desc">
            <div class="left">
              <p class="old-title">往期活动：</p>
              <p class="old-txt" v-for="(item, index) in oldList" :key="index" @click="jumpDetail(item.redirect_url)">
              {{ item.post_title }}
              </p>
            </div>
            <div class="right" v-show="oldList[0]?.conver_image">
              <img @click="jumpDetail(oldList[0]?.redirect_url)" loading="lazy" :src="oldList[0]?.conver_image" :alt="oldList[0]?.post_title">
            </div>
          </div>
        </div>
      </div>
    </StudyCenterTabs>
  </div>
</template>
<script setup>
useHead({
  title: '蔬东坡-行业会议',
  meta: [
    { name: 'description', content: '了解行业前言动态，解读优秀企业发展路径，集结前沿观点，分享大咖见解，引领行业新风尚。' },
    { name: 'keywords', content: '行业会议，蔬东坡' },
  ],
});
let curList = ref([]);
let oldList = ref([]);

const getArticleList = async () => {
  const res = await getStudyCenterPages('行业会议')
  if (res.status) {
    const data = res.data.list || []
    curList.value = data.filter(item => item.activity_type === '活动预告');
    oldList.value = data.filter(item => item.activity_type === '往期活动').splice(0,3);
  }
};
getArticleList();
const jumpDetail = (url) => {
  if(!url) return
  const urlParams =url&&new URL(url);
  window.open(url,'_blank')
  /* if(urlParams&&urlParams.hostname == window.location.hostname){
     window.location.href = url
  }else{
    window.open(url,'_blank')
  } */

}
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);

.hymt {
  color: #333333;
  .news_header {
    background: url(../assets/xueyuan/common_bg.png) no-repeat center center;
    background-size: 100% 100%;
    &__desc {
      font-size: 28px;
    }
  }
  .g-block {
    background-color: #f9fbff;
    padding-top: 50px;
  }
  .w {
    width: 980px;
    margin: 0 auto;
  }
  .w1 {
    width: 1100px;
    margin: 0 auto;
  }
  .title2 {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 10px;
    text-align: center;
  }
  .desc2 {
    text-align: center;
    font-size: 16px;
    color: #666666;
  }
  .article-list {
    margin-top: 50px;
    margin-bottom: 20px;
    color: #333333;
    .article {
      display: inline-flex;
      flex-direction: column;
      cursor: pointer;
      width: 300px;
      margin: 0 30px 30px 0;
      box-shadow: 1px 1px 2px 1px rgba(28, 102, 231, 0.1);
      border-radius: 5px;
      &:nth-child(3n) {
        margin-right: 0;
      }
      .img {
        width: 300px;
        height: 175px;
      }
      .txt-box {
        background-color: #ffffff;
        padding: 15px 13px 13px 18px;
        .title {
          margin-bottom: 10px;
          font: bolder 18px PingFang SC-Medium, PingFang SC;
        }
      }
    }
    
  }
  .old-box {
    width: 1100px;
    padding: 30px 120px 150px 90px;
    
    margin: 0 auto;
    border-top: 1px solid #E6E6E6;
    p {
      margin-bottom: 15px;
    }
    .old-title {
      font-size: 16px;
    }
    .old-txt {
      cursor: pointer;
      font-size: 14px;
      color: #666666;
    }
    .old-desc {
      display: flex;
      justify-content: space-between;
    }
    .right {
      flex-shrink: 0;
      margin-left: 20px;
      img {
        width: 260px;
        height: 151px;
        border-radius: 5px;
        cursor: pointer;
      }
    }
  }
}
</style>
