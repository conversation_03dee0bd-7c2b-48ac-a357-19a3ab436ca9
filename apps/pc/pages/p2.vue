<template>
  <solution-header title="餐饮酒店配送解决方案">
    提供一站式生鲜配送系统解决方案
    <br />
    助力餐饮酒店配送企业降本增效
    <template #img>
      <img loading="lazy" class="gxst_header__img" src="~/assets/p2/header-img.png" alt="餐饮酒店配送解决方案" />
    </template>
  </solution-header>
  <div class="tongdian">
    <h2 class="tongdian-title">餐饮酒店配送行业痛点</h2>
    <div class="tongdian-content">
      <div class="tongdian-item">
        <img src="../assets/p2/rengong.png" alt="人工接单效率低" />
        <div>人工接单效率低</div>
      </div>
      <div class="tongdian-item">
        <img src="../assets/p2/caigou.png" alt="采购成本高难管控" />
        <div>采购成本高难管控</div>
      </div>
      <div class="tongdian-item">
        <img src="../assets/p2/fenjian.png" alt="分拣品类多易出错" />
        <div>分拣品类多易出错</div>
      </div>
      <div class="tongdian-item">
        <img src="../assets/p2/peisong.png" alt="配送排线耗时长" />
        <div>配送排线耗时长</div>
      </div>
      <div class="tongdian-item">
        <img src="../assets/p2/zhangmu.png" alt="账目混乱管控难" />
        <div>账目混乱管控难</div>
      </div>
      <div class="tongdian-item">
        <img src="../assets/p2/caipin.png" alt="菜品损耗高难统计" />
        <div>菜品损耗高难统计</div>
      </div>
    </div>
  </div>
  <div class="liucheng bg-gray">
    <img src="../assets/p2/liucheng.png" alt="餐饮酒店配送流程图" />
  </div>
  <RichSection
    :padding="true"
    :btn="false"
    :title="'微信商城下单，收单效率提升100%'"
    :desc="[
      '告别传统纸质下单，收单效率提升100%，获客方式更高效',
      '微信小程序+公众号h5商城+支付宝商城全渠道覆盖',
      '多种在线支付方式，大幅度盘活生鲜配送企业现金流',
      '营销拓客CRM，精细化客户标签，掌握客户消费偏好\n摆脱对销售员的依赖',
    ]"
    :descStyle="{}"
    :titleStyle="{ whiteSpace: 'nowrap' }"
  >
    <template #img>
      <img src="../assets/p2/section_1.png" slot="img" alt="微信商城下单，收单效率提升100%" width="447" />
    </template>
  </RichSection>
  <div class="bg-gray">
    <RichSection
      :padding="true"
      :btn="false"
      :reverse="true"
      :title="'智能分拣称重 ，分拣0差错'"
      :desc="[
        '一键称重自动打签，数据直传系统后台',
        '随时查看分拣进度，杜绝错分漏分，分拣0差错',
        '平均每千个包裹降低至少50斤菜品损耗',
        '分拣包裹贴实重二维码标签，大幅度提升现场交货速度',
      ]"
      :descStyle="{}"
    >
      <template #img>
        <img src="../assets/p2/section_2.png" slot="img" alt="智能分拣称重 ，分拣0差错" width="472" />
      </template>
    </RichSection>
  </div>
  <RichSection
    :padding="true"
    :btn="false"
    :title="'可视化物流排线，单人半小时\n完成2000家客户物流排线'"
    :desc="['可结合车辆运力和客户地址批量排线，提升运力和配送效率', '配送企业和客户方可查看司机实时位置，配送轨迹']"
    :descStyle="{}"
  >
    <template #img>
      <img
        src="../assets/p2/section_3.png"
        slot="img"
        alt="可视化物流排线，单人半小时 完成2000家客户物流排线"
        width="540"
      />
    </template>
  </RichSection>
  <div class="bg-gray">
    <RichSection
      :padding="true"
      :btn="false"
      :reverse="true"
      :title="'数据化采购管控\n采购成本立省5%'"
      :desc="[
        '根据客户下单明细，实时汇总生成采购需求',
        '供应商在线询价报价，智能比价，采购成本更可控',
        '采购收货智能称实重，采购供应商监管有据可依',
      ]"
      :descStyle="{}"
      :sectionTextStyle="{ paddingLeft: '55px' }"
    >
      <template #img>
        <img src="../assets/p2/section_4.png" slot="img" alt="数据化采购管控 采购成本立省5%" width="504" />
      </template>
    </RichSection>
  </div>
  <RichSection
    :padding="true"
    :btn="false"
    :title="'自动对账，一键结算\n成本利润当天出'"
    :desc="['可先款后货，多退少补 应收应付款自动生成', '单品成本利润，单客户成本利润当天可看\n大幅度提升企业经营效率']"
    :descStyle="{}"
  >
    <template #img>
      <img src="../assets/p2/section_5.png" slot="img" alt="自动对账，一键结算 成本利润当天出" width="461" />
    </template>
  </RichSection>
  <div class="bg-gray">
    <RichSection
      :padding="true"
      :btn="false"
      :reverse="true"
      :title="'多维损耗报表\n助力配送企业降低损耗20%'"
      :desc="[
        '采购损耗、库房损耗、配送损耗实时掌控',
        '各项损耗趋势比重多维度清晰呈现',
        '助力配送企业精细化管控损耗，降低菜品损耗20%',
      ]"
      :descStyle="{}"
      :sectionTextStyle="{ paddingLeft: '55px' }"
    >
      <template #img>
        <img src="../assets/p2/section_6.png" slot="img" alt="多维损耗报表 助力配送企业降低损耗20%" width="426" />
      </template>
    </RichSection>
  </div>

  <BenchmarkCases :bg="caseBg" :items="cases" />
</template>
<script setup>
import caseBg from '../assets/p2/footer-bg.png';
import caiwuyou from '../assets/p2/caiwuyou.png';
import lvxianda from '../assets/p2/lvxianda.png'

useHead({
  title: '餐饮酒店配送解决方案_餐饮配送管理系统_餐饮酒店配送管理-蔬东坡',
  meta: [
    { name: 'description', content: '为餐饮酒店配送行业提供一站式生鲜配送系统解决方案，助力餐饮酒店配送企业降本增效' },
    { name: 'keywords', content: '餐饮配送软件,餐饮管理系统,餐饮配送管理系统,餐饮食品配送企业,餐饮酒店配送解决方案' },
  ],
});
const cases = [
  {
    title: '菜无忧',
    desc: '成立于2021年，与蔬东坡合作后，为其制定中小餐饮配送解决方案，现已为菜无忧搭建了信息化运营管理体系与先进的生鲜分拣中心，成功打造垂直供应链体系，在提升运转效率的同时也提高了企业的核心竞争力。',
    img: caiwuyou,
    link: '/xueyuan/c-35432.html#content'
  },
	{
		title: '绿鲜达',
		desc: '云南绿鲜达网络科技有限公司成立于2015年，搭建了“互联网+农产品”模式下的生鲜电商平台，2023年预计营收突破5个亿。随着业务量上涨，绿鲜达急需一款供应链管理软件，在市场上多方挑选后，最终选择了能够高度满足B2B业务运营需求的蔬东坡，双方一拍即合。如今，绿鲜达采取“基地+农户+公司”的方式，减少了农产品流通渠道，凭借自建仓储、物流、配送、创新升级农产品的供应链，成为了区域龙头。',
		img: lvxianda,
    link: '/xueyuan/c-37831.html#content'
	}
];
</script>
<style lang="postcss" scoped>
.bg-gray {
  background-color: #f5f8ff;
}

.p2_header {
  > div {
    padding-left: 65px;
  }
  height: 620px;
  padding-top: 194px;
  background: url(~/assets/p2/header-bg.png) no-repeat bottom/cover;
  color: #fff;
  .small-btn {
    width: 200px;
    margin: 50px 0 0;
    color: #2977fe;
    background-color: #fff;
  }
  &__title {
    font: bolder 36px PingFang SC;
    letter-spacing: 4px;
  }
  &__desc {
    margin-top: 60px;
    font-size: 20px;
    font-weight: lighter;
  }
  &__img {
    width: 420px;
    margin-left: 270px;
  }
}

.tongdian {
  width: 1200px;
  margin: 0 auto;
  padding: 50px 0 30px;
  color: #333;
  text-align: center;

  &-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 50px;
  }

  &-content {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  &-item {
    font-size: 20px;
    color: #333;
    font-weight: 500;
    width: 220px;
    height: 250px;
    margin: 0 50px 50px;
    background-color: #f1f7fe;
    padding-top: 22px;

    img {
      display: block;
      width: 130px;
      height: 130px;
      margin: 0 auto 40px;
    }
  }
}

.liucheng {
  padding: 95px 0;

  img {
    display: block;
    width: 1036px;
    margin: 0 auto;
  }
}
</style>
