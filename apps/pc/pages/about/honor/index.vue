<template>
  <div class="wrap">
    <Part1 />
    <Part2 />
    <Part3 />
    <Part4 />
    <Part5 />
    <Part6 />
    <Part7 />
  </div>
</template>
<script setup>
import Part1 from './Part1.vue';
import Part2 from './Part2.vue';
import Part3 from './Part3.vue';
import Part4 from './Part4.vue';
import Part5 from './Part5.vue';
import Part6 from './Part6.vue';
import Part7 from './Part7.vue';

useHead({
  title: '蔬东坡企业荣誉_行业领先地位—连续多年市占率靠前',
  meta: [
    {
      name: 'description',
      content: '蔬东坡生鲜SaaS ERP系统，经过十多年发展，经过权威平台认证，已经成为行业市场占有率第一系统，做生鲜配送就用蔬东坡',
    },
    { name: 'keywords', content: '蔬东坡怎么样|蔬东坡企业介绍|生鲜配送行业排名' },
  ],
  script: [
    {
      src: 'https://hm.baidu.com/hm.js?5fa4f2fc5624f544affb211eab296f46',
      bodyClose: false,
      async: true,
      defer: true,
    }
  ]
});
</script>
<style scoped>
.wrap {
  /* background: #0055e0; */
  width: 100%;
}
</style>