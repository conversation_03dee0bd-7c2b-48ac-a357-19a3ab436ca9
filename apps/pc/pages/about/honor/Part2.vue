<template>
  <div class="section">
    <div class="section-title">关于蔬东坡</div>
    <div class="section-content">
      <div class="section-content-left">
        <div class="section-content-left_title">《2023年中国生鲜食材供应链行业洞察》</div>
        <div class="section-content-left_container">
          报告显示，蔬东坡连续2年在生鲜食材供应链数字化行业排行第一，市场份额分别达45.7%和46.8%，且市占率增长趋势明显
        </div>
        <div class="section-content-left_tip">某权威数据平台</div>
      </div>

      <div class="section-content-right">
        <div class="section-content-right_item" v-for="(item, index) in contentInfo" :key="index">
          <div class="section-content-right_title">
            {{ item.title }}
          </div>
          <div class="section-content-right_content">
            {{ item.content }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const contentInfo = [
  {
    title: "技术驱动",
    content: "成立11年，研发团队占比超50%，年度产品更新1600+项",
  },
  {
    title: "荣誉资质",
    content: "已获得超100项专项软著，以及国家高新技术企业，S09001研发质量管理体系认证等荣誉",
  },
  {
    title: "融资信息",
    content: "累计获得多笔融资，最高单笔投资超1.5亿元，投资方由经纬创投、唯品会、明势资本、美团等顶级资本领投",
  },
];
</script>
<style scoped lang="postcss">
.section {
  width: 1200px;
  height: 100%;
  margin: 0 auto;

  &-title {
    font-size: 40px;
    text-align: center;
    margin: 40px 0;
    font-weight: 500;
  }

  &-content {
    display: flex;
    justify-content: space-between;

    &-left {
      width: 590px;
      height: 409px;
      background: url("../../../assets/honor/home_left.png") no-repeat;
      background-size: 100% 100%;
      position: relative;

      &_title {
        font-size: 26px;
        font-weight: bold;
        text-align: center;
        margin: 30px;
      }

      &_container {
        font-size: 16px;
        width: 285px;
        margin-left: 75px;
        text-align: justify;
      }

      &_tip {
        font-size: 16px;
        position: absolute;
        bottom: 25px;
        right: 40px;
        color: #fff;
        font-weight: normal;
      }
    }

    &-right {
      width: 590px;
      height: 409px;
      display: flex;
      flex-wrap: wrap;
      align-content: space-between;

      &_item {
        background-color: #0055e0;
        width: 100%;
        height: 123px;
        color: #fff;
        padding: 20px 30px;
        box-sizing: border-box;
      }

      &_title {
        font-size: 26px;
        font-weight: bold;
      }

      &_content {
        font-size: 16px;
        font-weight: normal;
      }
    }
  }
}
.section img {
  margin: 0 auto;
  display: block;
  width: 1500px;
}
</style>