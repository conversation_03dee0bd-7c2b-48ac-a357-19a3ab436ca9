<template>
  <div style="background: #f9fbff;">
    <div>
      <header class="contact_header">
        <h1 class="contact_header__title text-center">已为超万家生鲜食材供应链企业提供产品服务</h1>
      </header>
    </div>
    <div class="contact_content">
      <div class="contact_content__main">
        <img class="contact_content__img" :src="mapList[currentIndex].src" alt="联系我们" width="906" />
        <ul class="contact_content__address flex justify-between flex-wrap">
          <li
            v-for="(address, index) in addressList"
            :key="index"
            class="address-item"
            :class="{ active: currentIndex === index }"
            @mouseenter="handleChooseAddress(index)"
          >
            <h4 class="address-item__title">{{ address.title }}</h4>
            <p class="address-item__detail">{{ address.detail }}</p>
          </li>
        </ul>
      </div>
    </div>
    <footer class="contact_sidebar flex justify-between">
      <div class="phone_info">
        <p class="text_gray">售前咨询</p>
        <p class="phone_info_item">************</p>
        <p class="text_gray add_margin_top">售后咨询</p>
        <p class="phone_info_item add_margin_bottom">************</p>
      </div>
      <hr>
      <div class="code_wrapper">
        <img class="m-auto QRcode" src="~/assets/about/QRcode.png" alt="蔬东坡微信公众号，了解公司产品最新动态">
        <p class="code_desc">蔬东坡微信公众号</p>
        <p class="code_desc">了解公司产品最新动态</p>
      </div>
      <div class="code_wrapper">
        <img class="m-auto QRcode2" src="~/assets/about/QRcode2.png" alt="蔬东坡官方抖音号，立即扫码了解">
        <p class="code_desc reduce_margin_top">蔬东坡官方抖音号</p>
        <p class="code_desc">立即扫码了解</p>
      </div>
    </footer>
    <hr>
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import mapBeijing from '~/assets/about/map-beijing.png'
import mapChangsha from 'assets/about/map-changsha.png'
import mapHangzhou from '~/assets/about/map-hangzhou.png'
import mapGuangdong from '~/assets/about/map-guangdong.png'
import mapSichuang from '~/assets/about/map-sichuang.png'
import mapWuhan from '~/assets/about/map-wuhan.png'
import mapXian from '~/assets/about/map-xian.png'
import mapHenan from '~/assets/about/map-henan.png'

useHead({
  title: '联系我们,蔬东坡客服-蔬东坡',
  meta: [
    {
      name: 'description',
      content:
        '了解蔬东坡从这里开始，2014年成立，至今已经服务了10000+家的客户，每年为超过100亿的生鲜农产品流通提供服务。心动不如行动，加盟合作请撩起来。',
    },
    { name: 'keywords', content: '蔬东坡简介,蔬东坡怎么样,蔬东坡加盟合作' },
  ],
  script: [
    {
      src: 'https://hm.baidu.com/hm.js?5fa4f2fc5624f544affb211eab296f46',
      bodyClose: false,
      async: true,
      defer: true,
    }
  ]
});
const mapList = reactive([
  {
    src: mapBeijing,
  },
  {
    src: mapChangsha
  },
  {
    src: mapHangzhou
  },
  {
    src: mapGuangdong
  },
  {
    src: mapSichuang
  },
  {
    src: mapWuhan
  },
  {
    src: mapXian
  },
  {
    src: mapHenan
  },
])
const addressList = reactive([
  {
    title: '北京总部',
    detail: '北京市朝阳区富华弘燕大厦'
  },
  {
    title: '南方总部',
    detail: '湖南省长沙市岳麓区中电软件园二期'
  },
  {
    title: '浙江分部',
    detail: '杭州西湖区剑桥公社F座'
  },
  {
    title: '广州分部',
    detail: '广州市海珠区257号广东现代广告创意中心'
  },
  {
    title: '成都分部',
    detail: '四川省成都市高新区香年广场T3栋'
  },
  {
    title: '武汉分部',
    detail: '武汉市武昌区中山路456号安腾国际广场（地铁武昌火车站A出入口）'
  },
  {
    title: '西安分部',
    detail: '西安市未央区凤城九路中登文景大厦B座'
  },
  {
    title: '郑州分部',
    detail: '河南省郑州市管城回族区升龙广场1号口B座'
  },
])
let currentIndex = ref(0);
const handleChooseAddress = (index) => {
  currentIndex.value = index
}
</script>

<style scoped lang="postcss">
@import url(~/assets/style/contact.css);

.contact_content {
  padding: 30px 0 50px;
  &__main {
    width: 1000px;
    margin: 0 auto;
    padding: 48px 48px 0;
    background: #fff;
    box-shadow: 4px 4px 4px 0px rgba(204,204,204,0.2);
    border-radius: 10px;
  }
  &__img {
    width: 906px;
  }
  .address-item {
    position: relative;
    width: 400px;
    padding: 50px 0 50px 27px;
    color: #666;
    cursor: pointer;
    border-bottom: 2px solid #f5f5f5;
    &.active {
      color: #2977fe;
      &:after {
        content: '';
        position: absolute;
        top: 60px;
        left: -2px;
        width: 15px;
        height: 15px;
        background: url(~/assets/about/arrow-blue.png) no-repeat center/contain;
      }
    }
    &__title {
      font-size: 24px;
    }
    &__detail {
      padding-top: 10px;
      font-size: 20px;
    }
  }
}
.contact_sidebar {
  width: 1001px;
  margin: 50px auto 115px;
  padding: 60px 100px 60px 90px;
  color: #fff;
  background-color: #2977FE;
  border-radius: 5px;
  box-shadow: #b7f7f7 2px 4px 6px;

  .phone_info {
    color: #fff;

    .text_gray {
      font-size: 20px;
    }
    .add_margin_top {
      margin-top: 30px;
    }
    &_item {
      margin-top: 6px;
      font: 500 32px PingFang SC-Medium, PingFang SC;
    }
  }
  hr {
    width: 0px;
    height: 150px;
    margin-top: 18px;
    opacity: 1;
    border: 1px solid #fff;
  }
  .code_wrapper {
    text-align: center;
    .QRcode {
      width: 120px;;
    }
    .QRcode2 {
      width: 117px;
    }
  }
  .code_desc {
    margin-top: 10px;
    font-size: 14px;
    line-height: 25px;
    text-align: center;
  }
  .reduce_margin_top {
    margin-top: 13px;
  }
}
</style>
