<!--
 * @Author: ddcoder <PERSON><PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-28 15:22:55
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-06-09 14:22:01
-->
<template>
  <div class="about">
    <header class="about-banner">
      <div class="title">生鲜食材供应链数字化开创者</div>
    </header>
    <main class="about-main">
      <div class="w-block">
        <div class="block-1">
          <div class="title2">
            关于蔬东坡
          </div>
          <p>
            蔬东坡，生鲜食材供应链数字化开创者，成立于2014年，专注于生鲜食材供应链数字化领域。公司秉承“让生鲜人每天多睡两小时”的企业愿景，持续打造优质的产品和服务体系，致力于以先进的云计算+物联网技术，为生鲜配送、中央厨房、团膳食堂、餐饮酒店等企业提供SaaS ERP产品以及软硬件解决方案，实现30%以上的经营效率提升。
          </p>
          <div class="desc-box">
            <div class="left">
              <ul>
                <li>
                  <div class="l-icon">
                    <img src="../../assets/about/main-1-icon1.png" alt="使命">
                    <span>使 命</span>
                  </div>
                  <div class="r-txt">
                    用科技点亮商业智能
                  </div>
                </li>
                <li>
                  <div class="l-icon">
                    <img src="../../assets/about/main-1-icon2.png" alt="愿景">
                    <span>愿 景</span>
                  </div>
                  <div class="r-txt">
                    让生鲜人每天多睡两小时
                  </div>
                </li>
                <li>
                  <div class="l-icon">
                    <img src="../../assets/about/main-1-icon3.png" alt="价值观">
                    <span>价值观</span>
                  </div>
                  <div class="r-txt">
                    客户第一 诚信敬业 团队合作
                  </div>
                </li>
              </ul>
            </div>
            <div class="right">
            </div>
          </div>`
        </div>
        
      </div>

      <div class="g-block">
        <div class="block-2">
          <div class="title2">
            团队介绍
          </div>
          <p>
            蔬东坡拥有深厚的技术实力，产品研发团队占比稳定在50%以上。经多年积累，公司已拥有100余项知识产权，获国家高新技术企业、北京市“专精特新”中小企业、中国产业互联网100强等资质荣誉，备受行业及客户认可。 蔬东坡拥有完善的营销和服务网络，截至目前已设立9大本地化服务中心，累计为超万家客户提供高效和专业的服务。
          </p>
          <ul class="group-box">
            <li class="group">
              <div class="img1 img"></div>
              <!-- <img src="../../assets/about/main-2-icon1.png" alt="企业员工"> -->
              <div class="num">300人+</div>
              <div class="txt">企业员工</div>
            </li>
            <li class="group">
              <div class="img2 img"></div>
              <!-- <img src="../../assets/about/main-2-icon2.png" alt="研发团队"> -->
              <div class="num">100人+</div>
              <div class="txt">研发团队</div>
            </li>
            <li class="group">
              <div class="img3 img"></div>
              <!-- <img src="../../assets/about/main-2-icon3.png" alt="每年研发投入"> -->
              <div class="num">6000万+</div>
              <div class="txt">每年研发投入</div>
            </li>
          </ul>
        </div>
      </div>

      <div class="w-block">
        <div class="block-3">
          <div class="title2">
            发展历程
          </div>
          <div class="develop-tabs">
            <el-tabs v-model="activeName">
              <el-tab-pane name="0">
                <template #label>
                  <div class="label">
                    <span>2014-</span>
                    <span class="label-big">2015</span>
                  </div>
                </template>
                <div class="content">
                  <div class="left">
                    <span>初创新生</span>
                  </div>
                  <div class="right">
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        推出行业首款ERP产品
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        获经纬中国天使轮投资
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        国内生鲜食材供应链数字化开创者
                      </p>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="1">
                <template #label>
                  <div class="label">
                    <span>2016-</span>
                    <span class="label-big">2018</span>
                  </div>
                </template>
                <div class="content">
                  <div class="left">
                    <span>厚积薄发</span>
                  </div>
                  <div class="right">
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        成立北京、长沙两大研发中心
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        生鲜配送SaaS系统快速增长
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        服务客户数量过千家
                      </p>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="2">
                <template #label>
                  <div class="label">
                    <span>2019-</span>
                    <span class="label-big">2020</span>
                  </div>
                </template>
                <div class="content">
                  <div class="left">
                    <span>行业深耕</span>
                  </div>
                  <div class="right">
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        创立全国8大服务中心
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        成立生鲜研究院，培训上千名生鲜供应链企业高管
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        成为业内唯一能提供本地化服务的服务商
                      </p>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="3">
                <template #label>
                  <div class="label">
                    <span>2021-</span>
                    <span class="label-big">2023</span>
                  </div>
                </template>
                <div class="content">
                  <div class="left">
                    <span>强势引领</span>
                  </div>
                  <div class="right">
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        服务客户数量过万家
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        央厨和食品加工产品发布
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        完成A+轮融资，是2021年后业内最高单笔融资
                      </p>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane name="4">
                <template #label>
                  <div class="label">
                    <span>2024-</span>
                  </div>
                </template>
                <div class="content">
                  <div class="left">
                    <span>开创未来</span>
                  </div>
                  <div class="right">
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        发布高校食材供应链系统解决方案、校园食安管理 平台、供应链+项目点解决方案
                      </p>
                    </div>
                    <div class="step">
                      <i class="step-icon"></i>
                      <p>
                        唯一覆盖生鲜食材供应链全场景的服务商
                      </p>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          
        </div>
      </div>
      <div class="g-block c-block">
        <div class="block-4">
          <div class="title2">
            9大服务中心
          </div>
          <div class="center-container">
            <el-carousel
              indicator-position="none"
              height="31.24vw"
              :autoplay="false"
              @change="centerChange"
              @mouseleave.native="handleMouseLeave"
            >
              <el-carousel-item>
                <div class="center-list" v-for="(item, pIndex) in centerGroup" :key="pIndex">
                  <div
                    class="center-item"
                    v-for="(center, index) in item"
                    :key="center"
                    @mouseover="handleMouseOver(pIndex, index)"
                  > 
                    <img :src="center.curImg" :alt="center.title">
                    <div class="img-title">
                      <span>{{ center.title }}</span>
                    </div>
                  </div>
                </div>
                <div class="center-list current-img" :class="imgData.curImg ? 'current-hover-img' : ''" @mouseleave="handleMouseLeave">
                  <div
                    class="center-item"
                  > 
                    <img :src="imgData.curImg" :alt="imgData.title">
                    <div class="img-title">
                      <span>{{ imgData.title }}</span>
                      <span class="img-address">{{ imgData.address }}</span>
                    </div>
                  </div>
                </div>
              </el-carousel-item> 
            </el-carousel>
          </div>
        </div>
      </div>
      <div class="w-block">
        <div class="block-5">
          <div class="title2">
            公司荣誉
          </div>
          <div class="honor-list">
            <div class="honor-item">
              <img class="honor-img" src="../../assets/about/honor-1.png" alt="">
              <span class="honor-title">
                行业荣誉
              </span>
              <ScrollingText
                class="honor-desc"
                text="中国农业互联网先锋企,中国未来农业100强,中国产业互联网100强,中国团餐供应链优秀服务商,中国农产品供应链建设优秀单位,中国团餐信息化服务商top30,中国产业互联网推荐服务商,SaaS软件最佳服务商"
              />
            </div>
            <div class="honor-item">
              <ScrollingText
                style="margin-right: 0px;margin-left: 14px;"
                class="honor-desc"
                text="100+专利及软著,国家高新技术企业,北京市专精特新中小企业,ISO9001研发质量管理体系认证,国家三级等保认证,微信支付最佳合作服务商"
                direction="right"
              /> 
              <span class="honor-title" style="margin-right: 0px; text-align: right;margin-left: 8px;">
                技术认证
              </span>
              <img class="honor-img" src="../../assets/about/honor-2.png" alt="">
            </div>
            <div class="honor-item">
              <img class="honor-img" src="../../assets/about/honor-3.png" alt="">
              <span class="honor-title">
                媒体报道
              </span>
              <ScrollingText
                class="honor-desc"
                text="人民网,农民日报,中国日报网,艾瑞咨询,36氪,和讯网,亿欧,创业邦,i黑马,亿邦动力"
              />
            </div>
          </div>
          <HonorScroll style="margin-top: 70px;"/>
        </div>
      </div>
    </main>
  </div>
</template>
<script>
import { throttle, debounce } from 'lodash-es'
import useInViewAnimate from '../../hooks/inViewAnimate';

import addressImg1 from '../../assets/about/addressImg1.png';
import addressImgBig1 from '../../assets/about/addressImgBig1.png';
import addressImg2 from '../../assets/about/addressImg2.png';
import addressImgBig2 from '../../assets/about/addressImgBig2.png';
import addressImg3 from '../../assets/about/addressImg3.png';
import addressImgBig3 from '../../assets/about/addressImgBig3.png';
import addressImg4 from '../../assets/about/addressImg4.png';
import addressImgBig4 from '../../assets/about/addressImgBig4.png';
import addressImg5 from '../../assets/about/addressImg5.png';
import addressImgBig5 from '../../assets/about/addressImgBig5.png';
import addressImg6 from '../../assets/about/addressImg6.png';
import addressImgBig6 from '../../assets/about/addressImgBig6.png';
import addressImg7 from '../../assets/about/addressImg7.png';
import addressImgBig7 from '../../assets/about/addressImgBig7.png';
import addressImg8 from '../../assets/about/addressImg8.png';
import addressImgBig8 from '../../assets/about/addressImgBig8.png';
import addressImg9 from '../../assets/about/addressImg9.png';
import addressImgBig9 from '../../assets/about/addressImgBig9.png';

export default {
  setup() {
    useHead({
      title: '企业简介-蔬东坡',
      meta: [
        {
          name: 'description',
          content:
            '蔬东坡客户成功体系,7×24小在线客服联系方式,蔬东坡售后服务在线联系,5秒响应10分钟内解决问题',
        },
        { name: 'keywords', content: '蔬东坡电话,蔬东坡客服,蔬东坡联系方式,蔬东坡售后' },
      ],
      script: [
        {
          src: 'https://hm.baidu.com/hm.js?5fa4f2fc5624f544affb211eab296f46',
          bodyClose: false,
          async: true,
          defer: true,
        }
      ]
    });
    const activeName = ref('0')
    const imgData = ref({})
    const centerGroup = ref([])
    centerGroup.value = reactive([
      [
        {
          title: '北京总部',
          img: addressImg1,
          bigImg: addressImgBig1,
          curImg: addressImg1,
          address: '北京市朝阳区富华弘燕大厦',
          show: true,
          hover:false,
        },
        {
          title: '南方总部',
          img: addressImg2,
          bigImg: addressImgBig2,
          curImg: addressImg2,
          address: '湖南省长沙市岳麓区中电软件园二期F3栋',
          show: true,
          hover:false,
          
        },
        {
          title: '浙江分部',
          img: addressImg3,
          bigImg: addressImgBig3,
          curImg: addressImg3,
          address: '杭州西湖区剑桥公社F座',
          show: true,
          hover:false,
        },
        {
          title: '广州分部',
          img: addressImg4,
          bigImg: addressImgBig4,
          curImg: addressImg4,
          address: '广州市海珠区257号广东现代广告创意中心',
          show: true,
          hover:false,
        },
      ],
      [
        {
          title: '成都分部',
          img: addressImg5,
          bigImg: addressImgBig5,
          curImg: addressImg5,
          address: '四川省成都市高新区香年广场T3栋',
          show: true,
          hover:false,
        },
        {
          title: '武汉分部',
          img: addressImg6,
          bigImg: addressImgBig6,
          curImg: addressImg6,
          address: '武汉市武昌区中山路456号安腾国际广场（地铁武昌火车站A出入口）',
          show: true,
          hover:false,
        },
        {
          title: '西安分部',
          img: addressImg7,
          bigImg: addressImgBig7,
          curImg: addressImg7,
          address: '西安市未央区凤城九路中登文景大厦B座',
          show: true,
          hover:false,
        },
        {
          title: '郑州分部',
          img: addressImg8,
          bigImg: addressImgBig8,
          curImg: addressImg8,
          address: '河南省郑州市管城回族区升龙广场1号口B座',
          show: true,
          hover:false,
        },
        {
          title: '南京分部',
          img: addressImg9,
          bigImg: addressImgBig9,
          curImg: addressImg9,
          address: '南京市江宁区天赋蕾奥中心二楼221',
          show: true,
          hover:false,
        },
      ],
    ])
    const handleMouseOver = debounce((pIndex, index) => {
      let curItem = centerGroup.value[pIndex]
      imgData.value = curItem[index]
      curItem[index].curImg = curItem[index].bigImg;
    }, 300);

    const handleMouseLeave = () => {
      imgData.value = {}
    };
    const centerChange = (nowIndex, oldIndex) => {
      console.log('centerChange-now', nowIndex)
      console.log('centerChange-old', oldIndex)
    }
    onMounted(() => {
    })
    
    return {
      activeName,
      centerGroup,
      handleMouseOver,
      handleMouseLeave,
      centerChange,
      imgData
    };
  },
  directives: {
    'in-view-animate': {
      mounted: useInViewAnimate,
    },
  },
};
</script>

<style lang="postcss" scoped>
.about {
  min-width: 1300px;
  color: #666666;
  font-size: 16px;
  .about-banner {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #ffffff;
    background: url(../../assets/about/banner.png) no-repeat center center;
    height: 400px;
    background-size: cover;

    .title {
      padding-top: 211px;
      font: 48px PingFang SC-Medium, PingFang SC;
    }

  }
  .about-main {
    p {
      text-indent: 2em;
      text-align: left;
    }
    .block-1 {
      max-width: 1080px;
      padding-top: 73px;
      padding-bottom: 92px;
      margin: auto;
      .desc-box {
        margin-top: 48px;
        position: relative;
        .left {
          position: absolute;
          top: 80px;
          left: 60px;
          ul {
            li {
              margin-bottom: 20px;
              width: 380px;
              height: 48px;
              display: flex;
              align-items: center;
              background: #FFFFFF;
              box-shadow: 4px 4px 4px 0px rgba(204,204,204,0.2);
              border-radius: 5px;
              .l-icon {
                padding: 8px 10px;
                width: 110px;
                height: 38px;
                background-color: #2977FE;
                border-radius: 5px;
                margin-right: 20px;
                img {
                  width: 24px;
                  height: 24px;
                  display: inline-block;
                }
                span {
                  margin-left: 5px;
                  color: #FFFFFF;
                  display: inline-block;
                  text-align: justify;
                  text-align-last: justify;
                }
              }
              .r-txt {

              }
            }
          }
        }
        .right {
          /* background: url(../../assets/about/main-1-banner.png) no-repeat center center; */
          background-size: 90%;
          width: 628px;
          height: 520px;
          margin-left: 380px;
          background-size: contain;
        }
      }
    }
    .block-2 {
      max-width: 1080px;
      margin: auto;
      padding-top: 30px;
      padding-bottom: 50px;
      .group-box {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-top: 35px;
        .group {
          cursor: pointer;
          width: 250px;
          height: 172px;
          background-color: #FFFFFF;
          box-shadow: 4px 4px 4px 0px rgba(204,204,204,0.2);
          border-radius: 5px;
          .img {
            width: 50px;
            height: 50px;
            margin-top: 15px;
            margin-left: auto;
            margin-right: auto;
          }
          .img1 {
            background: url(~/assets/about/main-2-icon1.png) no-repeat top/cover;
          }
          .img2 {
            background: url(~/assets/about/main-2-icon2.png) no-repeat top/cover;
          }
          
          .img3 {
            background: url(~/assets/about/main-2-icon3.png) no-repeat top/cover;
          }
          
          .num {
            margin-top: 15px;
            color: #2977FE;
            font-size: 28px;
            font-weight: 500;
          }
          .txt {
            margin-top: 10px;
            font-size: 20px;
          }
        }
        .group:hover {
          color: #FFFFFF;
          background-color: #2977FE;
          .num {
            color: #FFFFFF;
          }
          .img1 {
            background: url(~/assets/about/main-2-icon1-hover.png) no-repeat top/cover;
          }
          .img2 {
            background: url(~/assets/about/main-2-icon2-hover.png) no-repeat top/cover;
          }
          .img3 {
            background: url(~/assets/about/main-2-icon3-hover.png) no-repeat top/cover;
          }
        }
      }
    }
    .block-3 {
      max-width: 1080px;
      margin: auto;
      padding-top: 70px;
      padding-bottom: 100px;
      .develop-tabs {
        margin-top: 44px;
        margin-left: 95px;
        /deep/.el-tabs {
          &__nav-wrap {

          }
          &__nav-wrap::after {
            display: none;
          }
          &__active-bar {
            display: none;
          }
          &__item {
            color: #666666;
            font-size: 20px;
            padding: 0 40px;
            .label {
              .label-big {

              }
            }
          }
          &__item:nth-child(2) {
            padding-left: 0px;
          }
          &__item:last-child {
            padding-right: 0px;
          }
          &__content {
            .content {
              margin-left: 180px;
              margin-top: 50px;
              display: flex;
              color: #333333;
              .left {
                font-size: 20px;
                margin-right: 30px;
              }
              .right {
                .step {
                  display: flex;
                  height: 70px;
                  font-size: 14px;
                  margin-bottom: 30px;
                  p {
                    text-indent: 0;
                    width: 420px;
                  }
                  .step-icon {
                    margin-right: 36px;
                    width: 16px;
                    height: 16px;
                    border: 3px solid #2977FE;
                    border-radius: 50%;
                    position: relative;
                    display: inline-block;
                  }
                  .step-icon::after {
                    content: "";
                    position: absolute;
                    left: 50%;
                    top: 100%;
                    transform: translateX(-50%);
                    width: 1px;
                    height: 50px;
                    background-color: #999999;
                  }
                }
                
              }
            }
          }
          .is-active {
            .label {
              color: #2977FE;
              font-size: 32px;
              .label-big {
                font-size: 42px;
              }
            }
          }
        }
      }
    }
    .block-4 {
      max-width: 1200px;
      margin: auto;
      .center {
        &-container {
          position: relative;
          width: 1200px;
          margin: 0 auto;
          /deep/ {
            .el-carousel__arrow--left {
              display: none;

              .el-icon {
                display: none;
              }
            }

            .el-carousel__arrow--right {
              display: none;

              .el-icon {
                display: none;
              }
            }
          }
        }
        &-list {
          height: 250px;
          width: 1200px;
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: left;
          .center-item {
            width: 20%;
            height: 250px;
            color: #fff;
            cursor: pointer;
            text-align: left;
            transition: width 0.5s;
            position: relative;
            z-index: 1;
            img {
              height: 250px;
              will-change: width;
              transform: translate3d(0, 0, 0);
              width: 100%;
              z-index: 10;
              object-fit: cover;
            }
            .img-title {
              position: absolute;
              left: 25px;
              bottom: 12px;
              span {
                display: block;
              }
            }
            .img-address {
              margin-top: 10px;
              padding-right: 15px;
            }
          }
          &.current-img {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            z-index: 10;
            width: 1200px;
            height: 300px;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: -1;
            .center-item {
              width: 25%;
              height: 300px;
              img {
                width: 100%;
                height: 300px;
                object-fit: cover;
              }
            }
            &.current-hover-img {
              width: 1200px;
              height: 600px;
              transition: all 0.3s ease;
              z-index: 10;
              .center-item {
                width: 100%;
                height: 600px;
                transition: all 0.3s ease;
                img {
                  width: 1200px;
                  height: 600px;
                  transition: all 0.3s ease;
                }
              }
            }
          }
        }
      }
    }
    .block-5 {
      padding-top: 70px;
      padding-bottom: 100px;
      .honor-list {
        max-width: 1200px;
        margin: auto;
        .honor-item {
          width: 1000px;
          height: 60px;
          margin: auto;
          margin-bottom: 40px;
          background-color: #F9FBFF;
          border-radius: 5px;
          display: flex;
          align-items: center;
          .honor-img {
            width: 90px;
            height: 100px;
            margin-top: -50px;
            display: inline-block;
          }
          .honor-title {
            flex-basis: 80px;
            color: #2977FE;
            margin-right: 8px;
            display: inline-block;
          }
          .honor-desc {
            flex: 1;
            text-align: left;
            font-size: 14px;
            margin-right: 14px;
            color: #666666;
            display: inline-block;
          }
        }
      }
    }
  }
  .title2 {
    font-size: 24px;
    margin-bottom: 40px;
    font-weight: 500;
    color: #333333;
  }
  .w-block {
    width: 100%;
    text-align: center;
    background: #ffffff;
  }

  .g-block {
    display: flex;
    align-items: center;
    width: 100%;
    height: 600px;
    text-align: center;
    background: url(~/assets/about/team-introduction.png) no-repeat top/cover;
  }
  .c-block {
    height: 830px;
    background: url(~/assets/about/eight-services.png) no-repeat top/cover;
  }
}  
.center-list:first-child .center-item:first-child {
  margin-left: 10%;
}
</style>
