<template>
    <div class="paas">
        <div class="paas__banner">
            <div class="wrap">
                <h1>
                    <span>AI录单助手pro</span>
                </h1>
                <h2>
                    <p>行业首个面向大众客户的AI智能产品</p>
                    <p>让AI解放你的双手，实现最快1秒下单，节省50％以上时间</p>
                </h2>
                <SubmitButton class="small-btn">限时免费体验</SubmitButton>
            </div>
        </div>
        <div class="business__case">
            <div class="business__case--items">
                <div class="text">
                    <div class="name">
                        录单功能从<span class="name_text">“工具”</span>到<span class="name_text">“智能助手”</span>的质变
                    </div>
                    <p>经过数月的技术深耕与客户试点，蔬东坡正式发布AI录单PRO版</p>
                    <p>完成了AI AGENT 技术对原有业务的重构升级，全面优化录单体验，助力生鲜供应链进入智能化新阶段。</p>
                    <div class="mb136">
                        <SubmitButton class="small-btn">免费试用名额预约</SubmitButton>
                    </div>
                </div>
                <img src="~/assets/aild/case_1.png" alt="" class="img" />
            </div>
            <div class="business__case--items">
                <div class="container">
                    <div class="big_title">让智能更懂你，让生鲜更高效</div>
                    <div class="small_title">五大核心亮点，生鲜配送从此迈向AI+时代</div>
                    <div class="bottom_container">
                        <div class="img_container" v-for="(item, index) in obj1" :key="index">
                            <div class="top">
                                <div class="circle">
                                    <img :src="item.imgUrl" :alt="item.title" class="img" />
                                    <div class="title_overlay">{{ item.title }}</div>
                                </div>
                            </div>
                            <div class="bottom">{{ item.content }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="business__case--items">
                <div class="container">
                    <div class="big_title">用蔬东坡，让工作都交给AI吧</div>
                    <div class="small_title">产品创新步履不停，创新行业升级未来，坚持以客户为中心</div>
                    <div class="small_title" style="margin-top: 15px;">用产品硬实力作为客户信赖的理由，让系统变得更全面、智能、好用！</div>
                    <div class="bottom_container2" v-for=" item in aiInfoList">
                        <aiorder class="show1" :infoObj="item"></aiorder>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import img0 from '~/assets/aild/case1.png'
import img1 from '~/assets/aild/case2.png'
import img2 from '~/assets/aild/case3.png'
import img3 from '~/assets/aild/case4.png'
import img4 from '~/assets/aild/case5.png'
import sceneImg1 from '~/assets/aild/scene_img1.png'
import sceneImg2 from '~/assets/aild/scene_img2.png'
import demoImg1  from '~/assets/aild/demo_img1.gif'
import aiorder from './aiorder.vue'
useHead({
    title: '生鲜配送ai录单_生鲜配送共享平台_蔬东坡ai定制-蔬东坡',
    meta: [
        {
            name: 'description',
            content: '蔬东坡业务定制平台,根据生鲜配送企业业务需要,为生鲜企业提供可自主定制化软件功能开发,帮助企业定义符合自身业务的管理流程,让业务更敏捷,护航企业高速发展。'
        },
        {
            name: 'keywords',
            content: '蔬东坡系统定制开发,生鲜配送软件定制,生鲜管理软件开发',
        },
    ],
});
const obj1 = [
    {
        title: '多场景解析',
        imgUrl: img0,
        content: '让AI看懂你的需求'
    },
    {
        title: '全链路智能',
        imgUrl: img1,
        content: '让AI更懂你的业务'
    },
    {
        title: '不做工具人',
        imgUrl: img2,
        content: '让AI解放你的双手'
    },
    {
        title: '快录快下班',
        imgUrl: img3,
        content: '让处理效率提升50％'
    },
    {
        title: '新奇软融入',
        imgUrl: img4,
        content: '适配原有操作习惯'
    },
]
const aiInfoList = [
    {
        header_text1: 'AI 代客下单',
        header_text2: '（已上线）',
        title1: '业务场景',
        title1_text: '当文员为顾客录入订单时，可将客户提供的聊天记录复制并转发给AI录单员（一键识别内容），以此方式高效完成订单信息的录入。',
        title2: '操作演示',
        title2_text: '在新增订单页面，点击【AI录单】唤起AI录单员，将客户的下单文字复制粘贴到AI聊天框，然后点击【AI识别】，稍后一会，客户的下单需求就完成了录入，待核对信息无误后，就可以提交议单啦!',
        sceneImages: [sceneImg1, sceneImg2],
        demoImages: [demoImg1]

    },
    {
        header_text1: 'AI 语音报单',
        header_text2: '（敬请期待）',
        title1: '业务场景',
        title1_text: '现场开单时，可以通过语音唤醒蔬东坡AI助手，直接语音报单;或者客户发过来纸质要货单，录单员可将客户要货单的内容口述，AI助手将自动识别语音内容。',
        title2: '使用说明',
        title2_text: '手机上启动蔬东坡 APP，在录单页面选择【AI录单】，唤起 AI录单助手，选择【语音识别】然后直接与 AI对话，即可完成订单录入!',
        sceneImages: [],
        demoImages: [],
    },
    {
        header_text1: 'AI数据分析师',
        header_text2: ' (敬请期待)',
        title1: '业务场景',
        title1_text: '随时随地想要调取经营数据时，可以直接唤醒蔬东坡AI数据分析助手，文字或语音询问:“帮我查一下本月的经营利润”，AI将自动调取本月数据并展示，数据分析就像和朋友聊天一样简单。',
        title2: '使用说明',
        title2_text: '启用蔬东坡 AI数据分析师，在聊天框中输入您想查询的数据指标，回车发送，AI数据分析师基于大模型和AIAGENT的企业数据分析决策，稍等一会，即可直接将数据直接反馈给您!',
        sceneImages: [],
        demoImages: [],
    }
]
</script>
<style scoped>
.paas {
    &__banner {
        background-image: url('~/assets/aild/banner.png');
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100%;
        height: 600px;

        .wrap {
            width: 1400px;
            margin: 0 auto;
            padding-left: 165px;
            padding-top: 205px;
        }

        h1 {
            display: flex;
            align-items: center;
            margin-bottom: 30px;

            span {
                &:first-child {
                    font-size: 56px;
                    font-family: PingFang SC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #2977FE;
                    line-height: 66px;
                }
            }
        }

        h2 {
            margin-bottom: 60px;

            p {
                font-size: 20px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 23px;
                margin-bottom: 15px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .business {
        &__case {
            &--items {
                display: flex;
                justify-content: center;

                &:nth-child(even) {
                    background: #F9FBFF;
                    /* flex-direction: row-reverse; */
                }

                &:nth-child(1) {
                    .img {
                        /* margin-right: -260px; */
                        margin-top: 80px;
                        padding-left: 20px;
                        width: 610px;
                        height: 330px;
                    }
                }

                &:nth-child(2) {
                    padding-top: 80px;
                    padding-bottom: 50px;
                }

                &:nth-child(3) {
                    padding-top: 80px;
                    padding-bottom: 50px;
                }

                .text {
                    padding-top: 100px;
                    max-width: 500px;

                    .name {
                        width: 458px;
                        /* padding-left: 34px; */
                        font-size: 24px;
                        font-family: HarmonyOS Sans SC;
                        font-weight: 600;
                        color: #333333;
                        line-height: 28px;
                        margin-bottom: 30px;
                        display: flex;
                        align-items: center;
                        img {
                            margin-bottom: 4px;
                        }

                        &_text {
                            color: #2977FE;
                        }
                    }

                    p {
                        width: 458px;
                        /* padding-left: 34px; */
                        font-size: 16px;
                        font-family: HarmonyOS_Sans_SC;
                        font-weight: 400;
                        color: #333333;
                        line-height: 19px;
                        margin-bottom: 30px;

                        &.noMb {
                            margin-bottom: 50px;
                        }
                    }

                    .mb136 {
                        margin-bottom: 136px;
                    }

                    .small-btn {
                        margin-top: 80px;
                        margin-left: 34px;
                    }
                }

                .img {
                    margin-top: 45px;
                }

                .container {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;

                    .big_title {
                        font-size: 36px;
                        font-family: PingFang SC-Medium, PingFang SC;
                        font-weight: 600;
                        color: #333333;
                        line-height: 42px;
                    }

                    .small_title {
                        font-size: 16px;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #333333;
                        line-height: 19px;
                        margin-top: 35px;
                    }

                    .bottom_container {
                        display: flex;
                        justify-content: space-around;
                        flex-wrap: wrap;
                        gap: 30px;
                    }

                    .bottom_container2 {
                        /* min-width: 1110px; */
                    }

                    .img_container {
                        width: 230px;
                        padding: 10px;
                        text-align: center;
                    }

                    .circle {
                        position: relative;
                        width: 200px;
                        height: 200px;
                        margin: 0 auto;
                    }

                    .img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    .title_overlay {
                        position: absolute;
                        width: 140px;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: white;
                        font-size: 23px;
                        font-weight: bold;
                        text-align: center;
                        padding: 5px 10px;
                        border-radius: 20px;
                    }

                    .bottom {
                        margin-top: 20px;
                        font-size: 16px;
                        color: #666;
                    }
                }
            }
        }
    }
}
</style>
