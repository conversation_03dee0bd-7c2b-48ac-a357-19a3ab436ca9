<template>
  <div>
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">新闻报道</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">News Coverage</h2>
        <p id="content"></p>
      </div>
    </div>
    <StudyCenterTabs activeName="/xueyuan/">
    </StudyCenterTabs>
    <NuxtPage></NuxtPage>
  </div>
</template>

<script setup>
import useAnchor from '../hooks/useAnchor';

useAnchor();
useHead({
  title: '',
  meta: [
    {
      name: 'description',
      content:
        '',
    },
    { name: 'keywords', content: '' },
  ],
  script: [
    {
      src:'https://hm.baidu.com/hm.js?37f9e8255a5fe1f567b5720f6cba2d2c',
      bodyClose: false,
      async: true,
      defer: true,
    }
  ]
})
</script>

<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);

.news_header {
  background: url('~/assets/xueyuan/common_bg.png') center/cover no-repeat;
  &__desc {
    margin-bottom: 105px;
  }
}
</style>