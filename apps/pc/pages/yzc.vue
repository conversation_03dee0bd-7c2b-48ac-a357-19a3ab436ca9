<template>
  <div>
    <solution-header title="预制菜解决方案">
      打造预制菜、卤制熟制食品加工全流程品质追溯体系
      <template #img>
        <img
          loading="lazy"
          class="gxst_header__img"
          src="~/assets/yzc/header_illustration.png"
          alt="预制菜解决方案"
        />
      </template>
    </solution-header>
    <div class="ds_content yzc_content">
      <div class="ds_content__intro w1065 text-center">
        <h3 class="ds_content__title">产品价值</h3>
        <ul class="flex flex-row justify-between">
          <li class="intro_item">
            <img class="inline-block" src="~/assets/delivery_sys/intro_icon2.png" alt="准时交货率提升40%" width="72" />
            <p class="intro_item__desc">准时交货率提升</p>
            <p class="intro_item__desc em">40%</p>
          </li>
          <li class="intro_item">
            <img
              class="inline-block"
              src="~/assets/delivery_sys/intro_icon1.png"
              alt="产品交货周期下降30%"
              width="86"
            />
            <p class="intro_item__desc">产品交货周期下降</p>
            <p class="intro_item__desc em">30%</p>
          </li>
          <li class="intro_item">
            <img
              class="inline-block"
              src="~/assets/delivery_sys/intro_icon2.png"
              alt="库存周转速度提升20%"
              width="72"
            />
            <p class="intro_item__desc">库存周转速度提升</p>
            <p class="intro_item__desc em">20%</p>
          </li>
          <li class="intro_item">
            <img class="inline-block" src="~/assets/delivery_sys/intro_icon2.png" alt="产品合格率提升15%" width="68" />
            <p class="intro_item__desc">产品合格率提升</p>
            <p class="intro_item__desc em">15%</p>
          </li>
        </ul>
      </div>
      <ul class="ds_content__main">
        <li class="sys_item" id="shangcheng">
          <div class="w1065 flex flex-row-reverse justify-end items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">预制菜商城</h3>
              <p class="item_desc">打造预制菜企业品牌商城</p>
              <p class="item_desc">
                支持快速下单、再来一单、分类下单
                <br />
                让经销商、餐厅下单更高效
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/yzc/sys_info1.png" alt="预制菜商城" width="667" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="bom">
          <div class="w1065 flex justify-end items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                BOM管理
              </h3>
              <p class="item_desc">
                产品关联原料用量，精准核算成本
                <br />
                精确记录每个产品的出成数量及出成率
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/yzc/sys_info2.png" alt="BOM管理" width="764" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="caigou">
          <div class="w1065 flex flex-row-reverse justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">采购</h3>
              <p class="item_desc">
                基于BOM进行MRP反算原料清单
                <br />
                采购收货智能称重，采购监管数据可依
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/yzc/sys_info3.png" alt="采购" width="743" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="guanli">
          <div class="w1065 flex justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;生产过程精细化管理
              </h3>
              <p class="item_desc">
                生产数据自动同步系统远程监管生产进度
                <br />
                移动式触屏操作，使用便捷
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/yzc/sys_info4.png" alt="生产过程精细化管理" width="741" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="fahuo">
          <div class="w1065 flex flex-row-reverse justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">发货配送</h3>
              <p class="item_desc">支持本地化及全国物流配送</p>
              <p class="item_desc">
                一键打印物流发货单
                <br />
                多仓库管理，支持订单分批配送
              </p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/yzc/sys_info5.png" alt="发货配送" width="813" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="caiwu">
          <div class="w1065 flex justify-end items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                财务
              </h3>
              <p class="item_desc">
                支持订单折扣、返利等操作。自动核算商品成本
              </p>
              <p class="item_desc">支持用友、金蝶等财务系统的无缝对接</p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/yzc/sys_info6.png" alt="财务" width="729" />
            </div>
          </div>
        </li>
        <li class="sys_item" id="daping">
          <div class="w1065 flex flex-row-reverse justify-between items-center">
            <div class="sys_item_detail">
              <h3 class="ds_content__title">数据大屏</h3>
              <p class="item_desc">预制菜大脑，工作进度实时掌控</p>
              <p class="item_desc">用数据管理企业订单、加工、分拣、配送</p>
              <p class="item_desc">业务全流程可视化管理</p>
            </div>
            <div class="flex-shrink-0">
              <img src="~/assets/yzc/sys_info7.png" alt="数据大屏" width="692" />
            </div>
          </div>
        </li>
      </ul>
      <!-- <div class="ds_content__apply w1065 text-center">
        <h3 class="ds_content__title">应用场景</h3>
        <p class="ds_content__desc">覆盖30W+生鲜配送企业业务场景</p>
        <ul class="flex justify-center">
          <li class="apply_item">
            <img
              class="apply_img apply_item__img"
              src="~/assets/yzc/apply_item3.jpg"
              alt="预制菜解决方案"
              width="427"
            />
            <p class="apply_desc apply_item__desc">预制菜解决方案</p>
          </li>
          <li class="apply_item">
            <img
              class="apply_img apply_item__img"
              src="~/assets/yzc/apply_item2.jpg"
              alt="食品加工解决方案"
              width="427"
            />
            <p class="apply_desc apply_item__desc">食品加工解决方案</p>
          </li>
        </ul>
      </div> -->
    </div>
    <!-- <footer class="ds_sidebar text-center">
      <h3 class="ds_sidebar__title">上万家客户信赖选择</h3>
      <CustomerScroll ></CustomerScroll>
    </footer> -->
  </div>
</template>

<script setup>
import useAnchor from '../hooks/useAnchor';
useAnchor();
useHead({
  title: '预制菜软件 预制菜系统 预制菜管理软件-蔬东坡',
  meta: [
    {
      name: 'description',
      content:
        '蔬东坡预制菜解决方案,反算原料清单,精准核算成本,采购监管数据可依据,支持全国物流配送,助力企业轻松管理,打造预制菜企业品牌。',
    },
    { name: 'keywords', content: '预制菜软件,预制菜系统,预制菜供应链,预制菜生产管理,预制菜管理软件' },
  ],
  script: [
    {
      src:'https://hm.baidu.com/hm.js?1110ccba6ac923e2d6cf7e589c0dd7e7',
      bodyClose: false,
      async: true,
      defer: true,
    }
  ]
});
onMounted(() => {
})
</script>

<style scoped lang="postcss">
@import url(~/assets/style/ds.css);

.ds_header {
  padding-top: 199px;
  background-image: url(~/assets/yzc/yzc_header_bg.jpg);

  .small-btn {
    margin-top: 60px;
  }

  &__title {
    font-size: 56px;
  }

  &__desc {
    margin-top: 14px;
    font-size: 32px;

    h3& {
      margin-top: 30px;
      font-size: 18px;
    }
  }
}

.ds_content {
  margin-top: 50px;
  &__intro {
    margin-bottom: 90px;
    .ds_content__title {
      margin-bottom: 70px;
      &::after {
        content: none;
      }
    }
    .intro_item {
      img.inline-block {
        width: 62px;
      }
      &:nth-child(2) img {
        width: 71px;
      }
    }
  }

  &__main {
    .sys_item {
      .flex-shrink-0 {
        min-width: 590px;
        img {
          margin-left: auto;
        }
      }
      &:nth-child(1) {
        background-size: 600px;
        background-position-x: 30%;
        img {
          width: 378px;
          margin: 0 auto;
        }
      }
      &:nth-child(2) {
        background-size: 750px;
        background-position: 73% -5%;
        img {
          width: 542px;
        }
      }

      &:nth-child(3) {
        background-position-x: 27%;
        img {
          width: 592px;
        }
      }

      &:nth-child(4) {
        background-size: 750px;
        background-position-x: 73%;
        img {
          width: 542px;
        }
        .sys_item_detail .ds_content__title::after {
          transform: translate(-16px);
        }
      }

      &:nth-child(5) {
        background-size: 700px;
        background-position: 30% -10%;
        img {
          width: 542px;
        }
      }

      &:nth-child(6) {
        background-size: 650px;
        background-position-y: 40%;
        img {
          width: 457px;
        }
        .sys_item_detail .ds_content__title::after {
          transform: translate(28px);
        }
      }

      &:nth-child(7) {
        background-position-x: 30%;
        img {
          width: 545px;
        }
      }

      &_detail {
        .ds_content__title {
          margin-left: 0;
          &::before {
            content: none;
          }
          &::after {
            content: '';
            display: block;
            width: 330px;
            height: 6px;
            margin: 10px 0 30px;
            background: url(~/assets/qsy/title_icon_left.png) no-repeat center/cover;
          }
        }
        .item_desc {
          margin-left: 23px;
        }
      }
      &:nth-child(2n) {
        .ds_content__title::after {
          background-image: url(~/assets/qsy/title_icon_right.png);
          transform: translate(-35px);
        }
      }
    }
  }

  &__core {
    padding: 100px 0 128px;
    background: url(~/assets/delivery_sys/core_bg.png) no-repeat top right/contain #f9fbff;

    .ds_content__title {
      margin-bottom: 84px;
      font-size: 36px;
    }

    .core_item {
      position: relative;
      width: 270px;
      padding: 100px 0 45px;
      background-color: #fff;
      border-radius: 5px;

      &__title {
        color: #333;
        font-size: 24px;
      }

      &__desc {
        margin-top: 33px;
        color: #333;
        font-size: 16px;
        line-height: 216.5%;
      }

      &:last-child .core_item__desc {
        margin-top: 20px;
      }

      &__img {
        position: absolute;
        top: -90px;
        left: 0;
        right: 0;
        margin: auto;
      }
    }
  }
  &__apply {
    padding-bottom: 110px;
    .apply_item {
      &:nth-child(n + 2) {
        margin-left: 40px;
      }
      &__img {
        width: 250px;
      }
      &__desc {
        margin-top: -50px;
        color: #fff;
        font-size: 16px;
      }
    }
  }
}
</style>
