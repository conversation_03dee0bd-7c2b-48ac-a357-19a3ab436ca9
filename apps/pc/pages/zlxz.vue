<!--
 * @Description: 行业会议页面
 * @Date: 2023-06-07 15:51:57
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-07-07 17:01:20
 * @FilePath: /shudongpo-website/apps/pc/pages/zlxz.vue
-->
<template>
  <div class="hymt">
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">资料下载</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">超多实用模板，即下即用</h2>
      </div>
    </div>
    <StudyCenterTabs>
      <div class="g-block">
        <div class="down_box">
            <div
              class="article"
              v-in-view-animate="['fly-up']"
              v-for="(item, index) in dataList"
              :key="index"
            ><div class="down_title" @click="jumpDetail(item?.src,item.post_title)">
              <!-- <p class="img_title">{{ item.post_title }}</p> -->
              <img src="~/assets/xueyuan/zlxz_illustration.png" alt="资料下载" >
            </div>
              <div class="txt-box">
                <p class="title text-two-line" @click="jumpDetail(item?.src,item.post_title)">
                  {{ item.post_title }}
                </p>
                <div class="downbox">
                  <p class="article__desc">{{ item.post_date_format }}</p>
                  <div class="down_click" @click="jumpDetail(item?.src,item.post_title)">点击下载</div>
                </div>
              </div>
            </div>
        </div>
      </div>
    </StudyCenterTabs>
      <div class="modal__mask" v-show="showModal">
    <div class="modal__main">
      <div class="modal__header">
        <img class="logoimg" src="~/assets/common/logo.png" alt="logo" />
        <span class="divider">|</span>
        <span>生鲜SaaS ERP</span>
        <img @click="handleCloseModal" class="modal__close"
          src="https://website-image.sdongpo.com/website/icon--close.png" alt="关闭" />
      </div>
      <div class="modal__content form-modal">
        <TheForm ref="form" @on-change="handleChange"></TheForm>
      </div>
      <div class="modal__btn" @click="handleSaveFormData">点击提交</div>
       <div class="modal__tips">请填写相关信息下载文件</div>
    </div>
  </div>
  </div>
</template>
<script setup>
useHead({
  title: '蔬东坡-资料下载',
  meta: [
    { name: 'description', content: '蔬东坡资料下载页面包含生鲜配送行业管理方面内容和资料，致力于为生鲜人提供一个学习和成长的平台。' },
    { name: 'keywords', content: '	资料下载，蔬东坡' },
  ],
});

  let showModal = ref(false);
  let url = ref(null);
  let fileName = ref(null);
  let formData = {};
  const form = ref(null);
  const handleChange = data => {
    formData = data;
  };
  const handleCloseModal = () => {
    form.value.resetForm();
    showModal.value = false
  };
  const handleSaveFormData = async () => {
    const validate = form.value.validate();
    if (!validate.valid) {
      showError(validate.error);
      return;
    }
    currentModalType.value = ModalType.datum;
    const { data } = await saveFormData(formData);
    const res = JSON.parse(data.value);
    // showSuccess(res.message);
    if (res.status === 'success') {
      window.open(url.value)
      handleCloseModal()
    }
  };
let dataList = ref([]);
const getArticleList = async () => {
  const res = await getStudyCenterMedia('资料下载')
  if (res.status) {
    dataList.value = res.data || []
  }
};
getArticleList();
const jumpDetail = (link,name) => {
  if(!link) return
  currentModalType.value = ModalType.datum
  showModal.value = true
  fileName.value = name
  url.value = link
}
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);

.down_box{
  width: 1000px;
  display: flex;
  flex-wrap:wrap;
  margin: auto;
  align-content:flex-start;
}
.article{
  margin: 10px 15px 20px 15px;
border-radius: 3px 3px 3px 3px;
box-shadow: 1px 1px 1px 1px rgba(28,102,231,0.1);
}
.down_title{
  height: 195px;
  width: 300px;
  cursor: pointer;
  position: relative;
  background: #2977fe;
  .img_title{
    position: absolute;
    top:50%;
    left:53%;
    width:100px;
    text-align:center;
    transform: translate(-50%,-50%);
    font-size: 12px;
    color:#333333;
  }
  img{
    width: 181px;
    height: 162px;
    margin: 0 auto;
    padding-top: 23px;
  }
}
.downbox{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}
.down_click{
background: #EAF1FF;
color: #2977FE;
width: 80px;
padding: 5px 0;
display: flex;
align-items: center;
justify-content: center;
border-radius: 3px 3px 3px 3px;
cursor: pointer;
float:right;
font-size: 12px;
}
.txt-box{
  padding: 30px 13px 25px 18px;
  width: 300px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .title {
    font: bolder 18px PingFang SC-Medium, PingFang SC;
  }
}
.hymt {
  color: #333333;
  .news_header {
    background: url(../assets/xueyuan/common_bg.png) no-repeat center center;
    background-size: 100% 100%;
    &__desc {
      font-size: 28px;
    }
  }
  .g-block {
    background-color: #f9fbff;
    padding-top: 50px;
    padding-bottom: 50px;
  }
  .w {
    width: 960px;
    margin: 0 auto;
  }
  .w1 {
    width: 1100px;
    margin: 0 auto;
  }
  .title2 {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 10px;
    text-align: center;
  }
  .desc2 {
    text-align: center;
    font-size: 16px;
    color: #666666;
  }
}
  .modal__mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 9999;
  }

  .modal__main {
    padding: 30px;
    background-color: #fff;
    font-size: 16px;
    display: inline-block;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .modal__header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #047eef;
    margin-bottom: 27px;
    margin-top: 30px;
    position: relative;
    .logoimg{
      width:140px;
    }
  }

  .divider {
    margin: 0 10px;
  }

  .modal__header img {
    width: 75px;
  }

  .modal__header .modal__close {
    position: absolute;
    width: 28px;
    top: -40px;
    right: -10px;
    cursor: pointer;
  }

  /deep/ {
    .form-item {
      display: flex;
      flex-direction: row;
      margin-bottom: 24px;
      vertical-align: middle;
    }

    .form-item__required {
      color: #ff3d15;
    }

    .form-item__label-tips {
      font-size: 12px;
      color: #b2b2b2;
    }

    .form-item__label {
      text-align: right;
      padding-top: 10px;
      width: 72px;
    }

    .form-item__content {
      padding-left: 16px;
      max-width: 360px;
    }

    .employee-num__item {
      width: 152px;
      height: 50px;
      line-height: 50px;
      border: 1px solid #cecece;
      border-radius: 6px;
      text-align: center;
      margin-right: 20px;
      margin-bottom: 14px;
      display: inline-block;
      cursor: pointer;
    }

    .employee-num__item.active {
      color: #fff;
      background-color: #ff3d15;
      border: 1px solid #ff3d15;
    }

    .form-item__input {
      border: 1px solid #cecece;
      border-radius: 6px;
      outline: 0;
      width: 330px;
      height: 50px;
      line-height: 50px;
      padding: 14px;
    }
  }

  .modal__btn {
    text-align: center;
    height: 50px;
    line-height: 50px;
    background: linear-gradient(90deg, #ff7214, #ff1616);
    border-radius: 6px;
    font-size: 20px;
    color: #fff;
    cursor: pointer;
    margin-top: -10px;
  }

  .modal__tips {
    color: #666;
    margin-top: 30px;
    width: 100%;
    text-align: center;
    font-size: 16px;
  }
</style>
