<template>
    <div>
        <solution-header title="信息化推动食材阳光采购高效运营" class="solution_header">
            实现校园食材采购高效运营、阳光透明、公平公正
            <template #img>
                <img loading="lazy" class="gxst_header__img" src="~/assets/yyd/header_illustration.png"
                    alt="食材阳光采购平台产品解决方案" />
            </template>
        </solution-header>
        <div class="ygsc_content">
            <div class="ygsc_content__pain text-center">
                <h3 class="ygsc_content__title">校园食材阳光采购运营方面临的痛点</h3>
                <ul class="flex flex-row justify-between w">
                    <li class="pain_item">
                        <img class="inline-block pain_item__img" src="~/assets/yysxt/pain_icon1.png" alt="食材仓储、运输 损耗成本高"
                            width="132" />
                        <h5 class="pain_item__title">食材价格波动  <br />应对难</h5>
                    </li>
                    <li class="pain_item">
                        <img class="inline-block pain_item__img" src="~/assets/yysxt/pain_icon2.png" alt="食材配送 时效性要求高"
                            width="132" />
                        <h5 class="pain_item__title">供应商筛选 <br />管理难</h5>
                    </li>
                    <li class="pain_item">
                        <img class="inline-block pain_item__img" src="~/assets/yysxt/pain_icon3.png"
                            alt="食材质量控制 难度大、食材追溯难" width="132" />
                        <h5 class="pain_item__title">采购监管难 <br />价格不透明</h5>
                    </li>
                    <li class="pain_item">
                        <img class="inline-block pain_item__img" src="~/assets/yysxt/pain_icon4.png" alt="与学校及供应商 沟通难度大"
                            width="132" />
                        <h5 class="pain_item__title">财务对账 <br />效率低易出错</h5>
                    </li>
                </ul>
            </div>
            <div class="ygsc_content__process">
                <div class="ygsc_content__process__title">蔬东坡校园食材阳光采购平台解决方案</div>
                <div class="ygsc_content__process__title">信息化构建清晰的平台业务流程</div>
                <img class="process_illustration" src="~/assets/yyd/process_illustration.png" alt="校园食材阳光采购平台业务流程示意图"
                    width="1204" />
            </div>
            <div class="ygsc_content__process">
                <div class="ygsc_content__process__title">构建公平公正的供应商准入机制，遴选优质供应商</div>
                <div class="ygsc_content__process__title">通过信用评价体系的汇总结果清退不达标的用户</div>
                <img class="process_illustration" src="~/assets/yyd/process_illustration2.png" alt="校园食材阳光采购平台业务流程示意图"
                    width="1204" />
            </div>
            <ul class="ygsc_content__main">
                <li class="sys_item">
                    <div class="w flex justify-between items-center">
                        <div class="sys_item_detail">
                            <p class="title_after__text">供应商维护商品价格</p>
                            <p class="item_desc">支持维护自身投标范围商品，对商品批 <br />量上下架、编辑商品价格、上传商品图 <br />片，随时随地快速完成商品编辑</p>
                            <p class="item_desc">支持EEXCEL进行导入维护商品价格</p>
                        </div>
                        <div class="flex-shrink-0">
                            <img src="~/assets/yysxt/sys_info1.png" alt="校园食材阳光采购平台运营后台" width="760" />
                        </div>
                    </div>
                </li>
                <li class="sys_item">
                    <div class="w flex flex-row-reverse justify-between items-center">
                        <div class="sys_item_detail">
                            <p class="title_after__text">供应商订单处理</p>
                            <p class="item_desc">将订单按供应商分类，并自动下发到各供应商的后台</p>
                            <p class="item_desc">供应商支持查看详细的要货清单，并打印发货单</p>
                            <p class="item_desc">支持对订单分拣、实际分拣量自动更新订单发货数量</p>
                            <p class="item_desc">分拣完成后，供应商支持对订单一键发货，更新订单业务状态</p>
                        </div>
                        <div class="flex-shrink-0">
                            <img src="~/assets/yysxt/sys_info2.png" alt="学校食堂端运营后台" width="633" />
                        </div>
                    </div>
                </li>
                <li class="sys_item">
                    <div class="w flex justify-between items-center">
                        <div class="sys_item_detail">
                            <p class="title_after__text">食材溯源信息快捷填报</p>
                            <p class="item_desc">
                                系统支持供应商上传送货单、农残检测单、供应商单据等
                            </p>
                            <p class="item_desc">
                                溯源数据同步上传到食安监管平台
                            </p>
                        </div>
                        <div class="flex-shrink-0">
                            <img src="~/assets/yysxt/sys_info3.png" alt="学生家长端后台" width="642" />
                        </div>
                    </div>
                </li>
                <li class="sys_item">
                    <div class="w flex flex-row-reverse justify-between items-center">
                        <div class="sys_item_detail">
                            <p class="title_after__text">供应商便捷订单处理</p>
                            <p class="item_desc">系统支持移动端分拣+打单，效率更高</p>
                            <p class="item_desc">支持供应商代分拣，移动端操作，简单快捷</p>
                            <p class="item_desc">支持供应商发货单打印，批量打印，快速高效</p>
                            <p class="item_desc">支持溯源报告:把控食品安全，分拣标签+发货单可任意配置</p>
                        </div>
                        <div class="flex-shrink-0">
                            <img src="~/assets/yysxt/sys_info4.png" alt="物流配送 轻松排线" width="636" />
                        </div>
                    </div>
                </li>
                <li class="sys_item">
                    <div class="w flex justify-between items-center">
                        <div class="sys_item_detail">
                            <p class="title_after__text">阳光配送，透明监管</p>
                            <p class="item_desc">支持车辆实时定位监控，确保食品安全</p>
                            <p class="item_desc">支持可视化地图排线，提升配送效率</p>
                        </div>
                        <div class="flex-shrink-0">
                            <img src="~/assets/yysxt/sys_info5.png" alt="生鲜溯源 食安保障" width="748" />
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <footer class="ygsc_sidebar text-center">
            <h3 class="ygsc_sidebar__title">选择蔬东坡，食安管理更放心</h3>
            <div class="w">
                <div class="ygsc_sidebar__tab flex justify-center">
                    <!--          <div class="tab_item" :class="{ active: activeTab === 'mjd' }" @mouseenter="changeTab('mjd')">上海麦金地</div>-->
                    <a class="tab_item" :class="{ active: activeTab === 'zyhu' }" href="javascript:;" @mouseenter="changeTab('zyhu')">
                        遵义市红花岗区校园阳光采购平台
                    </a>
                    <a class="tab_item" :class="{ active: activeTab === 'shcm' }" href="javascript:;" @mouseenter="changeTab('shcm')">
                        上海市崇明县教育局阳光采购平台
                    </a>
                    <a class="tab_item" :class="{ active: activeTab === 'zzgc' }" href="javascript:;" @mouseenter="changeTab('zzgc')">
                        郑州管城区教育局阳光采购平台
                    </a>
                    <a class="tab_item" :class="{ active: activeTab === 'lxxh' }" href="javascript:;" @mouseenter="changeTab('lxxh')">
                        临沂市沂河新区智慧食安平台
                    </a>
                </div>
                <div class="ygsc_sidebar__content">
                    <a v-show="activeTab === 'zyhu'" href="javascript:;"
                        class="content_item flex justify-around items-start">
                        <div class="text_container">
                            <p class="content_item__desc">
                                遵义市红花岗区教体局为了解决区里中小学食材统一采购问题，搭建了遵义市首个针对122家学校的服务平台。
                            </p>
                            <ul class="item_text_container">
                                <li class="item_text">通过智能化管理工具解决了各学校订单汇总问题，供应商直供，实现对商品流、<br />
                                    &nbsp;&nbsp;&nbsp;物流的全面管理</li>
                                <li class="item_text">学校可对每个单品进行采购成本分析、数量汇总，并输出报表</li>
                                <li class="item_text">财务上明确了解每个食堂的商品数量以及金额、每个供应商配送商品数量及金 <br />
                                    &nbsp;&nbsp;&nbsp;额，效率提升至少60%，2-3人即可轻松实现后勤食材保障</li>
                                <li class="item_text">教体局随时查看食堂采购价格、数量，查看供应商的服务评分</li>
                            </ul>
                        </div>
                        <img src="~/assets/ygsc/company_icon1.png" alt="遵义市红花岗区校园阳光采购平台" width="327"/>
                    </a>
                    <a v-show="activeTab === 'shcm'" href="javascript:;"
                        class="content_item flex justify-around items-start">
                        <div class="text_container">
                            <p class="content_item__desc">
                                上海市崇明县教育局是上海市崇明县人民政府主管全县教育工作的职能部门，解决区里100多所中小学统一学校下单问题，教育局监管订单和采购，减少规避出错，通过软件加强管理管控。
                            </p>
                            <ul class="item_text_container">
                                <li class="item_text">教育局统一制定菜谱，响应教育部要求的食堂选餐制</li>
                                <li class="item_text">对供应商、食堂行为进行统一管控，平台通过线上询价、竞价，增强学校管控力</li>
                                <li class="item_text">方便供应商自行管理商品，直接生成账单，与档口/商户直接对账</li>
                                <li class="item_text">食安溯源，所有食材的溯源信息都有据可查，保障食品安全</li>
                            </ul>
                        </div>
                        <img src="~/assets/ygsc/company_icon2.png" alt="上海市崇明县教育局阳光采购平台" width="236"/>
                    </a>
                    <a v-show="activeTab === 'zzgc'" href="javascript:;"
                        class="content_item flex justify-around items-start">
                        <div class="text_container">
                            <p class="content_item__desc">
                                郑州管城区有64所中小学校，所有学校的食材采购统一在教育平台上下单，通过平台对全地区学校食堂进行统一管理，建立供应商的询价、竞价、定价机智，降低采购成本，提高食材安全性性。
                            </p>
                            <ul class="item_text_container">
                                <li class="item_text">建立教育局（学校）食材采购商城，食材采购直接在商城下单，大大减少下单时
                                    <br>&nbsp;&nbsp;&nbsp;间，提高学校食材采购工作效率、减少人工、避免差错率
                                </li>
                                <li class="item_text">
                                    对供应商实现竞价比价，并可对所有食材产品、供应商的价格进行横向、纵向对<br>&nbsp;&nbsp;&nbsp;比，给学校食材采购提供数据参考
                                </li>
                                <li class="item_text">所有进入学校的食材实现溯源管理，实现商品的溯源功能</li>
                                <li class="item_text">供应商分拣配送，供应商配送的数据与学校收货、验货数据实现实时对接</li>
                            </ul>
                        </div>
                        <img src="~/assets/ygsc/company_icon3.png" alt="郑州管城区教育局阳光采购平台" width="327"/>
                    </a>
                    <a v-show="activeTab === 'lxxh'" href="javascript:;"
                        class="content_item flex justify-around items-start">
                        <div class="text_container">
                            <p class="content_item__desc">
                                临沂市沂河新区为了提升农副产品流通效率河增强食品安全防空能力，由临沂食安搭建智慧食安平台，通过平台实现统一的食材下单平台、供应链效率提升、食安溯源。
                            </p>
                            <ul class="item_text_container">
                                <li class="item_text">实现公开透明的采购模式，学校、企事业单位等可以自主的选择供应商下单</li>
                                <li class="item_text">
                                    供应商通过系统分拣配送以及上传检测报告，极大提升供应商的配送效率，目前<br>&nbsp;&nbsp;&nbsp;有60多家供应商入驻平台</li>
                                <li class="item_text">相关监管部门可以通过平台查看食品交易数据、价格指数、溯源信息等</li>
                            </ul>
                        </div>
                        <img src="~/assets/ygsc/company_icon4.png" alt="临沂市沂河新区智慧食安平台" width="236"/>
                    </a>
                </div>
                <div></div>
            </div>
        </footer>
    </div>
</template>
<script setup>
useHead({
    title: "食材阳光采购平台产品解决方案_食材配送系统_单位食堂配送解决方案_蔬菜配送-蔬东坡",
    meta: [
        {
            name: "description",
            content: "保障食品安全，降低经营风险，颠覆传统配送模式，减少中间环节损耗让企事业单位食堂配送更高效",
        },
        {
            name: "keywords",
            content: "食材配送系统,企事业单位食堂食材采购,团膳配送系统,团膳食堂ERP,企事业单位食堂解决方案",
        },
    ],
});
const activeTab = ref("zyhu");
const changeTab = tab => {
    activeTab.value = tab;
};
</script>
<style scoped>
:deep .solution_header .gxst_header__text {
    width: 580px;
}

.ygsc_header {
    height: 620px;
    padding-top: 194px;
    background: url(~/assets/qsy/qsy_header_bg.png) no-repeat bottom/cover;
    color: #fff;

    .small-btn {
        width: 200px;
        margin: 50px 0 0;
        color: #2977fe;
        background-color: #fff;
    }

    &__title {
        font: bolder 36px PingFang SC;
        letter-spacing: 4px;
    }

    &__desc {
        margin-top: 60px;
        font-size: 20px;
        font-weight: lighter;
    }

    &__img {
        width: 420px;
        transform: translateX(48px);
    }
}

.ygsc_content {
    margin-top: 50px;

    &__title {
        margin-bottom: 94px;
        color: #333;
        font: 24px PingFang SC;
    }

    &__pain {
        margin-bottom: 100px;

        .pain_item {
            position: relative;
            width: 250px;
            min-height: 260px;
            padding: 96px 0 22px;
            background: linear-gradient(313.8deg, #eef5ff 3.23%, #f9fafd 98.67%);
            box-shadow: 2px 4px 4px rgba(59, 105, 220, 0.05);
            border-radius: 5px;

            &__img {
                position: absolute;
                top: 35px;
                left: 0;
                right: 0;
                margin: auto;
                width: 130px;
            }

            &__title {
                position: absolute;
                top: 153px;
                left: 0;
                right: 0;
                margin-bottom: 15px;
                color: #333333;
                font: 20px PingFang SC;
                font-weight: 800;
            }

            &__desc {
                color: #333;
                font-size: 14px;
                line-height: 216.5%;
            }
        }
    }

    &__process {
        background-color: #f5f8ff;

        &__title {
            margin: 0 auto 0 auto;
            padding-top: 60px;
            color: #333;
            font: 26px PingFang SC;
            text-align: center;
        }

        .process_illustration {
            width: 1034px;
            margin: 0 auto;
            padding: 100px 0;
        }
    }

    &__explain {
        font-family: HarmonyOS Sans SC;

        .title1 {
            width: 350px;
            font-size: 20px;
            font-weight: 500;
            color: #333333;
            margin: 50px auto 20px auto;

            padding-top: 20px;
        }

        .title2 {
            width: 850px;
            font-size: 28px;
            font-weight: 800;
            color: #323433;
            margin: 3px auto;
            text-align: center;
        }

        .explain_text {
            width: 1034px;
            font-size: 16px;
            color: #333333;
            margin: 20px auto 70px auto;
            text-align: left;
        }

        .explain_img {
            width: 1034px;
            margin: 3px auto;
        }

        .explain_strong_text {
            color: #3E74F6;
            font-weight: 600;
        }
    }

    &__main {
        .ds_content__title {
            display: inline-block;
            color: #3E74F6;
            font-size: 24px;
            font-weight: bold;
        }

        .title_after__text {
            color: #3E74F6;
            font-size: 24px;
            font-weight: bold;
            text-align: left;
            margin: 10px 0;

            &::after {
                content: "";
                display: block;
                width: 330px;
                height: 6px;
                margin: 10px 0 30px;
                background: url(~/assets/ygsc/title_icon_left.png) no-repeat center/cover;
            }
        }

        .item_desc {
            position: relative;
            margin: 0 0 30px 20px;
            color: #333;
            font-size: 16px;

            &::before {
                content: "";
                position: absolute;
                top: 4px;
                left: -22px;
                display: inline-block;
                width: 15px;
                height: 15px;
                background: url(~/assets/ygsc/item_icon.png) no-repeat center/cover;
            }
        }

        .w {
            width: 1020px;
        }

        .sys_item {
            padding: 70px 0 76px;

            &:nth-child(2n) {
                background: #f9fbff;
            }

            &:nth-child(2n + 1) {
                .sys_item_detail {
                    transform: translateX(100px);
                }
            }

            &:nth-child(2n) .title_after__text::after {
                background-image: url(~/assets/ygsc/title_icon_left.png);
            }

            &:nth-child(1) {
                .ds_content__title::after {
                    transform: translateX(-82px);
                }

                img {
                    width: 560px;
                }
            }

            &:nth-child(2) {
                img {
                    width: 497px;
                }
            }

            &:nth-child(3) {
                .ds_content__title::after {
                    transform: translateX(-26px);
                }

                img {
                    width: 462px;
                }
            }

            &:nth-child(4) {
                img {
                    width: 527px;
                }
            }

            &:nth-child(5) {
                .ds_content__title::after {
                    transform: translateX(-42px);
                }

                img {
                    width: 544px;
                }
            }
        }
    }
}

.ygsc_sidebar {
    height: 33vw;
    color: #fff;
    background: url(~/assets/ygsc/ygsc_sidebar_bg.png) no-repeat top/cover;

    &__title {
        padding: 75px 0 40px;
        font: bolder 24px PingFang SC;
    }

    .w {
        width: 1150px;
    }

    &__tab {
        .tab_item {
            width: 290px;
            height: 40px;
            line-height: 40px;
            font-size: 18px;
            margin-right: 5px;

            &:not(.active) {
                background: rgba(255, 255, 255, 0.2);
            }
        }

        .active {
            background-color: #2173ff;
        }
    }

    &__content {
        .content_item {
            margin-top: 64px;

            .text_container {
                text-align: left;
                width: 570px;
            }
            .content_item__desc {
                font-size: 16px;
                font-weight: 600;
            }

            .item_text_container {
                margin-top: 20px;
            }

            .item_text {
                font-size: 16px;
                position: relative;
                line-height: 38px;
            }

            .item_text::before {
                content: "\200B";
                display: inline-block;
                width: 7px;
                line-height: 7px;
                background-color: #ffffff;
                border-radius: 50%;
                position: absolute;
                left: -1vw;
                top: 0.65vw;
            }

            /* &:nth-child(n + 2) {
                margin-top: 64px;
            } */

            &__desc {
                width: 550px;
                font-size: 16px;
            }

            &:nth-child(1) img {
                width: 400px;
            }

            &:nth-child(2) img {
                width: 400px;
            }

            &:nth-child(3) img {
                width: 400px;
            }

            &:nth-child(4) img {
                width: 400px;
            }
        }
    }
}
</style>