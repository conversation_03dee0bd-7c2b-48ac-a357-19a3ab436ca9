<template>
  <div>
    <solution-header title="高校后勤供应链管理解决方案">
      助力高校整合供应链，规范供应商、档口行为，
      <br />
      保障食品安全
      <template #img>
        <img
          loading="lazy"
          class="gxst_header__img"
          src="~/assets/gxst/header-right.png"
          alt="高校后勤供应链管理解决方案"
        />
      </template>
    </solution-header>
    <div class="gxst_content">
      <div class="gxst_content__pain text-center">
        <h3 v-in-view-animate="['fly-up']" class="gxst_content__title">高校后勤供应链管理行业痛点</h3>
        <div class="gxst_content__pain_content">
          <div v-in-view-animate="['fly-up']" class="pain_item">
            <img loading="lazy" class="pain_item__img" src="~/assets/gxst/icon1.png" alt="人工拆单易出错" width="132" />
            <div class="pain_item__title">人工拆单易出错</div>
          </div>
          <div v-in-view-animate="['fly-up']" class="pain_item">
            <img loading="lazy" class="pain_item__img" src="~/assets/gxst/icon2.png" alt="食品安全难保障" width="132" />
            <div class="pain_item__title">食品安全难保障</div>
          </div>
          <div v-in-view-animate="['fly-up']" class="pain_item">
            <img loading="lazy" class="pain_item__img" src="~/assets/gxst/icon3.png" alt="数据分析难" width="132" />
            <div class="pain_item__title">食堂库存管理难</div>
          </div>
          <div v-in-view-animate="['fly-up']" class="pain_item">
            <img loading="lazy" class="pain_item__img" src="~/assets/gxst/icon4.png" alt="食品安全难保障" width="132" />
            <div class="pain_item__title">手工对账效率低</div>
          </div>
          <div v-in-view-animate="['fly-up']" class="pain_item">
            <img loading="lazy" class="pain_item__img" src="~/assets/gxst/icon5.png" alt="数据分析难" width="132" />
            <div class="pain_item__title">档口利润不清晰</div>
          </div>
        </div>
      </div>
      <div v-in-view-animate="['fly-up']" class="gxst_content__process">
        <img loading="lazy" class="process_illustration" src="~/assets/gxst/flow-bg.png" alt="高校食堂供应链管理流程图" />
      </div>
      <ul class="gxst_content__main">
        <li class="sys_item">
          <div v-in-view-animate="['fly-up']" class="sys_item_box">
            <div class="sys_item_detail mr30">
              <h3 class="ds_content__title">
                商城自动接单、自动拆单
                <br />
                效率提升100%
              </h3>
              <p class="item_desc">避免手工接单，接单效率提升100%</p>
              <p class="item_desc">系统根据供应商自动拆单，使拆单错误率降为0</p>
            </div>
            <div v-in-view-animate="['fly-up']" class="sys_item_img">
              <img
                loading="lazy"
                src="~/assets/gxst/order-bg.png"
                alt="商城自动接单、自动拆单 效率提升100%"
                width="434"
              />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="sys_item_box">
            <div v-in-view-animate="['fly-up']" class="sys_item_detail mr30">
              <h3 class="ds_content__title">单据实时可追溯，数据0丢失</h3>
              <p class="item_desc">各种单据、数据实时保存，避免纸质单据的丢失</p>
              <p class="item_desc">
                监管机构可随时根据单据对食品源头进行监控
                <br />
                一旦出现问题，可立即控制流通
              </p>
            </div>
            <div v-in-view-animate="['fly-up']" class="sys_item_img">
              <img loading="lazy" src="~/assets/gxst/bill-bg.png" alt="单据实时可追溯，数据0丢失" width="447" />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="sys_item_box">
            <div v-in-view-animate="['fly-up']" class="sys_item_detail mr30">
              <h3 class="ds_content__title">
                档口的进销存管理
                <br />
                可使货品丢失降低10%
              </h3>
              <p class="item_desc">每个档口的货品数量的进和出都有据可查</p>
              <p class="item_desc">对货品可进行有效监管，避免人为导致的货品丢失</p>
            </div>
            <div v-in-view-animate="['fly-up']" class="sys_item_img">
              <img
                loading="lazy"
                src="~/assets/gxst/stock-bg.png"
                alt="档口的进销存管理 可使货品丢失降低10%"
                width="422"
              />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="sys_item_box">
            <div v-in-view-animate="['fly-up']" class="sys_item_detail mr30">
              <h3 class="ds_content__title">
                系统自动对账
                <br />
                可节省1-2个财务人员成本
              </h3>
              <p class="item_desc">供应商发货后，系统就已经自动完成了对账</p>
              <p class="item_desc">
                无需安排1-2个人工去根据纸质单据去核对账目
                <br />
                既费时又容易出错
              </p>
            </div>
            <div v-in-view-animate="['fly-up']" class="sys_item_img">
              <img loading="lazy" src="~/assets/gxst/accountChecking-bg.png" alt="系统自动对账" width="519" />
            </div>
          </div>
        </li>
        <li class="sys_item">
          <div class="sys_item_box">
            <div v-in-view-animate="['fly-up']" class="sys_item_detail mr30">
              <h3 class="ds_content__title">
                多维度报表
                <br />
                档口利润当天出
              </h3>
              <p class="item_desc">实时对档口利润进行测算，以保证档口正常经营</p>
              <p class="item_desc">根据实时利润情况，从而给予政策性补贴的支持</p>
            </div>
            <div v-in-view-animate="['fly-up']" class="sys_item_img">
              <img loading="lazy" src="~/assets/gxst/report-bg.png" alt="多维度报表" width="508" />
            </div>
          </div>
        </li>
      </ul>
    </div>
    <footer class="sidebar text-center">
      <h3 v-in-view-animate="['fly-up']" class="sidebar__title">标杆案例</h3>
      <div class="w">
        <div v-in-view-animate="['fly-up']" class="sidebar__tab flex justify-center">
          <div class="tab_item" :class="{ active: activeTab === 0 }" @mouseenter="changeTab(0)">欧亚学院</div>
          <div class="tab_item" :class="{ active: activeTab === 1 }" @mouseenter="changeTab(1)">昆明学院</div>
        </div>
        <div v-in-view-animate="['fly-up']" class="sidebar__content">
          <a href="https://www.sdongpo.com/xueyuan/c-40092.html#content" target="_blank" v-show="activeTab === 0" class="content_item flex justify-around items-center">
            <p class="content_item__desc">
              西安欧亚学院是一所多学科协调发展的国际化应用型普通本科高校、中国
              大陆地区办学水平和办学层次最高的财经类民办大学之一。校内100多档
              口+餐饮街，每日所需食材量数十吨，包含上千品类。与蔬东坡合作后
              学校对供应商、档口/商户行为进行统一管控；供应商自行管理商品，直接
              生成账单，与档口/商户直接对账；食安溯源，保障食品安全
            </p>
            <img loading="lazy" src="~/assets/gxst/school-icon1.png" alt="欧亚学院" width="436" />
          </a>
          <div v-show="activeTab === 1" class="content_item flex justify-around items-center">
            <p class="content_item__desc">
              昆明学院地处中国（云南）自贸试验区昆明片区（昆明市经开区），校内
              食堂+其余商户，合计1000多档口需管控。与蔬东坡合作后，实现业财数
              据一体化，帮助学校实现全流程监管，增强学校食安管控力；供应商可与
              档口直接对账，学校后勤直接查询账单数据，为三方省时省力；小程序商
              城内直接显示各商品的基准价与上浮率，方便档口/商户比价，方便学校 统一汇总记录。
            </p>
            <img loading="lazy" src="~/assets/gxst/school-icon2.png" alt="昆明学院" width="210" />
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import useInViewAnimate from '../hooks/inViewAnimate';
export default {
  setup() {
    useHead({
      title: '高校食堂供应链管理解决方案_生鲜供应链管理_蔬菜配送-蔬东坡',
      meta: [
        { name: 'description', content: '助力高效整合供应链规范供应商、档口行为，保障食品安全' },
        { name: 'keywords', content: '生鲜供应链,高校食堂供应链管理,生鲜供应链管理系统,高校食堂供应链管理解决方案' },
      ],
    });
    const activeTab = ref(0);
    const changeTab = tab => {
      activeTab.value = tab;
    };
    return {
      activeTab,
      changeTab,
    };
  },
  directives: {
    'in-view-animate': {
      mounted: useInViewAnimate,
    },
  },
};
</script>

<style lang="postcss" scoped>
.gxst_header {
  height: 620px;
  padding-top: 195px;
  background: url(~/assets/qsy/qsy_header_bg.png) no-repeat top/cover;
  color: #fff;

  &__box {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__text {
    width: 480px;
  }

  .small-btn {
    width: 200px;
    margin: 78px 0 0;
    color: #2977fe;
    background-color: #fff;
  }

  &__title {
    font-size: 36px;
    font-weight: 600;
  }

  &__desc {
    margin-top: 60px;
    font-weight: 400;
    font-size: 20px;
    font-weight: lighter;
  }

  &__img {
    margin-left: 70px;
    width: 420px;
    height: 320px;
  }
}

.gxst_content {
  margin-top: 75px;

  &__title {
    margin-bottom: 50px;
    color: #333;
    font-weight: 400;
    font-size: 24px;
  }

  &__pain {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 110px;

    &_content {
      width: 1200px;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: center;

      .pain_item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 100px;
        margin-bottom: 50px;
        width: 220px;
        background: linear-gradient(313.8deg, #eef5ff 3.23%, #f9fafd 98.67%);
        box-shadow: 2px 4px 4px rgba(59, 105, 220, 0.05);
        border-radius: 5px;

        &__img {
          width: 95px;
          height: 112px;
          margin-top: 35px;
          mix-blend-mode: 40px;
        }

        &__title {
          color: #333333;
          font-size: 20px;
          margin-top: 40px;
          margin-bottom: 35px;
        }
      }
    }
  }

  &__process {
    background-color: #f9fbff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 90px;
    padding-bottom: 90px;

    .process_illustration {
      width: 1048px;
      height: 493px;
    }
  }

  &__main {
    color: #333333;

    .ds_content__title {
      display: inline-block;
      font-size: 24px;

      &::after {
        content: '';
        display: block;
        width: 330px;
        height: 6px;
        margin: 10px 0 30px;
        background: url(~/assets/gxst/lineRight-icon.png) no-repeat center/cover;
      }
    }

    .item_desc {
      color: #666666;
      position: relative;
      margin: 0 0 30px 30px;
      font-size: 16px;

      &::before {
        content: '';
        position: absolute;
        top: 5px;
        left: -20px;
        display: inline-block;
        width: 15px;
        height: 15px;
        background: url(~/assets/qsy/item_icon.png) no-repeat center/cover;
      }
    }

    .sys_item {
      padding: 130px 0 150px;

      &_box {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &_img {
        width: 530px;
      }

      &:nth-child(2n) {
        background: #f9fbff;
      }

      &:nth-child(2n + 1) .ds_content__title::after {
        transform: translateX(-31px);
      }

      &:nth-child(2n) .ds_content__title::after {
        background-image: url(~/assets/qsy/title_icon_left.png);
      }

      &:nth-child(4) {
        padding-top: 159px;
      }
    }
  }
}

.sidebar {
  height: 600px;

  color: #ffffff;
  background: url(../assets/gxst/gao.jpg) no-repeat top/cover;

  &__title {
    padding: 95px 0 60px;
    font-weight: 500;
    font-size: 24px;
  }

  &__tab {
    .tab_item {
      cursor: pointer;
      width: 400px;
      height: 50px;
      line-height: 50px;
      font-size: 20px;

      &:not(.active) {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    .active {
      background-color: #2173ff;
    }
  }

  &__content {
    .content_item {
      margin-top: 64px;

      &__desc {
        width: 550px;
        line-height: 22px;
        font-size: 16px;
      }
    }
  }
}

.ml30 {
  margin-left: 30px;
}

.mr30 {
  margin-right: 30px;
}

@keyframes fly-up {
  0% {
    transform: translateY(100px);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.fly-up {
  animation-name: fly-up;
  animation-fill-mode: both;
  animation-duration: 0.5s;
}

@keyframes scale-large {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.scale-large {
  animation-name: scale-large;
  animation-fill-mode: both;
}
</style>
