<template>
  <div class="yckt">
    <div class="news_header">
      <div class="news_header__box">
        <h1 class="news_header__title">央厨课堂</h1>
        <div class="news_header__line"></div>
        <h2 class="news_header__desc">Central Kitchen Classroom</h2>
      </div>
    </div>
    <StudyCenterTabs>
    <div class="g-block">
      <div class="search-box">
        <div class="search-line">
          <span class="label">业务：</span>
          <div class="category-list">
            <div
              class="name"
              :class="{
                'is-active': item === curC2
              }"
              v-for="(item, index) in category2" :key="index"
              @click="selectC2(item)"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </div>
      <ArticleList
        :list="showList"
        :keyMap="keyMap"
      />
    </div>
    </StudyCenterTabs>
  </div>
</template>
<script setup>
useHead({
  title: '蔬东坡-央厨课堂',
  meta: [
    { name: 'description', content: '蔬东坡央厨课堂涵盖订单管理、菜谱管理、生产计划、库房管理等中央厨房生产流程内容。' },
    { name: 'keywords', content: '蔬东坡，央厨课堂，中央厨房' },
  ],
});
let category2 = ref([])
const allList = ref([])
let showList = ref([])
let curC2 = ref('查看全部')

const keyMap = ref({
  title: 'post_title',
  img: 'conver_image',
  date: 'post_date_format',
  tag: 'business_config_central_kitchen'
})

const getArticleList = async () => {
  let res = await getStudyCenterPages('央厨课堂')
  if(res.status) {
    console.log('res', res)
    allList.value = res.data.list || []
    showList.value = res.data.list || []
    console.log('allList', allList)
    let c2 = res.data.business_config_central_kitchen || []
    // allList.value.forEach(item => {
    //   console.log('item', item)
    //   if(Array.isArray(item.business_config) && item.business_config.length > 0) {
    //     console.log('business_config', item.business_config)
    //     c2 = [
    //       ...item.business_config,
    //       ...c2
    //     ]
    //   } 
    // })
    c2.unshift('查看全部')
    category2.value = Array.from(new Set(c2))
    console.log('category2', category2)
  }
}
getArticleList()

const getShowArticleList = () => {
  showList.value = allList.value
  const c2 = curC2.value
  if(c2 !== '查看全部') {
    showList.value = showList.value.filter(item => {
      return (item.business_config_central_kitchen || []).findIndex((business) => c2 === business) !== -1
    })
  }
  console.log('showList-value', showList.value)

}
const selectC2 = (c2) => {
  curC2.value = c2
  getShowArticleList()
}
</script>
<style lang="postcss" scoped>
@import url(~/assets/style/xueyuan.css);
@import url(~/assets/style/yckt.css);
</style>