<style scoped lang="postcss">
.shian {
  .section__label {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 36px;
    color: #2977FE;
    line-height: 42px;
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding-top: 50px;
    padding-bottom: 60px;
    position: relative;
    &:after {
      content: '';
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      bottom: 40px;
      width: 140px;
      height: 0px;
      border-radius: 0px 0px 0px 0px;
      border: 3px solid;
      border-image: linear-gradient(90deg, rgba(41, 119, 254, 0), rgba(41, 119, 254, 1), rgba(41, 119, 254, 0.04)) 6 6;
    }
  }
  &__header {
    height: 600px;
    background: url(~/assets/shian/shian_19.png) no-repeat top/cover;
    padding-top: 135px;
    &--box {
      width: 1200px;
      margin: 0 auto;
      .content {
        width: 900px;
        height: 335px;
        background: rgba(41, 36, 36, .4);
        border-radius: 0px 0px 0px 0px;
        padding-left: 60px;
        padding-top: 43px;
        h1 {
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 48px;
          color: #FFFFFF;
          line-height: 70px;
          text-shadow: 0px 4px 4px rgba(0,0,0,0.25);
          margin-bottom: 22px;
        }
        .desc {
          padding-right: 50px;
          text-align: right;
          font-family: PingFang SC, PingFang SC;
          font-weight: 500;
          font-size: 28px;
          color: #FFFFFF;
          line-height: 33px;
          text-shadow: 0px 4px 4px rgba(0,0,0,0.25);
        }
        /deep/ .btn  {
          margin-top: 15px;
        }
      }
    }
  }
  &__feature {
    display: flex;
    justify-content: center;
    padding-top: 30px;
    padding-bottom: 30px;
    .items {
      margin-right: 50px;
      &:last-child {
        margin-right: 0;
      }
      img {
        width: 200px;
        height: 184px;
        margin:0 auto;
      }
      p {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 24px;
        color: #000000;
        line-height: 28px;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }
  /*&__background {
    padding-bottom: 100px;
    background: #F9FBFF;
    &--content {
      width: 900px;
      margin: 0 auto;
      position: relative;
      &:before {
        content: '';
        border-left: 2px dashed #fff;
        background: linear-gradient(to bottom, rgba(41, 119, 254, 1), rgba(181, 208, 255, 1));
        background-origin: border-box;
        height: 100%;
        position: absolute;
        left: -26.5px;
        top: 0;
      }
      .items {
        margin-bottom: 20px;
        height: 120px;
        background: linear-gradient( 91deg, rgba(74,141,255,0.76) 0%, rgba(137,181,254,0.8) 100%);
        border-radius: 10px 10px 10px 10px;
        display: flex;
        align-items: center;
        padding-top: 10px;
        padding-bottom: 10px;
        padding-left: 26px;
        padding-right: 14px;
        position: relative;
        &:last-child {
          margin-bottom: 0;
        }
        &:before {
          content: '';
          border-radius: 50%;
          position: absolute;
          left: -34px;
          width: 16px;
          height: 16px;
          background: #2977FE;
        }
        img {
          margin-left: auto;
          width: 266px;
          height: 100px;
        }
        p {
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 18px;
          color: #FFFFFF;
          line-height: 24px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          margin-right: 8px;
          &.title {
            font-size: 20px;
            font-weight: 500;
            margin-bottom: 10px;
          }
        }
      }
    }
  }*/
  &__question {
    background: #F9FBFF;
    padding-bottom: 100px;
    &--content {
      display: flex;
      justify-content: center;
      .items {
        border-radius: 8px;
        width: 270px;
        overflow: hidden;
        display: inline-block;
        margin-right: 30px;
        &:last-child {
          margin-right: 0;
        }
        img {
          display: block;
          margin-bottom: -50px;
          width: 270px;
          height: 200px;
        }
        p {
          position: relative;
          z-index: 2;
          text-align: center;
          height: 50px;
          line-height: 50px;
          background: linear-gradient( 90deg, #4F8FFF 0%, #85B2FF 100%);
          font-family: PingFang SC, PingFang SC;
          font-weight: 400;
          font-size: 22px;
          color: #FFFFFF;
          text-align: center;
          border-radius: 0 0 8px 8px;
        }
      }
    }
  }
  &__production, &__operate {
    padding-bottom: 100px;
    &--content {
      .category {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
        .items {
          padding: 20px;
          display: flex;
          align-items: center;
          margin-right: 40px;
          position: relative;
          &:last-child {
            margin-right: 0;
          }
          img {
            width: 40px;
            height: 40px;
            margin-right: 5px;
          }
          &.active {
            background: rgba(228,238,255,0.8);
            border-radius: 5px 5px 5px 5px;
            border: 1px dashed #2977FE;
            &:after {
              border-color: #2977FE;
            }
            &:before {
              background: #2977FE;
            }
          }
          &:after {
            content: '';
            width: 20px;
            height: 20px;
            border: 1px solid #CCCCCC;
            border-radius: 50%;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -52px;
          }
          &:before {
            content: '';
            width: 12px;
            height: 12px;
            background: #CCCCCC;
            border-radius: 50%;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: -48px;
          }
          span {
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 28px;
            color: #2977FE;
            line-height: 33px;
          }
        }
      }
      .split {
        width: 1600px;
        height: 3px;
        background: #e6e6e6;
        margin: 0 auto 64px;
      }
      .content {
        .items {
          display: block;
          margin: 0 auto;
          width: 1200px;
        }
      }
    }
  }
  &__operate {
    background: #F9FBFF;
  }
  &__advance {
    background: #F9FBFF;
    padding-bottom: 75px;
    &--content {
      .honor-list {
        max-width: 1200px;
        margin: auto;
        .honor-item {
          width: 1000px;
          height: 60px;
          margin: auto;
          margin-bottom: 40px;
          background-color: #fff;
          border-radius: 5px;
          display: flex;
          align-items: center;
          .honor-img {
            width: 90px;
            height: 100px;
            margin-top: -50px;
            display: inline-block;
          }
          .honor-title {
            flex-basis: 80px;
            color: #2977FE;
            margin-right: 8px;
            display: inline-block;
          }
          .honor-desc {
            flex: 1;
            text-align: left;
            font-size: 14px;
            margin-right: 14px;
            color: #666666;
            display: inline-block;
          }
        }
      }
    }
  }
  &__case {
    background: #fff;
    padding-bottom: 100px;
    &--content {
      .el-carousel__container {}
      width: 1200px;
      margin: 0 auto;
      background: url(../assets/shian/shian_58.png);
      background-size: 1000px;
      background-position: center;
      background-repeat: no-repeat;
      /deep/ {
        .el-carousel__arrow--left {
          background: url(../assets/index/carousel-arrow-left.png) no-repeat center/contain;
          background-size: 23px;

          .el-icon {
            display: none;
          }
        }

        .el-carousel__arrow--right {
          background: url(../assets/index/carousel-arrow-right.png) no-repeat center/contain;
          background-size: 23px;

          .el-icon {
            display: none;
          }
        }
      }
      .items {
        width: 1000px;
        height: 660px;
        margin:0 auto;
        padding: 65px 60px;
        border-radius: 20px 20px 20px 20px;
        display: flex;
        &.first {
          padding-top: 90px;
          padding-left: 90px;
          .right {
            img {
              margin-bottom: 50px;
            }
          }
        }
        .left {
          margin-right: 40px;
          width: 350px;
          height: 530px;
        }
        .right {
          .label {
            font-family: PingFang SC, PingFang SC;
            font-weight: 500;
            font-size: 24px;
            color: #2977FE;
            line-height: 28px;
            margin-bottom: 10px;
          }
          .value {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #fff;
            line-height: 24px;
            margin-bottom: 30px;
          }
        }
      }
    }
  }
}
</style>

<template>
  <div class="shian">
    <header class="shian__header">
      <div class="shian__header--box">
        <div class="content">
          <h1 class="title">学校食堂食品安全管控<br/>信息化平台解决方案</h1>
          <div class="desc">为师生就餐添加一道“安全防护墙”</div>
          <SubmitButton class="button small-btn">专属方案咨询</SubmitButton>
        </div>
      </div>
    </header>
    <div class="shian__feature">
      <div class="items">
        <img src="../assets/shian/shian_13.png" alt="专注校园食安10年" width="150" height="138" />
        <p>专注校园食安<br/>10年</p>
      </div>
      <div class="items">
        <img src="../assets/shian/shian_14.png" alt="100+国家级认证资质" width="150" height="138" />
        <p>100+国家级<br/>认证资质</p>
      </div>
      <!-- <div class="items">
        <img src="../assets/shian/shian_15.png" alt="超过1000万行源代码积累" width="150" height="138" />
        <p>超过1000万行源代码积累</p>
      </div> -->
      <div class="items">
        <img src="../assets/shian/shian_16.png" alt="首个食材供应链数字化平台" width="150" height="138" />
        <p>首个食材供应链<br/>数字化平台</p>
      </div>
      <div class="items">
        <img src="../assets/shian/shian_17.png" alt="全国8大服务中心3小时内响应" width="150" height="138" />
        <p>全国8大服务中心<br/>3小时内响应</p>
      </div>
    </div>
    <!-- <div class="shian__background">
      <div class="shian__background--label section__label">行业背景</div>
      <div class="shian__background--content">
        <div class="items">
          <p>2020年四部委联合印发《校园食品安全守护行动方案》联合开展校园食品安全守护行动，致力于保护学生食品安全。</p>
          <img src="../assets/shian/shian_24.png" alt="" width="266" height="100" />
        </div>
        <div class="items">
          <p>2021年相关部门下发《关于加强学校食堂卫生安全与营养健康管理工作的通知》，要求部属各高等学校、部省合建各高等学校利用互联网等手段实现“明厨亮灶”，制止餐饮浪费。</p>
          <img src="../assets/shian/shian_20.png" alt="" width="266" height="100" />
        </div>
        <div class="items">
          <p>2022年四部委下发《关于做好2022年秋季学校食品安全工作的通知》，要求压紧压实主体责任，持续推进校外供餐单位和学校食堂“互联网+明厨亮灶”等智慧管理模式提质扩面。</p>
          <img src="../assets/shian/shian_21.png" alt="" width="266" height="100" />
        </div>
        <div class="items">
          <p>2023年四部委联合下发《关于做好2023年春季学校食品安全工作的通知》，要求“全心守护，压实落细“两个责任””全力以赴，制止餐饮浪费行为”全民参与，提升智慧监管效能”。</p>
          <img src="../assets/shian/shian_22.png" alt="" width="266" height="100" />
        </div>
        <div class="items">
          <div>
            <p class="title">持续加强学校食安监管，成为常态</p>
            <p>2024年，为期半年的校园食品安全专项活动在全国开始执行。</p>
          </div>
          <img src="../assets/shian/shian_23.png" alt="" width="266" height="100" />
        </div>
      </div>
    </div> -->
    <div class="shian__question">
      <div class="shian__question--label section__label">传统食堂问题多</div>
      <div class="shian__question--content">
        <div class="items">
          <img src="../assets/shian/shian_30.png" alt="卫生监管难" width="300" height="220" />
          <p>卫生监管难</p>
        </div>
        <div class="items">
          <img src="../assets/shian/shian_25.png" alt="用餐估算难、浪费多" width="300" height="220" />
          <p>用餐估算难、浪费多</p>
        </div>
        <!-- <div class="items">
          <img src="../assets/shian/shian_26.png" alt="后勤管理缺培训、人工贵" width="300" height="220" />
          <p>后勤管理缺培训、人工贵</p>
        </div>
        <div class="items">
          <img src="../assets/shian/shian_27.png" alt="就餐高峰排队久、效率低" width="300" height="220" />
          <p>就餐高峰排队久、效率低</p>
        </div> -->
        <div class="items">
          <img src="../assets/shian/shian_28.png" alt="食材溯源难" width="300" height="220" />
          <p>食材溯源难</p>
        </div>
        <div class="items">
          <img src="../assets/shian/shian_29.png" alt="数据统计难、对账难" width="300" height="220" />
          <p>数据统计难、对账难</p>
        </div>
      </div>
    </div>
    <div class="shian__production">
      <div class="shian__production--label section__label">食品生产安全监管解决方案</div>
      <div class="shian__production--content">
        <div class="category">
          <div
            class="items"
            :class="{ active: currentProductionIndex == index }"
            v-for="(item, index) in production"
            :key="index"
            @click="handleSwitchActive(index)">
              <img :src="item.icon" />
              <span>{{ item.label }}</span>
            </div>
        </div>
        <div class="split"></div>
        <div class="content">
          <template v-for="(item, index) in production">
            <img class="items" :key="index" :src="item.value" v-if="currentProductionIndex == index" />
          </template>
        </div>
      </div>
    </div>
    <div class="shian__operate">
      <div class="shian__operate--label section__label">食材供应链运营安全管理解决方案</div>
      <div class="shian__operate--content">
        <div class="category">
          <div
            class="items"
            :class="{ active: currentOperateIndex == index }"
            v-for="(item, index) in operate"
            :key="index"
            @click="handleSwitchOperateActive(index)">
              <img :src="item.icon" />
              <span>{{ item.label }}</span>
            </div>
        </div>
        <div class="split"></div>
        <div class="content">
          <template v-for="(item, index) in operate">
            <img class="items" :key="index" :src="item.value" v-if="currentOperateIndex == index" />
          </template>
        </div>
      </div>
    </div>
    <div class="shian__case">
      <div class="shian__case--label section__label">蔬东坡落地案例</div>
      <div class="shian__case--content">
        <el-carousel
          height="34.8vw"
          ref="carouselRef"
          indicator-position="none"
          :autoplay="true"
          arrow="always"
        >
          <el-carousel-item v-for="(item, index) in cases" :key="item.index">
            <div class="items" :class="{first: index == 1}">
              <img class="left" :src="item.img" width="350" height="530" v-if="index != 1" />
              <div class="right">
                <img :src="item.img" width="85" height="62" v-if="index == 1" />
                <template v-for="(child) in item.text">
                  <div class="label">{{ child.label }}</div>
                  <div class="value" v-html="child.value"></div>
                </template>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
    <div class="shian__advance">
      <div class="shian__advance--label section__label">蔬东坡优势</div>
      <div class="shian__advance--content">
        <div class="honor-list">
          <div class="honor-item">
            <img class="honor-img" src="../assets/about/honor-1.png" alt="">
            <span class="honor-title">
              行业荣誉
            </span>
            <ScrollingText
              class="honor-desc"
              text="中国农业互联网先锋企,中国未来农业100强,中国产业互联网100强,中国团餐供应链优秀服务商,中国农产品供应链建设优秀单位,中国团餐信息化服务商top30,中国产业互联网推荐服务商,SaaS软件最佳服务商"
            />
          </div>
          <div class="honor-item">
            <ScrollingText
              style="margin-right: 0px;margin-left: 14px;"
              class="honor-desc"
              text="100+专利及软著,国家高新技术企业,北京市专精特新中小企业,ISO9001研发质量管理体系认证,国家三级等保认证,微信支付最佳合作服务商"
              direction="right"
            />
            <span class="honor-title" style="margin-right: 0px; text-align: right;margin-left: 8px;">
              技术认证
            </span>
            <img class="honor-img" src="../assets/about/honor-2.png" alt="">
          </div>
        </div>
        <HonorScroll style="margin-top: 70px;"/>
      </div>
    </div>
  </div>
</template>

<script setup>
import production_1 from '../assets/shian/shian_51.png';
import production_2 from '../assets/shian/shian_41.png';
import production_3 from '../assets/shian/shian_42.png';
import production_4 from '../assets/shian/shian_43.png';
import production_5 from '../assets/shian/shian_44.png';
import production_6 from '../assets/shian/shian_45.png';

import production_icon_1 from '../assets/shian/shian_61.png';
import production_icon_2 from '../assets/shian/shian_38.png';
import production_icon_3 from '../assets/shian/shian_55.png';
import production_icon_4 from '../assets/shian/shian_57.png';
import production_icon_5 from '../assets/shian/shian_56.png';
import production_icon_6 from '../assets/shian/shian_60.png';

import operate_1 from '../assets/shian/shian_52.png';
import operate_2 from '../assets/shian/shian_50.png';
import operate_3 from '../assets/shian/shian_49.png';
import operate_4 from '../assets/shian/shian_48.png';
import operate_5 from '../assets/shian/shian_47.png';
import operate_6 from '../assets/shian/shian_46.png';

import operate_icon_1 from '../assets/shian/shian_54.png';
import operate_icon_2 from '../assets/shian/shian_40.png';
import operate_icon_3 from '../assets/shian/shian_53.png';
import operate_icon_4 from '../assets/shian/shian_39.png';
import operate_icon_5 from '../assets/shian/shian_36.png';
import operate_icon_6 from '../assets/shian/shian_37.png';

import case_0 from '../assets/shian/shian_35.png';
import case_1 from '../assets/shian/shian_18.png';
import case_2 from '../assets/shian/shian_32.png';
import case_3 from '../assets/shian/shian_31.png';
import case_4 from '../assets/shian/shian_34.png';
import case_5 from '../assets/shian/shian_33.png';

useHead({
  title: '蔬东坡——学校食堂食品安全管控信息化平台解决方案',
  meta: [
    {
      name: 'description',
      content: '蔬东坡——学校食堂食品安全管控信息化平台解决方案,专注校园安全10年,广泛应用于企事业单位食堂、校园食堂、团餐公司等场景,是国内首个食材供应链数字化平台。',
    },
    { name: 'keywords', content: '食品安全监管系统, 食品安全监管软件, 食品溯源, 智慧食安系统, 食安监管系统, 学校食堂监管系统, 明厨亮灶系统' },
  ],
  script: [
    {
      src:'https://hm.baidu.com/hm.js?37f9e8255a5fe1f567b5720f6cba2d2c',
      bodyClose: false,
      async: true,
      defer: true,
    }
  ]
})

const production = [
  {
    label: '溯源安全',
    icon: production_icon_1,
    value: production_1,
  },
  {
    label: '仓储安全',
    icon: production_icon_2,
    value: production_2
  },
  {
    label: '加工安全',
    icon: production_icon_3,
    value: production_3
  },
  {
    label: '生产安全',
    icon: production_icon_4,
    value: production_4
  },
  {
    label: '人资安全',
    icon: production_icon_5,
    value: production_5
  },
  {
    label: '智能预警',
    icon: production_icon_6,
    value: production_6
  }
]

const operate = [
  {
    label: '食材采购加工流程',
    icon: operate_icon_1,
    value: operate_1,
  },
  {
    label: '智慧食堂架构',
    icon: operate_icon_1,
    value: operate_2
  },
  {
    label: '供应商管理',
    icon: operate_icon_1,
    value: operate_3
  },
  {
    label: '订单管理',
    icon: operate_icon_1,
    value: operate_4
  },
  {
    label: '采购管理',
    icon: operate_icon_1,
    value: operate_5
  },
  {
    label: '供应商分拣配送',
    icon: operate_icon_1,
    value: operate_6
  }
]

const cases = [
  {
    img: case_0,
    text: [
      {
        label: '广东拔萃教育集团',
        value: '广东拔萃教育集团是一家新兴的民营企业，历经近二十年发展，确立了以教育为主体，服务、金融为两翼的发展格局。集团持股 10 余个实体学校，涵盖幼儿园、小学、初中、高中和职业教育，在校学生超 20000 人。'
      },
      {
        label: '应用价值',
        value: '为了解决2万多师生的用餐便利与安全问题，拔萃教育集团与蔬东坡取得了全面合作。通过蔬东坡校园食安系统，解决了集团内部食堂食品安全、家长学生菜单下单、食材分拣效率提升、供应商管理等问题，实现了食堂系统的数字化管理，对各个校园的食堂运转带来以食材安全采购、供应、溯源为核心的全面提质提效。'
      }
    ]
  },
  {
    img: case_1,
    text: [
      {
        label: '建始县教育局',
        value: '本科层次的普通高等学校，入选云南省首批六所“应用型人才培养示范院校”、云南省十所“地方高校转型发展试点改革学校”、云南省首批两所“国门大学”基础能力建设高校。'
      },
      {
        label: '应用价值',
        value: '建始县共计131个学校，有近万教师及中小学生，蔬东坡食安管理系统将全面赋能学校实现菜谱下单、食材供应商管理、食材供应商分拣及供货管理、生鲜食品溯源以及客户退货自动退回供应商管理。<br/>通过蔬东坡系统的全面接入，学校老师可以在pc端直接按照食谱下单，采购单自动汇集给供应商和采购员； <br/>食堂可以查看各类食材的历史价格，并且同时对多家供应商管理，降本增效； 供应商也可以通过系统端实现分拣提速，提升食材交付效率； <br/>同时，最重要的蔬东坡溯源系统，可以实现一品一码的追踪管理； <br/>有退货退款情况时，通过订单系统也可以直接完成商品的退货结账，真正为全县学校、食材供应商带来便捷的数字化管理服务，让师生的健康餐食有数可依。'
      }
    ]
  },
  {
    img: case_2,
    text: [
      {
        label: '欧亚学院',
        value: '西安欧亚学院是一所多学科协调发展的国际化应用型普通本科高校、中国大陆地区办学水平和办学层次最高的财经类民办大学之一，目前师生22000多人。校内100多档口+餐饮街，每日所需食材量数十吨，包含上千种品类，金额高达几十万。'
      },
      {
        label: '系统功能',
        value: '对供应商、档口/商户行为进行统一管控，增强学校管控力； 方便供应商自行管理商品，直接生成账单，与档口/商户直接对账； 食安溯源，所有食材的溯源信息都有据可查，保障食品安全。'
      },
      {
        label: '应用价值',
        value: '学校从2018年就已经开始实现了后勤食材统一采购，但当时手段缺失，大量工作需要人为完成，不但没有产生效益，反倒增加了经营成本。 2022年7月与蔬东坡合作后，使用蔬东坡高校后勤食材供应链管理系统，运营人员从12人降为2人，通过信息化系统，学校实现了针对供应商的统一服务管理，在为供应商供货提供极大便利的同时，也为学校创造了上百万的管理效益。'
      }
    ]
  },
  {
    img: case_3,
    text: [
      {
        label: '西科大高新学院',
        value: '西安科技大学高新学院是经教育部批准，由西安科技大学与陕西西科美芯科技有限责任公司合作举办的全日制普通本科独立学院，学校占地1000余亩，总建筑面积约32.45万平方米；在校生11000余人。学校里还开办了一所职业技术学校，一所职业高中，等于三所学校共用一个后勤集团。'
      },
      {
        label: '系统功能',
        value: '通过智能化管理工具解决了各档口订单汇总问题，供应商直供档口，实现对商品流、物流的全面管理； 学校可对每个单品进行采购成本分析、数量汇总，并输出报表； 财务上明确了解每个档口的商品数量以及金额、每个供应商配送商品数量及金额，效率提升至少60%，2-3人即可轻松实现后勤食材保障。'
      },
      {
        label: '应用价值',
        value: '因工作过于繁琐，该校供应商重不堪负，直接推荐了蔬东坡，建议学校使用我们的统采 方案，并愿意为系统买单。2022年10月学校与我司开始了合作。现在已经实现了承包档口 经营者食材采购线上下单付款。自营餐厅采购直接通过线上报价优选供应商的方式实现了日 日招标。校方，档口经营者，供应商三方均达到预期，项目进展顺利，目前后勤的管理人 员工作量下降只用了1个老师，带了两名勤工俭学的学生每日抽2小时就完成整个采购流程。'
      }
    ]
  },
  {
    img: case_4,
    text: [
      {
        label: '昆明学院',
        value: '昆明学院，位于云南省昆明市，是经教育部批准建立的全日制普通高等学校，入选首批“三全育人”综合改革试点高校、云南省应用型整体转型试点高校、云南省应用型人才培养示范院校，为云南省应用型高校联盟理事长单位，全国新建本科院校联盟、全国地方院校教师教育联盟、云南省高等学校教师教育联盟成员单位。'
      },
      {
        label: '系统功能',
        value: '供应商管理，对学校源头进行监管，供应商负责分拣、配送； 食堂档口多渠道下单，提升效率，减少人为出错的情况； 财务上明确了解每个档口的成本以及利润、每个供应商配送商品数量及金额，账目一目了然。'
      },
      {
        label: '应用价值',
        value: '由于全部人为操作，导致工作即繁琐又容易出错，该校供应商重不堪负，在2022年学校与我司开始了合作。现在已经实现了食堂档口食材采购直接线上下单，供应商直接在系统接单，并进行分拣配送。校方对食堂经营成本、品类使用、溯源情况等实时可查。'
      }
    ]
  },
  {
    img: case_5,
    text: [
      {
        label: '保山学院',
        value: '本科层次的普通高等学校，入选云南省首批六所“应用型人才培养示范院校”、云南省十所“地方高校转型发展试点改革学校”、云南省首批两所“国门大学”基础能力建设高校。'
      },
      {
        label: '系统功能',
        value: '食堂档口多渠道自助下单，减少下单出错情况； 系统自动汇总采购需求，自动匹配给对应的供应商，节省了时间； 财务自动对账，结算及时方便；食堂档口成本利润清晰'
      },
      {
        label: '应用价值',
        value: '全流程规范化，自动化，减少人力的投入。各食堂通过小程序下单、自动汇总订单、一键生成采购单、分拣、发货出库 各环节责任明确。后台自动生成营业统计报表、分析报表、财务统计报表。'
      }
    ]
  }
]

const currentProductionIndex = ref(0);
const handleSwitchActive = tab => {
  currentProductionIndex.value = tab;
}

const currentOperateIndex = ref(0);
const handleSwitchOperateActive = tab => {
  currentOperateIndex.value = tab;
}

onMounted(() => {

})
</script>
