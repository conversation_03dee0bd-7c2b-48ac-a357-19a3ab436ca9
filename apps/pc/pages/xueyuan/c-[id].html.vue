<template>
  <div class="news_detail">
    <div class="news_detail__nav">
      <a href="/" class="news_detail__nav--item">蔬东坡</a>
      <span class="add_margin">&gt;</span>
      <NuxtLink to="/xueyuan/" class="news_detail__nav--item">新闻报道</NuxtLink>
      <span v-if="category && category.slug !== undefined">
        <span class="add_margin">&gt;</span>
        <NuxtLink :to="`/xueyuan/${category?.slug}`" class="news_detail__nav--item">{{ category?.name }}</NuxtLink>
      </span>
      <span class="add_margin">&gt;</span>
      <span style="color: #333">正文</span>
    </div>

    <div class="news_detail-container">
      <div class="news_detail-content">
        <h1 class="news_detail__title">{{ articleInfo?.post_title || '' }}</h1>
        <div class="news_detail__date">发布时间：{{ articleInfo?.post_date_format || ''  }}</div>
        <pre class="news_detail__detail" style="white-space: pre-line">
          <div v-html="articleInfo?.post_content || ''"></div>
        </pre>
      </div>
      <div class="outer_container">
        <HotMessageList class="news_detail-hot"></HotMessageList>
        <HotTags class="hot_tags"></HotTags>
      </div>
    </div>

    <div class="newMessage-container">
      <div class="newMessage-container-left" v-if="newMessageList && newMessageList.length">
          <div class="newMessage-title">相关文章</div>
          <Message :info-list="newMessageList" :max-length="15" :is-double="false" class="message"/>
          <div class="indicator"></div>
       </div>
       <div class="newMessage-container-diver"></div>
      <div class="newMessage-container-right" v-if="lastMessageList && lastMessageList.length">
          <div class="newMessage-title">最新资讯</div>
          <Message :info-list="lastMessageList" :max-length="15" :is-double="false" class="message" />
          <div class="indicator"></div>
          <a class="newMessage-more" href="https://www.sdongpo.com/xueyuan/" target="_blank">查看更多 ></a>
       </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import Message from "../message.vue";

const route = useRoute();
const id = route.params.id;
let articleInfo = ref({});
let category = useState("category").value;
const res = await getNewsDetail(id);
if (res && res.status === 1) {
  res.data.seo_info &&
    useHead({
      title: res.data.seo_info.title,
      meta: [
        {
          name: "description",
          content: res.data.seo_info.description,
        },
        { name: "keywords", content: res.data.seo_info.keywords },
      ],
      script: [
        {
          src: "https://hm.baidu.com/hm.js?37f9e8255a5fe1f567b5720f6cba2d2c",
          bodyClose: false,
          async: true,
          defer: true,
        },
      ],
    });
  articleInfo.value = res.data;
  
}

// 相关文章
const newMessageList = ref([]);
const messageList = await getMessageList({
  id,
});
if (messageList) {
  newMessageList.value = messageList.data;
}

//最新资讯文章
const lastMessageList = ref([]);
const lastMessageListTemp = await getLastMessageList({
  max_id:id,
  per_page:10
});

if (lastMessageListTemp) {
  lastMessageList.value = lastMessageListTemp.data;
}

onBeforeMount(() => {
  const storeCate = localStorage.getItem("xueyuan-category");
  if (!category) category = storeCate ? JSON.parse(storeCate) : { name: "全部分类", slug: "" };
  // const scrollContainer = document.querySelector('#__nuxt')
  // scrollContainer.scroll(0, 550)
});
onMounted(() => {
  window.onbeforeunload = () => {
    if (category) localStorage.setItem("xueyuan-category", JSON.stringify(category));
  };
});
</script>

<style  scoped>
.news_detail {
  width: 1000px;
  margin: 0 auto;
  padding: 10px 0 50px;

  & &__nav {
    padding: 20px 0;
    color: #666;
    font: 18px PingFang SC-Regular, PingFang SC;
    border-bottom: 1px solid #ebebeb;
    .add_margin {
      margin: 0 10px;
    }
    &--item {
      cursor: pointer;
      &:hover {
        color: #2977fe;
      }
    }
  }

  &-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
  }

  &-content {
    width: 700px;
    margin-right: 30px;
  }

  &__title {
    margin: 30px 0;
    color: #2977fe;
    font-size: 32px;
  }
  &__date {
    color: #999;
    font-size: 14px;
    margin-top: -10px;
    margin-bottom: 30px;
  }
  &__detail {
    /deep/ div,
    p {
      margin: 10px 0;
      color: #666;
      font: 18px PingFang SC-Regular, PingFang SC;
      line-height: 33px;
    }
    /deep/ h3 {
      margin: 10px 0;
      color: #3c3c3c;
      font-size: 16px;
      font-weight: bold;
    }
    /deep/ img {
      max-width: 650px;
      margin: 10px auto;
    }
  }
  .hot_tags {
      width: 270px;
      margin-top: 20px;
    }
}
.newMessage {
  &-title {
    font-size: 24px;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #333333;
    margin: 0 auto 40px;
    text-align: center;
  }

  &-container {
    max-width: 1200px;
    margin: 100px 0 60px 0;
    position: relative;
    display: flex;
    &-diver {
      width: 150px;
    }
    &-left,&-right {
      min-width: 400px;
    }
  }

  &-more {
    font-size: 16px;
    position: absolute;
    bottom: -30px;
    right: 0;
  }
}
/deep/ .message .info-item {
  margin-left: 0px;
}
</style>
