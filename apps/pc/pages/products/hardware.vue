<template>
  <div class="processing">
    <header class="gxst_header">
      <div class="gxst_header__box">
        <h1 class="gxst_header__title">智能硬件</h1>
        <div class="gxst_header__detail">为生鲜、央厨企业提供云计算+物联网于一体的全流程解决方案</div>
        <SubmitButton class="button">获取专属方案</SubmitButton>
      </div>
    </header>
    <div class="processing-container">
      <div class="g-block" id="irpds">
        <div class="top">
          <h3 class="top_title">智能分拣称重</h3>
          <p class="top_titlep">
            支持触屏、网页、安卓端分拣，一键同步分拣重量、自动打印标签、按商品分拣、按客户分拣，分拣员任务分配、查看分拣进度、分拣员绩效考核。
          </p>
        </div>
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="~/assets/irpds/z1.png" alt="智能分拣称重" />
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-text desc-text1">
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="智能分拣称重" />
                </div>
                <div>
                  <span>
                    分拣员将商品放在分拣台，数据自动化记录及同步，自动打印标签，分拣员根据打印的标签信息投框，降低分拣错误率
                  </span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="智能分拣称重" />
                </div>
                <div>
                  <span>支持移动分拣、分拣更灵活，效率更高</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="智能分拣称重" />
                </div>
                <div>
                  <span>可以按客户、按商品查询分拣进度，避免分拣漏单分拣员绩效统计可实时查询，加强对分拣员的激励</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
        </div>
      </div>
      <div class="w-block" id="pesticide">
        <div class="top">
          <h3 class="top_title">农残检测设备</h3>
          <p class="top_titlep">
            配置安卓系统、4核处理器、高灵敏触屏、内置打印机、进口光源、USB接口、果蔬菜单库、多通道检测等多种功能。
          </p>
        </div>
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text desc-text2">
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="农残检测设备" />
                </div>
                <div>
                  <span>高智能农残检测仪，检测进入智能新时代</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="农残检测设备" />
                </div>
                <div>
                  <span>适用于水果、蔬菜、粮食、茶叶、农产等多种综合检测（有机磷和氨基甲酸脂）</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="农残检测设备" />
                </div>
                <div>
                  <span>可同时检测包括禽流感、猪瘟、农药残留等200项检测项目</span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="~/assets/irpds/z2.png" alt="农残检测设备" />
          </div>
        </div>
      </div>
      <div class="g-block" id="screen">
        <div class="top">
          <h3 class="top_title">BI数据大屏</h3>
          <p class="top_titlep">
            基于大数据的分析和整合，将生鲜配送企业内部项目数据进行链接，对企业进行全流程的即时分析、指挥、调动、管理，通过数据大屏，可以实时展现企业内部运营数据。
          </p>
        </div>
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-img margin50">
            <img loading="lazy" src="~/assets/irpds/z3.png" alt="BI数据大屏" />
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-text desc-text1">
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="BI数据大屏" />
                </div>
                <div>
                  <span>蔬东坡BI数据大屏，兼具科技、美感于一体</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt=">BI数据大屏" />
                </div>
                <div>
                  <span>
                    通过对客户分拣进度，商品分拣进度、运营数据、分类运营数据、商品贡献率等进行统计展示，能够准确而高效、精简而全面地传递企业运营情况，促使生鲜企业的决策从“业务驱动”转变为“数据驱动”，实时掌握决策风向标
                  </span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
        </div>
      </div>
      <div class="w-block" id="pda">
        <div class="top">
          <h3 class="top_title">库管PDA</h3>
          <p class="top_titlep">
            采购收货，移动收货、智能电子秤传重数据到移动端、移动手持PDA设备扫码收货，满足库房收货人员日常需求，减少重复录入工作，无纸化提升工作效率；入库审核，移动审核，智能电子秤传重数据到移动端、移动手持PDA设备扫码入库，同样也支持商品入库模式，效率翻倍；商品盘点，具备实时采集、移动手持PDA设备扫码盘点、自动存储、即时显示、即时反馈、自动传输功能，实现快速盘点；数据报表，查看库房的出入库趋势、今日入库数据、库存上下限预警、库房的损耗趋势等数据，实时掌握库房情况。
          </p>
        </div>
        <div class="desc-box">
          <div v-in-view-animate="['fly-up']" class="desc-text desc-text2">
            <div class="content">
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="库管PDA" />
                </div>
                <div>
                  <span>简单易用</span>
                  <span>系统操作基于移动端（手机）主流交互操作习惯，企业操作人员快速上手，简洁易用</span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="库管PDA" />
                </div>
                <div>
                  <span>方便快捷</span>
                  <span>
                    支持条码扫码操作，能快速查找，大幅提升工作效率，减少入库、盘点工作量，库房管理人员库管工作更轻松快捷
                  </span>
                </div>
              </div>
              <div class="text-box">
                <div class="img">
                  <img loading="lazy" src="~/assets/qsy/item_icon.png" alt="库管PDA" />
                </div>
                <div>
                  <span>数据准确</span>
                  <span>
                    设备可连接打印机和蔬东坡智能电子秤，称重数据一键传输至后台系统，提升库存数据准确率，增强企事业单位资产盘点数据采集准确度，步入仓储精细化管理
                  </span>
                </div>
              </div>
            </div>
            <SubmitButton>免费试用</SubmitButton>
          </div>
          <div v-in-view-animate="['fly-up']" class="desc-img">
            <img loading="lazy" src="~/assets/irpds/z4.png" alt="库管PDA" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import useInViewAnimate from '../../../pc/hooks/inViewAnimate';
import useAnchor from '../../../pc/hooks/useAnchor';
export default {
  setup() {
    useHead({
      title: '智能硬件',
      meta: [
        {
          name: 'description',
          content:
            '蔬东坡中央厨房管理系统,助力央厨业务管理数字升级,实现中央厨房生产集约化、操作智能化、生产高效化、食品绿色化、财账清晰化,提高央厨企业精细化管理能力',
        },
        { name: 'keywords', content: '中央厨房管理系统,中央厨房系统,中央厨房管理软件,净菜加工,央厨SaaS 软件' },
      ],
      script: [
        {
          src: 'https://hm.baidu.com/hm.js?d48f6da230615bef4b3765442ede582d',
          bodyClose: false,
          async: true,
          defer: true,
        },
      ],
    });
    useAnchor();
    onMounted(() => {});
    return {};
  },
  directives: {
    'in-view-animate': {
      mounted: useInViewAnimate,
    },
  },
};
</script>
<style lang="postcss" scoped>
.gxst_header {
  height: 620px;
  padding-top: 195px;
  background: url(~/assets/irpds/bg.png) no-repeat top/cover;
  color: #fff;

  &__box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-direction: column;
    height: 300px;
  }
  &__title {
    font-size: 56px;
    color: #ffffff;
  }
  &__detail {
    font-size: 32px;
  }

  &__desc {
    margin-top: 60px;
    font-weight: 400;
    font-size: 20px;
    font-weight: lighter;
  }

  &__img {
    margin-left: 70px;
    width: 420px;
    height: 320px;
  }
}

.processing {
  min-width: 1300px;

  .processing-banner {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #ffffff;
    background: url(~/assets/processing/banner.png) no-repeat center center;
    height: 694px;
    background-size: cover;

    .title {
      margin-top: 200px;
      font-weight: 400;
      font-size: 56px;
    }

    .tips1 {
      margin-top: 53px;
      font-weight: 400;
      font-size: 32px;
    }

    .mb76 {
      margin-bottom: 76px;
    }
  }
  .top {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    margin-top: 60px;
    flex-direction: column;
    .top_title {
      font-size: 20px;
      font-weight: 500;
      color: #333333;
      line-height: 14px;
    }
    .top_titlep {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-top: 20px;
      width: 760px;
      text-align: center;
    }
  }
  .processing-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .title {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 82px;
      font-weight: 500;
      font-size: 24px;
    }

    .underline {
      width: 142px;
      height: 3px;
      margin-top: 10px;
      background: #2977fe;
      border-radius: 5px;
    }

    .up-box {
      width: 100%;
      margin-top: 53px;
      margin-bottom: 90px;
      display: flex;
      align-items: center;
      justify-content: center;

      .up {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 220px;
        width: 220px;
        background: linear-gradient(180deg, #ffffff -32.39%, #f1f9ff 132.39%);
        box-shadow: 5px 5px 5px rgba(165, 181, 214, 0.2);
        border-radius: 5px;
        margin-right: 30px;
        font-weight: 400;
        font-size: 16px;
        color: #333333;

        span {
          margin-top: 8px;
        }

        .percent {
          margin-top: 12px;
          font-weight: 500;
          font-size: 24px;
          line-height: 24px;
        }

        img {
          width: 68px;
          height: 92px;
          flex-shrink: 0;
        }
      }
    }

    .desc-box {
      display: flex;
      justify-content: center;
      margin-top: 70px;
      margin-bottom: 70px;
      .desc-text1{
        padding-right: 120px;
        padding-top:50px;
      }
      .margin50{
        margin-top: -50px;
      }
      .desc-text2{
        padding-left: 120px;
      }
      .desc-text {
        color: #333333;
        width: 500px;
        margin-right: 30px;
        .content {
          font-size: 16px;
          font-weight: 400;

          .text-box {
            display: flex;
            margin-bottom: 30px;

            span {
              display: block;
            }
            .img {
              margin-top: 5px;
              margin-right: 5px;
              flex-shrink: 0;
              img {
                width: 15px;
                height: 15px;
              }
            }
          }

          .text-box:last-of-type {
            margin-bottom: 50px;
          }
        }
      }

      .desc-img {
        img {
          width: 600px;
          object-fit: cover;
        }
      }
    }
    .img-list-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 70px;
      padding-bottom: 150px;
      color: #333333;
      .img-list-title {
        text-align: center;
        margin-bottom: 10px;
        font-weight: 500;
        font-size: 24px;
      }
      .img-list-desc {
        text-align: center;
        color: #666666;
        font-size: 16px;
        font-weight: 400;
      }
      .img-list {
        margin-top: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        div {
          margin-right: 40px;
          width: 250px;
          position: relative;
          img {
            width: 250px;
            height: 300px;
          }
          p {
            position: absolute;
            color: #ffffff;
            width: 250px;
            bottom: 30px;
            left: 0;
            text-align: center;
          }
        }
      }
    }
  }

  .w-block {
    width: 100%;
    background: #ffffff;
  }

  .g-block {
    width: 100%;
    background: #f9fbff;
  }
  .advantage-bg {
    background-size: cover;
    background: url(~/assets/processing/advantage-bg.png) no-repeat center center;
    .advantage-title {
      color: #333333;
      font-size: 24px;
      font-weight: 500;
      text-align: center;
    }
  }

  .button {
    width: 200px;
    font-size: 20px;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-weight: 400;
    padding: 0;
  }
  .advantage-box {
    width: 100%;
    margin-top: 80px;
    margin-bottom: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    .advantage {
      color: #333333;
      font-size: 20px;
      font-weight: 400px;
      width: 230px;
      height: 250px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      margin-right: 30px;
      padding-top: 90px;
      .title2 {
        color: #333333;
        font-size: 20px;
        font-weight: 500;
      }
      p {
        margin-bottom: 30px;
      }
      .tips2 {
        width: 180px;
        text-align: center;
        font-size: 14px;
      }
      img {
        position: absolute;
        top: -30px;
        left: 50%;
        transform: translateX(-50%);
        width: 181px;
        height: 99px;
      }
    }
  }
  .tips3 {
    color: #666666;
    font-size: 24px;
    font-weight: 400;
    text-align: center;
  }
  .sidebar {
    &__title {
      color: #333333;
      font-size: 40px;
      padding-top: 86px;
      padding-bottom: 42px;
    }
  }
  .pb100 {
    padding-bottom: 100px;
  }

  @keyframes fly-up {
    0% {
      transform: translateY(100px);
      opacity: 0;
    }

    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  .fly-up {
    animation-name: fly-up;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  @keyframes scale-large {
    0% {
      transform: scale(0.2);
      opacity: 0;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .scale-large {
    animation-name: scale-large;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  @keyframes fade-in {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .fade-in {
    animation-name: fade-in;
    animation-fill-mode: both;
    animation-duration: 0.5s;
  }

  .animate-element {
    opacity: 0;
  }
}
</style>
