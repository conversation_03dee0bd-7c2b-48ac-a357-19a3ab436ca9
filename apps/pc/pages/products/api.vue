<style lang="postcss" scoped>
.api {
    &__banner {
        background-image: url('../../assets/products/api/banner.png');
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100%;
        height: 600px;
        .wrap {
            width: 1400px;
            margin:0 auto;
            padding-left: 165px;
            padding-top: 205px;
        }
        h1 {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            span {
                &:first-child {
                    font-size: 56px;
                    font-family: PingFang SC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #2977FE;
                    line-height: 66px;
                }
            }
        }
        h2 {
            margin-bottom: 60px;
            p {
                font-size: 20px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #999999;
                line-height: 23px;
                margin-bottom: 15px;
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    &__intro {
        display: flex;
        justify-content: center;
        padding: 70px 0 115px;
        &--items {
            width: 200px;
            position: relative;
            text-align: center;
            margin-right: 50px;
            &:last-child {
                margin-right: 0;
            }
            img {
                vertical-align: top;
                margin:0 auto 25px;
            }
            .name {
                font-size: 20px;
                font-family: PingFang SC-Medium, PingFang SC;
                font-weight: 500;
                color: #333333;
                line-height: 23px;
                margin-bottom: 10px;
            }
            .desc {
                font-size: 14px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                line-height: 30px;
            }
            &:after {
                content: '';
                position: absolute;
                left:0;
                top: 37px;
                width: 100%;
                height: 210px;
                background: linear-gradient(180deg, #FFFFFF 0%, #F1F9FF 100%);
                box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
                border-radius: 5px 5px 5px 5px;
                opacity: 1;
                z-index: -1;
            }
        }
    }
    &__case {
        &--items {
            display: flex;
            justify-content: center;
            padding-bottom: 50px;
            &:nth-child(even) {
                flex-direction: row-reverse;
            }
            &:nth-child(odd) {
                background: #F9FBFF;
            }
            &:nth-child(1) {
                > .content {
                    padding-top: 160px;
                    margin-left: 180px;
                    margin-right: 30px;
                }
            }
            &:nth-child(2) {
                > img {
                    margin-left: -200px;
                    margin-right: -80px;
                }
            }
            .content {
                padding-top: 100px;
                padding-left: 30px;
                .small-btn {
                    margin-top: 20px;
                }
                .items {
                    margin-bottom: 30px;
                    position: relative;
                    .icon {
                        position: absolute;
                        left:-30px;
                    }
                    .name {
                        font-size: 18px;
                        font-family: PingFang SC-Medium, PingFang SC;
                        font-weight: bold;
                        color: #333333;
                        line-height: 21px;
                        margin-bottom: 5px;
                    }
                    .desc {
                        font-size: 16px;
                        font-family: PingFang SC-Regular, PingFang SC;
                        font-weight: 400;
                        color: #333333;
                        line-height: 19px;
                    }
                }
            }
            > img {
                margin-top: 40px;
            }
        }
    }
    &__advantage {
        padding: 70px 0 215px;
        background-color:#F9FBFF;
        background-image:url('../../assets/products/api/plan_bg.png');
        background-repeat: no-repeat;
        background-position: right bottom;
        text-align: center;
        &--name {
            font-size: 24px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: bold;
            color: #333333;
            line-height: 28px;
            margin-bottom: 73px;
        }
        &--content {
            display: flex;
            justify-content: center;
            .items {
                position: relative;
                width: 220px;
                margin-right: 40px;
                &:last-child {
                    margin-right: 0;
                }
                img {
                    display: block;
                    margin:0 auto 22px;
                    z-index: 2;
                    position: relative;
                }
                .name {
                    font-size: 20px;
                    font-family: PingFang SC-Medium, PingFang SC;
                    font-weight: bold;
                    color: #333333;
                    line-height: 23px;
                    z-index: 2;
                    position: relative;
                }
                &:after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 22px;
                    width: 100%;
                    height: 210px;
                    background: #FFFFFF;
                    box-shadow: 1px 1px 2px 1px rgba(28,102,231,0.1);
                    border-radius: 5px 5px 5px 5px;
                    opacity: 1;
                }
            }
        }
    }
}
</style>
<template>
    <div class="api">
        <div class="api__banner">
            <div class="wrap">
                <h1>
                    <span>开放平台OpenAPI</span>
                </h1>
                <h2>
                    <p>基于生鲜食材供应链ERP管理系统的开放平台</p>
                    <p>可对接第三方系统，自研系统，自定义业务流程等</p>
                </h2>
                <SubmitButton class="small-btn">获取专属方案</SubmitButton>
            </div>
        </div>
        <div class="api__intro">
            <div class="api__intro--items">
                <img src="../../assets/products/api/img_1.png" alt="接口丰富" width="100" height="86" />
                <div class="name">接口丰富</div>
                <div class="desc">
                    <p>丰富接口帮助企业</p>
                    <p>满足个性化需求</p>
                </div>
            </div>
            <div class="api__intro--items">
                <img src="../../assets/products/api/img_2.png" alt="数据同步" width="100" height="86" />
                <div class="name">数据同步</div>
                <div class="desc">
                    <p>业务数据互通</p>
                    <p>保障业务流转闭环</p>
                </div>
            </div>
            <div class="api__intro--items">
                <img src="../../assets/products/api/img_3.png" alt="稳定维护" width="100" height="86" />
                <div class="name">稳定维护</div>
                <div class="desc">
                    <p>自动化重新启动</p>
                    <p>方便管理维护</p>
                </div>
            </div>
        </div>
        <div class="api__case">
            <div class="api__case--items">
                <div class="content">
                    <div class="items">
                        <img src="../../assets/products/api/icon.png" class="icon" alt="" />
                        <p class="name">企业ERP</p>
                        <p class="desc">订单自动转入，避免重复录入，减少人工错误<br/>实时查看库存，实时查看订单状态，降低<br/>沟通成本</p>
                    </div>
                    <div class="items">
                        <img src="../../assets/products/api/icon.png" class="icon" alt="" />
                        <p class="name">自研系统</p>
                        <p class="desc">丰富接口帮助企业满足个性化需求</p>
                    </div>
                    <SubmitButton class="small-btn" />
                </div>
                <img src="../../assets/products/api/case_2.png" alt="" />
            </div>
            <div class="api__case--items">
                <div class="content">
                    <div class="items">
                        <img src="../../assets/products/api/icon.png" class="icon" alt="" />
                        <p class="name">服务能力</p>
                        <p class="desc">以同步策略规范业务标准，实现业务数据互<br/>通，保障企业业务流转闭环</p>
                    </div>
                    <div class="items">
                        <img src="../../assets/products/api/icon.png" class="icon" alt="" />
                        <p class="name">开放接口</p>
                        <p class="desc">提供基础资料、录单、采购以及库房等相关业<br/>务几十种接口，支持对接企业自有系统</p>
                    </div>
                    <div class="items">
                        <img src="../../assets/products/api/icon.png" class="icon" alt="" />
                        <p class="name">稳定维护</p>
                        <p class="desc">提醒通知数据异常、同步数据失败以及原因，<br/>自动化重新启动，方便管理维护</p>
                    </div>
                    <SubmitButton class="small-btn" />
                </div>
                <img src="../../assets/products/api/case_1.png" alt="" />
            </div>
        </div>
        <div class="api__advantage">
            <h3 class="api__advantage--name">协作方案</h3>
            <div class="api__advantage--content">
                <div class="items">
                    <img src="../../assets/products/api/plan_1.png" alt="订单推送" width="135" height="150" />
                    <div class="name">订单推送</div>
                </div>
                <div class="items">
                    <img src="../../assets/products/api/plan_2.png" alt="单据回传" width="135" height="150" />
                    <div class="name">单据回传</div>
                </div>
                <div class="items">
                    <img src="../../assets/products/api/plan_3.png" alt="字段映射" width="135" height="150" />
                    <div class="name">字段映射</div>
                </div>
                <div class="items">
                    <img src="../../assets/products/api/plan_4.png" alt="接口新增查询" width="135" height="150" />
                    <div class="name">接口新增查询</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
useHead({
  title: '生鲜财务API_生鲜配送软件开发_蔬东坡生鲜配送软件API-蔬东坡',
  meta: [
    { name: 'description', content: '蔬东坡生鲜配送软件API,基于供应链生鲜ERP管理系统的开放平台,给蔬东坡客户提供对接第三方系统,现已与微信、支付宝、用友T+、金蝶K3等第三方服务平台对接,自定义业务流程等数据服务,帮助生鲜企业实现信息化管理,为企业降本增效。' },
    {
      name: 'keywords',
      content: '生鲜配送软件API,蔬东坡生鲜配送软件API,生鲜配送软件可打通财务软件吗',
    },
  ],
});
</script>
