.w1065 {
  width: 1065px;
  margin: 0 auto;
}
.w1000 {
  width: 1000px;
  margin: 0 auto;
}
.ds_header {
  height: 601px;
  padding-top: 189px;
  background: url(~/assets/delivery_sys/ds_header_bg.jpg) no-repeat center/cover;
  color: #fff;
  .small-btn {
    width: 220px;
    height: 50px;
    line-height: 50px;
    margin: 60px auto 0;
  }
  &__title {
    font-size: 56px;
    letter-spacing: 4px;
  }
  &__desc {
    margin-top: 35px;
    font-size: 32px;
    &__mini {
      margin-top: 28px;
      font-size: 18px;
    }
  }
}
.ds_content {
  margin-top: 82px;
  &__title {
    color: #333;
    font-size: 24px;
  }
  &__intro {
    margin-bottom: 70px;
    color: #333;
    .ds_content__title::after {
      content: '';
      display: block;
      margin: 10px auto 40px;
      width: 142px;
      height: 3px;
      background-color: #2977fe;
      border-radius: 5px;
    }
    .intro_item {
      width: 220px;
      height: 220px;
      padding: 20px 0;
      background: linear-gradient(180deg, #fff -32.39%, #f1f9ff 132.39%);
      box-shadow: 5px 5px 5px rgba(165, 181, 214, 0.2);
      border-radius: 5px;
      &__desc {
        margin-top: 15px;
        font-size: 16px;
        &.em {
          margin-top: 10px;
          font-size: 24px;
        }
      }
      &:first-child img {
        width: 71px;
      }
      &:nth-child(n+2) img {
        width: 62px;
      }
    }
  }
  &__main {
    .small-btn {
      width: 180px;
      margin: 20px 62px 0;
    }
    .sys_item {
      padding: 70px 0 80px;
      background: url(~/assets/delivery_sys/sys_info_bg.png) no-repeat 75% 5%/800px;
      &:nth-child(2n + 1) {
        background-color: #f9fbff;
      }
      &:nth-child(1) {
        background-position-y: 50%;
        img {
          width: 498px;
        }
      }
      &:nth-child(2) {
        .ds_content__title::before {
          background-image: url(~/assets/common/icon-caigou.png);
        }
        img {
          width: 470px;
        }
      }
      &:nth-child(3) {
        .ds_content__title::before {
          background-image: url(~/assets/common/icon-fenjian.png);
        }
        img {
          width: 479px;
        }
      }
      &:nth-child(4) {
        .ds_content__title::before {
          background-image: url(~/assets/common/icon-kucun.png);
        }
        img {
          width: 444px;
        }
      }
      &:nth-child(5) {
        .ds_content__title::before {
          background-image: url(~/assets/common/icon-paixian.png);
        }
        img {
          width: 472px;
        }
      }
      &:nth-child(6) {
        .ds_content__title::before {
          background-image: url(~/assets/common/icon-caiwu.png);
        }
        img {
          width: 484px;
        }
      }
      &_detail {
        padding-left: 38px;
        .ds_content__title {
          position: relative;
          margin: 0 0 30px 60px;
          &::before {
            content: '';
            position: absolute;
            top: 2px;
            left: -38px;
            display: inline-block;
            width: 34px;
            height: 34px;
            background: url(~/assets/common/icon-dingdan.png) no-repeat center/cover;
          }
        }
        .item_desc {
          position: relative;
          margin: 0 0 30px 63px;
          color: #333;
          font-size: 16px;
          &::before {
            content: '';
            position: absolute;
            top: 4px;
            left: -24px;
            display: inline-block;
            width: 15px;
            height: 15px;
            background: url(~/assets/qsy/item_icon.png) no-repeat center/cover;
          }
        }
      }
    }
  }
  &__core {
    padding: 72px 0 81px;
    .w {
      width: 1010px;
    }
    background: url(~/assets/delivery_sys/core_bg.png) no-repeat top right/contain #f9fbff;
    .ds_content__title {
      margin-bottom: 80px;
      font-size: 36px;
    }
    .core_item {
      position: relative;
      width: 230px;
      padding: 96px 0 24px;
      background-color: #fff;
      border-radius: 5px;
      &__title {
        color: #333;
        font-size: 20px;
      }
      &__desc {
        margin-top: 23px;
        color: #333;
        font-size: 14px;
        line-height: 216.5%;
      }
      &:last-child .core_item__desc {
        margin-top: 20px;
      }
      &__img {
        position: absolute;
        top: -50px;
        left: 0;
        right: 0;
        margin: auto;
        width: 100px;
      }
    }
  }
  &__apply {
    position: relative;
    padding: 70px 0;
    /* .ds_content__title {
      font-size: 40px;
    } */
    .ds_content__desc {
      margin: 10px 0 40px;
      color: #666;
      font-size: 16px;
    }
    &.w, .swiper {
      width: 860px;
    }
    .swiper-slide {
      width: 301px;
      height: 353px;
      .apply_img {
        margin: 0 auto;
        width: 301px;
      }
      .apply_desc {
        position: absolute;
        bottom: 30px;
        left: 0;
        right: 0;
        margin: 0 auto;
        /* width: 427px; */
        color: #fff;
        font-size: 20px;
      }
    }
    .swiper-button {
      top: 57%;
      width: 28px;
      height: 28px;
      &-prev {
        left: -18px;
      }
      &-next {
        right: -20px;
      }
    }
    &--btn {
      cursor: pointer;
      width: 138px;
      height: 40px;
      line-height: 40px;
      margin: 40px auto 0;
      color: #fff;
      font-size: 16px;
      background-color: #2977fe;
      border-radius: 5px;
    }
  }
}
.ds_sidebar {
  padding-bottom: 110px;
  background-color: #f9fbff;
  &__title {
    padding: 70px 0 37px;
    color: #333;
    font-size: 24px;
  }
  img {
    margin-right: 17px;
    width: 145px;
  }
  &__hidden {
    width: 100px;
    height: 100%;
    z-index: 1;
    &.left-0 {
      background-image: linear-gradient(to right, rgb(249, 251, 255), rgba(249, 251, 255, 0.5), rgba(0, 0, 0, 0));
    }
    &.right-0 {
      background-image: linear-gradient(to left, rgb(249, 251, 255), rgba(249, 251, 255, 0.5), rgba(0, 0, 0, 0));
    }
  }
}
.newMessage {
  &-title {
    font-size: 24px;
    font-family: PingFang SC-Medium, PingFang SC;
    color: #333333;
    margin: 0 auto 40px;
    text-align: center;
  }

  &-container {
    max-width: 1200px;
    margin: 0 auto 80px;
    position: relative;
  }

  &-more {
    position: absolute;
    bottom: -30px;
    font-size: 16px;
    right: 0;
  }
}
@keyframes scroll-horizon {
  0% {
    transform: translate(0);
  }
  to {
    transform: translate(calc(-8.4375vw * 6));
  }
}
@keyframes scroll-horizon2 {
  0% {
    transform: translate(0px);
  }
  to {
    transform: translate(calc(-8.4375vw * 7));
  }
}
.animation-scroll {
  margin-bottom: 17px;
  animation: scroll-horizon 30s linear infinite;
  &:nth-child(2) {
    margin-left: -81px;
    animation: scroll-horizon2 35s linear infinite;
  }
}
