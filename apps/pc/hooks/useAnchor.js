/*
 * @Description:
 * @Date: 2023-04-11 16:29:21
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-07-06 11:25:08
 * @FilePath: /shudongpo-website/apps/pc/hooks/useAnchor.js
 */
import { onMounted } from 'vue';

const useAnchor = () => {
  const scrollToAnchor = () => {
    const targetId = window.location.hash.substr(1);
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.scrollIntoView();
    }
  };

  onMounted(scrollToAnchor);
};

export default useAnchor;
