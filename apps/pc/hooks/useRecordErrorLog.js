// import winston from 'winston';

// const logger = winston.createLogger({
//   transports: [
//     new winston.transports.File({
//       filename: 'logs/error.log',
//       level: 'error',
//       format: winston.format.combine(
//         winston.format.timestamp({ format: 'YYYY-MM-DDTHH:mm:ss.SSSZ' }),
//         winston.format.json(),
//       )
//     })
//   ]
// });
const useRecordErrorLog = (error) => {
  console.error(error);
  // logger.error(error)
}

export default useRecordErrorLog