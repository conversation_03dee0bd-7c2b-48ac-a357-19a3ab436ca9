/*
 * @Description:
 * @Date: 2023-04-03 14:00:19
 * @LastEditors: hgj
 * @LastEditTime: 2023-04-03 14:19:42
 * @FilePath: /shudongpo-website/apps/pc/hooks/inViewAnimate.js
 */
import { onBeforeUnmount } from 'vue';

const useInViewAnimate = (el, binding) => {
  const options = {
    rootMargin: '100px',
    threshold: 0,
  };

  const observer = new IntersectionObserver(([entry]) => {
    if (entry.isIntersecting) {
      el.classList.add(...binding.value);
    }
  }, options);

  observer.observe(el);

  onBeforeUnmount(() => {
    observer.disconnect();
  });
};
export default useInViewAnimate;
