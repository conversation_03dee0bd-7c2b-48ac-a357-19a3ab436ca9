/*
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-28 10:05:28
 * @LastEditors: hgj
 * @LastEditTime: 2023-06-09 10:50:53
 * @FilePath: /shudongpo-website/apps/pc/nuxt.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// https://nuxt.com/docs/api/configuration/nuxt-config

import vitePluginAliOss from './vite-plugins/vite-plugin-ali-oss'
import { terser } from 'rollup-plugin-terser'
import { loadEnv } from 'vite'
const isProd = process.env.NODE_ENV === 'production'
const options = {
  region: 'oss-cn-beijing',
  accessKeyId: 'LTAI5tBm9T661p8qCL5cAEhM',
  accessKeySecret: '******************************',
  bucket: 'sdongpo-base-file',
  exclude: /\.(html|php|map)$/i
}
const proPlugins = [
  vitePluginAliOss(options),
  terser({
    compress: {
      drop_console: true, // 删除console.log语句
      drop_debugger: true // 删除debugger语句
    }
  })
]
const prodCdnUrl = 'https://base-oss.shudongpoo.com/static/'
const devPlugins = [{}]
interface VITE_ENV_CONFIG {
  VITE_PACK_URL: string
}
const envScript = (process.env as any).npm_lifecycle_script.split(' ')
const envName = envScript[envScript.length - 1]
const envData = loadEnv(envName, 'env') as unknown as VITE_ENV_CONFIG
export default {
 extends: ['../base'],
 modules: ['@nuxtjs/tailwindcss', '@element-plus/nuxt', 'nuxt-swiper', '@vueuse/nuxt'],
 serverMiddleware: [
  { path: '/wp-json/', handler: '~server/middleware/cache.ts' },
],
 postcss: {
   plugins: {
     'postcss-nested': {},
     'postcss-px-to-viewport': {
       unitToConvert: 'px',
       viewportWidth: 1920,
       unitPrecision: 5,
       propList: ['*', '!--van-*'],
       viewportUnit: 'vw',
       fontViewportUnit: 'vw',
       selectorBlackList: [/^\.vant-.*/],
       minPixelValue: 1,
       mediaQuery: false,
       replace: true,
       exclude: [],
       landscape: false,
       landscapeUnit: 'vw',
       landscapeWidth: 568,
     },
   },
 },

 nitro: {
   devProxy: {
     '/index/save': {
       target: 'http://sdp.com/index/save', // 这里是接口地址
       changeOrigin: true,
       prependPath: true,
     },
     '/index/scene-code': {
       target: 'https://www.sdongpo.com/index/scene-code', // 这里是接口地址
       changeOrigin: true,
       prependPath: true,
     },
     '/index/scene-code-result': {
       target: 'https://www.sdongpo.com/index/scene-code-result', // 这里是接口地址
       changeOrigin: true,
       prependPath: true,
     },
   },
   // 该配置用于服务端请求转发
   routeRules: {
     '/index/save': {
       proxy: 'http://sdp.com/index/save',
     },
   },
 },

 swiper: {
   modules: ['navigation', 'effect-coverflow'],
 },

 app: {
   cdnURL: isProd ? prodCdnUrl : ''
 },

 runtimeConfig: {
   public: envData // 把env放入这个里面，通过useRuntimeConfig获取
 },

 // router: {
 //   middleware: 'mobile-redirect'
 // },
 vite: {
   envDir: '~/env',
   plugins:  isProd ? proPlugins : devPlugins
 },

 compatibilityDate: '2024-10-18'
};