{"name": "pc", "private": true, "scripts": {"build": "nuxt build --mode test", "build-production": "nuxt build --mode production", "build-test": "nuxt build --mode test", "dev": "nuxt dev --port=3020 --mode dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@element-plus/nuxt": "^1.0.4", "@nuxtjs/tailwindcss": "^6.6.4", "@vueuse/core": "^9.13.0", "@vueuse/nuxt": "^9.13.0", "nuxt": "^3.3.1", "postcss-px-to-viewport-8-plugin": "^1.2.2", "rollup-plugin-terser": "^7.0.2"}, "dependencies": {"@nuxt/nitro": "^0.10.0", "@unhead/vue": "^1.0.0", "element-plus": "^2.3.1", "ioredis": "^5.5.0", "nuxt-swiper": "^1.0.1", "vite-plugin-ali-oss": "^1.0.8", "winston": "^3.8.2"}}