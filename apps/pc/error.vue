<!--
 * @Description: 错误处理页面
 * @Date: 2023-04-17 15:52:27
 * @LastEditors: hgj
 * @LastEditTime: 2023-04-19 15:47:49
 * @FilePath: /shudongpo-website/apps/pc/error.vue
-->
<template>
  <ErrorPage
  :errCode="error.statusCode"
  >

  </ErrorPage>
</template>
<script setup>
import useRecordErrorLog from './hooks/useRecordErrorLog'

const props = defineProps({
  error: Object
})
if(props.error && props.error.statusCode === 500) {
  console.log('error', props.error)
  useRecordErrorLog(props.error)
}

</script>