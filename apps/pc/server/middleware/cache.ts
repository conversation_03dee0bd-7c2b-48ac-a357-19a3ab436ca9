import Redis from 'ioredis';
import { useRuntimeConfig, eventHandler } from '#imports';

// 创建 Redis 实例
const config = useRuntimeConfig();
const redis = new Redis(Number(config.public.VITE_REDIS_PORT || 6379), config.public.VITE_REDIS_HOST, {
  password: config.public.VITE_REDIS_PASSWORD,
  db: Number(config.public.VITE_REDIS_DB || 0),
  connectTimeout: 50000,
  retryStrategy(times) {
    return Math.min(times * 50, 2000);
  },
});


// 生成缓存 Key
const generateCacheKey = (path: string, params: any) => {
  return `${path}?${new URLSearchParams(params).toString()}`;
};

const cacheMaxPageAndMaxPostId = (jsonData: any = {}) => {
  if (jsonData.data.total) {
    const maxPage = Math.max(1, Math.ceil(jsonData.data.total / 9))
    if (jsonData.data.list && jsonData.data.list.length) {
      const maxPostId = jsonData.data.list[0].ID
      // 将最大文章ID缓存24小时
      redis.set('max_post_id', maxPostId, 'EX', 60 * 24 * 60 );
      console.log('*******************缓存最大页数和最大文章ID*******************')
      console.log('maxPage', maxPage)
      console.log('maxPostId', maxPostId)
    }
    // 将最大页数缓存24小时
    redis.set('posts_max_page', maxPage, 'EX', 60 * 24 * 60 );
  }
}

// 使用 eventHandler 来定义中间件
export default eventHandler(async (event) => {
  const { path, query } = event;

  const postMaxPage = await redis.get('posts_max_page');
  const maxPostId = await redis.get('max_post_id');
  // 根据线上数据看的最小id
  const minPostId = 4079;

  console.log('path', path)
 
  /**
   * 如果超出缓存的最大页数或最大文章ID，认为是非法请求，返回404
   */
  if (path.startsWith('/xueyuan/page-')) {
    const pageRegex = /\/page-(\d+)/;
    const page = path.match(pageRegex)[1];
    // 如果page大于缓存的最大页数，则返回404
    if (page && postMaxPage && page - postMaxPage > 0) {
      event.res.statusCode = 404;
      event.res.end('Not Found');
    }
  }

  if (path.startsWith('/xueyuan/c-')) {
    const idRegex = /\/c-(\d+)/;
    const id = path.match(idRegex)[1];
    if (
      (id && maxPostId && id - maxPostId > 0) ||
      (id && id - minPostId < 0)
    ) {
      event.res.statusCode = 404;
      event.res.end('Not Found');
    }
  }

  // 判断请求路径是否以 /wp-json/ 开头
  if (path.startsWith('/wp-json/website/') || path.startsWith('/wp-json/wp/v2/posts')) {
    const cacheKey = generateCacheKey(path, query);

    // 尝试从 Redis 获取缓存
    const cachedData = await redis.get(cacheKey);
    if (cachedData) {
      if (path.includes('action=cache_max_page_and_max_post_id')) {
        const jsonData = JSON.parse(cachedData);
        cacheMaxPageAndMaxPostId(jsonData)
      }
      // 如果缓存命中，直接返回缓存内容
      event.res.setHeader('Content-Type', 'application/json');
      return event.res.end(cachedData);
    } else {
      // 如果缓存未命中，进行原始请求
      const response = await fetch(`https://www.sdongpo.com${path}`, { method: event.req.method });
      const data = await response.json();

      if (path.includes('action=cache_max_page_and_max_post_id')) {
        cacheMaxPageAndMaxPostId(data)
      }      
      // 将响应数据存入 Redis
      redis.set(cacheKey, JSON.stringify(data), 'EX', 60 * 24 * 60 ); // 设置24小时缓存过期时间

      // 返回原始数据
      event.res.setHeader('Content-Type', 'application/json');
      return event.res.end(JSON.stringify(data));
    }
  }

  // 如果是非 /wp-json/ 请求，交给 Nuxt 处理（不返回固定的 JSON）
  return; // 不做任何操作，交给 Nuxt 继续处理
});