<!--
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 12:30:34
 * @LastEditors: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @LastEditTime: 2023-04-03 19:14:47
 * @FilePath: \fudeyun-website\apps\base\components\TheForm.vue
-->
<template>
  <form
    class="clearfix form"
  >
    <div class="form-item">
      <div class="form-item__label">
        <span class="form-item__required">*</span>姓名
      </div>
      <div class="form-item__content">
        <input
          v-model="formData.username"
          type="text"
          :class="{error: !validate.username}"
          class="form-item__input"
          placeholder="请输入姓名"
          @blur="checkUsername"
          @focus="validate.username = true"
        >
      </div>
    </div>
    <div class="form-item">
      <div class="form-item__label">
        <span class="form-item__required">*</span>手机号
      </div>
      <div class="form-item__content">
        <input
          v-model="formData.phone"
          type="text"
          :class="{error: !validate.phone}"
          class="form-item__input"
          placeholder="请输入手机号"
          @blur="checkPhone"
          @focus="validate.phone = true"
        >
      </div>
    </div>
    <div class="form-item">
      <div class="form-item__label">
        <span
          v-show="currentModalType===ModalType.xsc"
          class="form-item__required"
        >*</span>企业名称
      </div>
      <div class="form-item__content">
        <input
          v-model="formData.companyName"
          type="text"
          class="form-item__input"
          placeholder="请输入企业名称"
        >
      </div>
    </div>
    <div
      v-if="props.needEmployee"
      class="form-item employee"
    >
      <div class="form-item__label">
        员工人数
      </div>
      <div class="form-item__content employee-num">
        <div
          v-for="(level, index) in levelList"
          :key="index"
          class="employee-num__item"
          :class="{active: level.value === formData.user_level}"
          @click="handleChangeUserLevel(level.value)"
        >
          <span for="employee-num--1">{{ level.label }}</span>
        </div>
      </div>
    </div>
    <div
      v-if="dataGramList.length > 0"
      class="form-item dataGram"
    >
      <div class="form-item__label">
        资料包
      </div>
      <div class="form-item__content datum-num">
        <div
          v-for="(gram, index) in dataGramList"
          :key="index"
          class="datum-num__item"
          :class="{active: gram.ID === activeGram.ID}"
          @click="handleChangeGram(gram)"
        >
          <span for="employee-num--1">{{ gram.post_title }}</span>
        </div>
      </div>
    </div>
  </form>
</template>
<script setup>
  const emit = defineEmits();
  const props = defineProps({
    needEmployee: {
      type: Boolean,
      default: true
    },
  });
  const formData = formDataObj;

  const isShowTips = computed(() => {
    return currentModalType.value !== ModalType.price
  })
  watch(formData, () => {
    emit('on-change', formData)
  }, { deep: true });

  const levelList = [
    {
      label: '0-20',
      value: 4
    },
    {
      label: '21-50',
      value: 3
    },
    {
      label: '51-100',
      value: 2
    },
    {
      label: '100以上',
      value: 1
    }
  ]
  const handleChangeUserLevel = (val) => {
    formData.user_level = val;
  };
  
  const handleChangeGram = (val) => {
    activeGram.value = val;
  };

  const validate = reactive({
    username: true,
    phone: true
  });
  const checkPhone = () => {
    validate.phone = phonePattern.test(formData.phone.trim());
  }
  const checkUsername = () => {
    validate.username = !!formData.username;
  }
  defineExpose({
    resetForm,
    validate() {
      const check = {
        valid: true,
        error: ''
      }
      if (!formData.username) {
        check.valid = false;
        check.error = '请填写姓名';
        return check;
      }
      if (!formData.phone) {
        check.valid = false;
        check.error = '请填写手机号';
        return check;
      }
      if (currentModalType === ModalType.xsc && !formData.companyName) {
        check.valid = false;
        check.error = '请填写企业名称';
        return check;
      }
      if (!validate.phone) {
        check.valid = false;
        check.error = '请填写正确手机号';
        return check;
      }
      return check;
    }
  });
</script>
<style scoped>
  .error {
    border-color: red;
  }
  .form {
    display: flex;
    flex-direction: column;
    //justify-content: space-between;
    height: 100%;
  }
  .form-item {
    display: flex;
    flex-direction: row;
    margin-bottom: 28px;
    justify-content: space-between;
    vertical-align: middle;
    width: 100%;
  }
  
  .employee {
    margin-bottom: 0;
  }
  .dataGram {
    .datum-num {
      display: flex;
      justify-content: flex-start;
      gap: 9px;
      font-size: 14px;
      color: #333333;
      flex-wrap: wrap;
      .datum-num__item {
        text-align: center;
        margin-bottom: 14px;
        cursor: pointer;
        height: 40px;
        padding: 0 10px;
        background: #FFFFFF;
        border-radius: 15px 15px 15px 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #2977FE;
        &.active {
          color: #fff;
          background-color: #2977FE;
          border: 2px solid #2977FE;
        }
      }
    }
  }
  
  .form-item__required {
    color: #ff3d15;
  }
  
  .form-item__label-tips {
    font-size: 12px;
    color: #b2b2b2;
  }
  
  .form-item__label {
    text-align: right;
    padding-top: 10px;
    width: 95px;
    color: #333333;
  }
  
  .form-item__content {
    padding-left: 16px;
    width: 100%;
  }
  
  .employee-num {
    display: flex;
    justify-content: flex-start;
    gap: 9px;
    font-size: 14px;
    color: #333333;
  }
  
  .employee-num__item {
    text-align: center;
    margin-bottom: 14px;
    cursor: pointer;
    width: 82px;
    height: 40px;
    background: #FFFFFF;
    border-radius: 15px 15px 15px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #2977FE;
  }
  
  .employee-num__item.active {
    color: #fff;
    background-color: #2977FE;
    border: 1px solid #2977FE;
  }
  
  .form-item__input {
    outline: 0;
    width: 100%;
    height: 46px;
    line-height: 50px;
    padding: 14px;
    border-radius: 15px 15px 15px 15px;
    opacity: 1;
    border: 2px solid #005DFF;
  }
</style>
