<!--
 * @Author: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @Date: 2023-03-17 12:30:34
 * @LastEditors: ddcoder zu<PERSON><PERSON><PERSON>@movee.cn
 * @LastEditTime: 2023-04-03 19:14:47
 * @FilePath: \fudeyun-website\apps\base\components\TheForm.vue
-->
<template>
  <form class="clearfix form" :class="size">
    <div class="form-item">
      <div class="form-item__label">
        <span class="form-item__required">*</span>姓名
      </div>
      <div class="form-item__content">
        <input v-model="formData.username" type="text" @blur="checkUsername" @focus="validate.username = true"
               :class="{error: !validate.username}" class="form-item__input" placeholder="请输入姓名" />
      </div>
    </div>
    <div class="form-item">
      <div class="form-item__label">
        <span class="form-item__required">*</span>手机号
      </div>
      <div class="form-item__content">
        <input v-model="formData.phone" type="text" @blur="checkPhone" @focus="validate.phone = true"
               :class="{error: !validate.phone}" class="form-item__input" placeholder="请输入手机号" />
      </div>
    </div>
    <div class="form-item">
      <div class="form-item__label">
        企业名称
        <div class="form-item__label-tips" v-if="isShowTips">(选填)</div>
      </div>
      <div class="form-item__content">
        <input v-model="formData.companyName" type="text" class="form-item__input" placeholder="请输入企业名称" />
      </div>
    </div>
    <div class="form-item">
      <div class="form-item__label">
        员工人数
        <div class="form-item__label-tips" v-if="isShowTips">(选填)</div>
      </div>
      <div class="form-item__content employee-num">
        <div class="employee-num__item" :class="{active: level.value === formData.user_level}"
             v-for="(level, index) in levelList" :key="index" @click="handleChangeUserLevel(level.value)">
          <span for="employee-num--1">{{ level.label }}</span>
        </div>
      </div>
    </div>
  </form>
</template>
<script setup>
const emit = defineEmits();
const formData = formDataObj;
const isShowTips = computed(() => {
  return currentModalType.value !== modalTypeList.value.price
})
watch(formData, () => {
  emit('on-change', formData)
}, { deep: true });
const levelList = [
  {
    label: '0-20',
    value: 4
  },
  {
    label: '21-50',
    value: 3
  },
  {
    label: '51-100',
    value: 2
  },
  {
    label: '100以上',
    value: 1
  }
]
const handleChangeUserLevel = (val) => {
  formData.user_level = val;
};

const validate = reactive({
  username: true,
  phone: true
});
const checkPhone = () => {
  validate.phone = phonePattern.test(formData.phone.trim());
}
const checkUsername = () => {
  validate.username = !!formData.username;
}
defineExpose({
  resetForm,
  validate() {
    const check = {
      valid: true,
      error: ''
    }
    if (!formData.username) {
      check.valid = false;
      check.error = '请填写姓名';
      return check;
    }
    if (!formData.phone) {
      check.valid = false;
      check.error = '请填写手机号';
      return check;
    }
    if (!validate.phone) {
      check.valid = false;
      check.error = '请填写正确手机号';
      return check;
    }
    return check;
  }
});
</script>
<style scoped>
.error {
  border-color: red;
}
</style>
