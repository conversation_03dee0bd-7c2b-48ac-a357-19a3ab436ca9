{"name": "my-nuxt-layer", "type": "module", "version": "0.0.1", "main": "./nuxt.config.ts", "scripts": {"dev": "nuxi dev .playground", "build": "nuxt build .playground", "generate": "nuxt generate .playground", "preview": "nuxt preview .playground", "lint": "eslint .", "postinstall": "nuxt prepare .playground"}, "devDependencies": {"@nuxt/eslint-config": "^0.1.1", "eslint": "^8.28.0", "nuxt": "^3.0.0", "typescript": "^4.9.3"}}