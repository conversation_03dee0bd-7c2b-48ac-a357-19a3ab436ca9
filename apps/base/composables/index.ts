
const setCookie = (name, value, expireDays = null) => {
  const exDate = new Date();
  if (expireDays) {
    exDate.setDate(exDate.getDate() + expireDays);
  }
  document.cookie = name + "=" + decodeURIComponent(value)
  + ((expireDays == null) ? "" : ";expires=" + exDate.toString());
}

export const setBaiduUrl = () => {
  const search = window.location.search;
  if (search && (search.includes('e_keywordid') || search.includes('bd_vid'))) {
    setCookie('from_baidu_url', window.location.href);
  } 
}
